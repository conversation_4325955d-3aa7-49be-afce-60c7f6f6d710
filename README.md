# ELCube Jimmer 建模方案

基于Jimmer ORM和Kotlin语言重新设计ELCube的数据模型，充分利用Kotlin的语言特性和Jimmer的强类型特性。

## 设计理念

### 1. 强类型建模
- 利用Jimmer的编译时代码生成，确保类型安全
- 使用Kotlin的null safety特性，减少空指针异常
- 通过sealed class和enum class提供更好的类型约束

### 2. 函数式编程
- 利用Kotlin的扩展函数和高阶函数
- 使用data class简化数据模型定义
- 通过inline函数提升性能

### 3. DSL设计
- 为单据配置提供Kotlin DSL
- 为查询条件提供类型安全的DSL
- 为业务规则配置提供声明式DSL

### 4. 协程支持
- 异步处理单据计算和数据同步
- 非阻塞的文档引擎操作
- 响应式的数据流处理

## 模块结构

```
elcube-jimmer-model/
├── src/main/kotlin/
│   ├── cn/nkpro/elcube/jimmer/
│   │   ├── model/           # 核心数据模型
│   │   │   ├── BaseEntity.kt        # 基础实体定义
│   │   │   ├── DocType.kt          # 单据类型模型
│   │   │   ├── Document.kt         # 单据实体模型
│   │   │   ├── DocCard.kt          # 卡片模型
│   │   │   ├── DocIndex.kt         # 索引模型
│   │   │   ├── Component.kt        # 组件模型
│   │   │   └── BusinessFlow.kt     # 业务流模型
│   │   ├── engine/          # 引擎层
│   │   │   ├── DocumentEngine.kt       # 文档引擎
│   │   │   ├── DocumentEngineImpl.kt   # 文档引擎实现
│   │   │   └── BusinessFlowEngine.kt   # 业务流引擎
│   │   ├── component/       # 组件系统
│   │   │   ├── CardComponent.kt    # 卡片组件接口
│   │   │   └── DefaultCards.kt     # 默认卡片实现
│   │   ├── dsl/            # DSL定义
│   │   │   ├── DocumentDsl.kt      # 单据DSL
│   │   │   └── BusinessFlowDsl.kt  # 业务流DSL
│   │   ├── extension/       # Kotlin扩展函数
│   │   │   ├── DocumentExtensions.kt      # 单据扩展
│   │   │   └── BusinessFlowExtensions.kt  # 业务流扩展
│   │   ├── repository/      # 数据访问层
│   │   ├── config/         # 配置类
│   │   └── example/        # 示例代码
│   │       ├── SalesOrderExample.kt           # 销售订单示例
│   │       └── SalesBusinessFlowExample.kt    # 销售业务流示例
│   └── resources/
├── src/test/kotlin/        # 测试代码
└── build.gradle.kts
```

## 核心特性

### 1. 类型安全的单据建模
- **强类型实体定义**：使用Jimmer接口定义实体，编译时生成类型安全的代码
- **关联关系管理**：通过@OneToMany、@ManyToOne等注解定义清晰的关联关系
- **继承体系设计**：BaseEntity、AuditableEntity、BusinessEntity提供分层的基础功能

### 2. 声明式的组件配置
- **组件接口抽象**：CardComponent接口定义统一的组件规范
- **泛型类型安全**：通过泛型确保数据类型和配置类型的一致性
- **注解驱动配置**：使用@Component、@Field等注解简化组件定义

### 3. 响应式的数据处理
- **协程支持**：所有引擎操作都支持suspend函数，提供非阻塞的异步处理
- **Flow数据流**：使用Kotlin Flow处理数据流，支持背压和响应式操作
- **扩展函数**：丰富的扩展函数提供便捷的数据操作方法

### 4. 强类型的查询DSL
- **类型安全查询**：编译时检查查询条件的类型正确性
- **链式调用**：流畅的API设计，支持方法链式调用
- **条件构建器**：灵活的查询条件构建，支持复杂查询场景

### 5. 协程驱动的异步处理
- **非阻塞操作**：所有数据库操作和业务逻辑都支持异步处理
- **并发安全**：通过协程实现高并发下的线程安全
- **性能优化**：减少线程阻塞，提升系统整体性能

### 6. 业务流程编排
- **可视化流程定义**：通过DSL定义复杂的业务流程
- **状态驱动执行**：基于单据状态变化驱动流程执行
- **异步流程处理**：支持长时间运行的业务流程
- **流程监控分析**：提供流程执行状态和性能分析

## 技术优势

### 相比原有Java实现的优势

1. **类型安全性**
   ```kotlin
   // Kotlin + Jimmer: 编译时类型检查
   val document: Document = documentEngine.getDocument(docId) ?: throw DocumentNotFoundException()

   // 原Java实现: 运行时可能出现类型错误
   DocHV doc = docEngine.detail(docId);
   if(doc == null) throw new RuntimeException("Document not found");
   ```

2. **空安全性**
   ```kotlin
   // Kotlin: 编译时空安全检查
   val partnerName: String? = document.partnerName
   val displayName = partnerName ?: "未知客户"

   // Java: 运行时空指针风险
   String partnerName = document.getPartnerName();
   String displayName = partnerName != null ? partnerName : "未知客户";
   ```

3. **DSL表达能力**
   ```kotlin
   // Kotlin DSL: 声明式配置
   val docType = documentType {
       docType = "SALES_ORDER"
       docName = "销售订单"

       state("DRAFT") {
           stateName = "草稿"
           isInitial = true
           transitionTo("CONFIRMED") {
               conditionExpr = "doc.amount > 0"
           }
       }
   }

   // Java: 命令式配置
   DocType docType = new DocType();
   docType.setDocType("SALES_ORDER");
   docType.setDocName("销售订单");
   // ... 大量setter调用
   ```

4. **协程异步处理**
   ```kotlin
   // Kotlin协程: 简洁的异步代码
   suspend fun processOrder(orderId: String) {
       val document = documentEngine.getDocument(orderId)
       val calculated = documentEngine.calculateDocument(document)
       documentEngine.updateDocument(calculated.docId) {
           state("PROCESSED")
       }
   }

   // Java: 复杂的异步处理
   CompletableFuture<Void> processOrder(String orderId) {
       return docEngine.detailAsync(orderId)
           .thenCompose(doc -> docEngine.calculateAsync(doc))
           .thenCompose(calculated -> docEngine.updateAsync(calculated.getDocId(), ...));
   }
   ```

5. **扩展函数**
   ```kotlin
   // Kotlin扩展函数: 为现有类型添加方法
   fun Document.isExpired(): Boolean = expireDate?.isBefore(LocalDateTime.now()) == true
   fun Document.getAgeInDays(): Long = Duration.between(createdTime, LocalDateTime.now()).toDays()

   // 使用
   if (document.isExpired()) {
       println("文档已过期 ${document.getAgeInDays()} 天")
   }
   ```

## 使用示例

### 1. 定义单据类型
```kotlin
val salesOrderType = documentType {
    docType = "SALES_ORDER"
    docName = "销售订单"

    state("DRAFT") {
        stateName = "草稿"
        isInitial = true
        transitionTo("CONFIRMED")
    }

    card("HEADER") {
        cardName = "订单抬头"
        componentName = "HeaderCard"
        field("customerName") {
            fieldName = "客户名称"
            required = true
        }
    }
}
```

### 2. 创建和操作单据
```kotlin
// 创建单据
val document = documentEngine.create("SALES_ORDER") {
    setFields(
        "customerName" to "测试客户",
        "orderDate" to LocalDateTime.now(),
        "totalAmount" to BigDecimal("1000.00")
    )
}

// 更新单据
documentEngine.update(document.docId) {
    setField("customerName", "更新后的客户")
    state("CONFIRMED", "客户确认订单")
}
```

### 3. 查询单据
```kotlin
val orders = documentEngine.find {
    docTypes = listOf("SALES_ORDER")
    states = listOf("CONFIRMED", "SHIPPED")
    where("totalAmount", QueryOperator.GE, BigDecimal("1000"))
    orderBy("orderDate", SortDirection.DESC)
    limit = 50
}.filterByPartner("CUSTOMER_001")
 .mapToSummary()
 .toList()
```

### 4. 自定义组件
```kotlin
@Component("CustomCard", "自定义卡片")
class CustomCard : AbstractCardComponent<CustomCard.Data, CustomCard.Config>(
    "CustomCard",
    CustomCard.Data::class,
    CustomCard.Config::class
) {

    data class Data(val value: String)
    data class Config(val template: String)

    override suspend fun calculate(
        context: DocumentContext,
        data: Data,
        config: Config,
        isTrigger: Boolean,
        options: Map<String, Any>
    ): Data {
        val result = context.evaluateExpression<String>(config.template)
        return data.copy(value = result ?: "")
    }
}
```

### 5. 业务流程定义
```kotlin
val salesFlow = businessFlow {
    flowCode = "SALES_PROCESS"
    flowName = "销售业务流程"

    startNode("START") {
        nodeName = "流程开始"
        position(100, 200)
    }

    documentNode("OPPORTUNITY", "SALES_OPPORTUNITY") {
        nodeName = "销售机会"
        position(300, 200)
        autoCreate("true")
    }

    documentNode("QUOTATION", "SALES_QUOTATION") {
        nodeName = "销售报价"
        position(500, 200)
        condition("opportunity.status == 'QUALIFIED'")
    }

    gatewayNode("DECISION") {
        nodeName = "客户决策"
        position(700, 200)
    }

    documentNode("SALES_ORDER", "SALES_ORDER") {
        nodeName = "销售订单"
        position(900, 100)
        condition("quotation.customerAccepted == true")
    }

    endNode("END") {
        nodeName = "流程完成"
        position(1100, 200)
    }

    // 定义连接关系
    connect("START", "OPPORTUNITY")
    connect("OPPORTUNITY", "QUOTATION")
    connect("QUOTATION", "DECISION")
    conditionalConnect("DECISION", "SALES_ORDER", "quotation.customerAccepted == true")
    conditionalConnect("DECISION", "END", "quotation.customerAccepted == false")
    connect("SALES_ORDER", "END")
}
```

### 6. 业务流程执行
```kotlin
// 启动业务流程
val flowInstance = businessFlowEngine.start("SALES_PROCESS", opportunityDocId) {
    instanceName = "销售流程-客户A"
    data(
        "priority" to "HIGH",
        "salesRep" to "张三",
        "region" to "华北"
    )
}

// 处理流程节点
businessFlowEngine.trigger(flowInstance.instanceCode, "QUOTATION") {
    data("quotationAmount" to BigDecimal("100000"))
}

// 完成节点
businessFlowEngine.complete(nodeInstanceCode, quotationDocId) {
    data("customerAccepted" to true)
}

// 查询流程状态
val status = businessFlowEngine.getFlowStatus(flowInstance.instanceCode)
println("流程进度: ${status.progress}%")
```

## 迁移指南

### 从原有Java实现迁移到Kotlin + Jimmer

1. **数据模型迁移**
   - 将原有的Entity类转换为Jimmer接口定义
   - 使用Kotlin的数据类替代Java的POJO
   - 利用Jimmer的关联关系注解简化关系映射

2. **业务逻辑迁移**
   - 将Service类转换为Kotlin，使用协程处理异步操作
   - 使用DSL替代原有的配置方式
   - 利用扩展函数增强现有类型的功能

3. **组件系统迁移**
   - 将原有的卡片组件转换为新的CardComponent接口实现
   - 使用泛型确保类型安全
   - 利用注解简化组件配置

4. **业务流迁移**
   - 将原有的业务流配置转换为新的DSL定义
   - 使用协程处理异步的流程执行
   - 利用强类型确保流程配置的正确性

5. **测试迁移**
   - 使用Kotlin的测试DSL编写更简洁的测试代码
   - 利用协程测试工具测试异步逻辑
   - 使用MockK等Kotlin友好的Mock框架

## 性能优化

1. **编译时优化**
   - Jimmer的编译时代码生成减少运行时反射
   - Kotlin的内联函数消除函数调用开销
   - 类型安全的查询避免运行时类型检查

2. **运行时优化**
   - 协程的轻量级线程模型提升并发性能
   - Flow的背压机制避免内存溢出
   - 扩展函数的零开销抽象

3. **内存优化**
   - Kotlin的值类型减少对象分配
   - Jimmer的懒加载机制减少不必要的数据加载
   - 协程的结构化并发避免内存泄漏

## 业务流特性详解

### 业务流 vs 工作流

ELCube的业务流与传统的工作流（Workflow）有本质区别：

| 特性 | 业务流（Business Flow） | 工作流（Workflow） |
|------|----------------------|-------------------|
| **设计理念** | 单据驱动，业务原子性 | 流程驱动，任务导向 |
| **生命周期** | 每个单据有独立生命周期 | 流程实例统一管理 |
| **耦合度** | 低耦合，单据可独立存在 | 高耦合，依赖流程定义 |
| **并发性** | 支持多并发、循环创建 | 通常线性执行 |
| **变更性** | 可随时调整，版本无关 | 变更影响运行实例 |
| **终点** | 多终点，分支汇入不同结果 | 通常单一终点 |

### 业务流核心优势

1. **业务原子性**：每个单据都是独立的业务原子，即使脱离流程也能独立存在
2. **低耦合设计**：流程变更不影响已创建的单据，支持业务的灵活调整
3. **多并发支持**：同一流程可以并发创建多个单据，支持复杂的业务场景
4. **状态驱动**：基于单据状态变化驱动流程执行，更符合业务实际
5. **可视化管理**：提供流程图设计和执行监控，业务人员易于理解

### 业务流执行模式

```kotlin
// 1. 事件驱动模式 - 单据状态变化触发流程
documentEngine.update(orderId) {
    state("CONFIRMED") // 状态变化自动触发下游流程
}

// 2. 条件触发模式 - 满足条件时自动创建下游单据
conditionalConnect("ORDER", "DELIVERY", "order.amount > 1000 && inventory.available")

// 3. 手动触发模式 - 人工干预决定流程走向
businessFlowEngine.trigger(instanceCode, "APPROVAL") {
    data("approvalResult" to "APPROVED")
}

// 4. 定时触发模式 - 延迟或定时执行
connect("PAYMENT", "REMINDER") {
    delay(1440) // 24小时后发送提醒
}
```

## 总结

这个基于Jimmer和Kotlin的建模方案充分利用了现代JVM语言的特性，提供了：

- **更高的类型安全性**：编译时检查，减少运行时错误
- **更好的开发体验**：DSL语法，扩展函数，协程支持
- **更强的表达能力**：声明式配置，函数式编程范式
- **更优的性能表现**：编译时优化，异步处理，零开销抽象
- **完整的业务流支持**：单据驱动的业务流程编排，支持复杂业务场景

相比原有的Java实现，这个方案在保持功能完整性的同时，显著提升了代码的可读性、可维护性和性能表现，特别是在业务流程管理方面提供了更加灵活和强大的能力。
