package cn.nkpro.elcube.jimmer.repository

import cn.nkpro.elcube.jimmer.engine.DocumentQuery
import cn.nkpro.elcube.jimmer.model.Document
import cn.nkpro.elcube.jimmer.model.DocumentHistory
import org.babyfish.jimmer.spring.repository.KRepository
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Repository

/**
 * 单据仓储接口
 */
@Repository
interface DocumentRepository : KRepository<Document, String> {
    
    /**
     * 根据单据ID查找
     */
    suspend fun findByDocId(docId: String): Document?
    
    /**
     * 根据单据类型和业务键查找
     */
    suspend fun findByDocTypeAndBusinessKey(docType: String, businessKey: String): Document?
    
    /**
     * 根据查询条件查找
     */
    suspend fun findByQuery(query: DocumentQuery): List<Document>
    
    /**
     * 根据单据ID删除
     */
    suspend fun deleteByDocId(docId: String): Int
    
    /**
     * 保存历史记录
     */
    suspend fun saveHistory(history: DocumentHistory): DocumentHistory
}

/**
 * 单据类型仓储接口
 */
@Repository
interface DocTypeRepository : KRepository<cn.nkpro.elcube.jimmer.model.DocType, String> {
    
    /**
     * 根据单据类型编码查找
     */
    suspend fun findByDocType(docType: String): cn.nkpro.elcube.jimmer.model.DocType?
    
    /**
     * 查找所有启用的单据类型
     */
    suspend fun findAllEnabled(): List<cn.nkpro.elcube.jimmer.model.DocType>
}

/**
 * 组件仓储接口
 */
@Repository
interface ComponentRepository : KRepository<cn.nkpro.elcube.jimmer.model.Component, String> {
    
    /**
     * 根据组件编码查找
     */
    suspend fun findByComponentCode(componentCode: String): cn.nkpro.elcube.jimmer.model.Component?
    
    /**
     * 查找所有启用的组件
     */
    suspend fun findAllEnabled(): List<cn.nkpro.elcube.jimmer.model.Component>
    
    /**
     * 根据组件类型查找
     */
    suspend fun findByComponentType(componentType: String): List<cn.nkpro.elcube.jimmer.model.Component>
}
