package cn.nkpro.elcube.jimmer.extension

import cn.nkpro.elcube.jimmer.engine.*
import cn.nkpro.elcube.jimmer.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import java.time.LocalDateTime
import java.time.Duration

/**
 * 业务流扩展函数
 */

/**
 * 业务流实例扩展函数
 */

/**
 * 检查流程是否正在运行
 */
fun BusinessFlowInstance.isRunning(): Boolean {
    return instanceStatus == FlowInstanceStatus.RUNNING.value
}

/**
 * 检查流程是否已完成
 */
fun BusinessFlowInstance.isCompleted(): Boolean {
    return instanceStatus == FlowInstanceStatus.COMPLETED.value
}

/**
 * 检查流程是否已暂停
 */
fun BusinessFlowInstance.isPaused(): Boolean {
    return instanceStatus == FlowInstanceStatus.PAUSED.value
}

/**
 * 检查流程是否已终止
 */
fun BusinessFlowInstance.isTerminated(): Boolean {
    return instanceStatus == FlowInstanceStatus.TERMINATED.value
}

/**
 * 检查流程是否有错误
 */
fun BusinessFlowInstance.hasError(): Boolean {
    return instanceStatus == FlowInstanceStatus.ERROR.value
}

/**
 * 获取流程运行时长
 */
fun BusinessFlowInstance.getDuration(): Duration? {
    return if (startTime != null) {
        val endTime = this.endTime ?: LocalDateTime.now()
        Duration.between(startTime, endTime)
    } else null
}

/**
 * 获取流程运行天数
 */
fun BusinessFlowInstance.getDurationInDays(): Long? {
    return getDuration()?.toDays()
}

/**
 * 获取当前运行的节点
 */
fun BusinessFlowInstance.getCurrentNodes(): List<BusinessFlowNodeInstance> {
    return nodeInstances.filter { it.nodeStatus == NodeInstanceStatus.RUNNING.value }
}

/**
 * 获取已完成的节点
 */
fun BusinessFlowInstance.getCompletedNodes(): List<BusinessFlowNodeInstance> {
    return nodeInstances.filter { it.nodeStatus == NodeInstanceStatus.COMPLETED.value }
}

/**
 * 获取失败的节点
 */
fun BusinessFlowInstance.getFailedNodes(): List<BusinessFlowNodeInstance> {
    return nodeInstances.filter { it.nodeStatus == NodeInstanceStatus.FAILED.value }
}

/**
 * 获取流程进度百分比
 */
fun BusinessFlowInstance.getProgressPercentage(): Int {
    val totalNodes = nodeInstances.size
    val completedNodes = getCompletedNodes().size
    return if (totalNodes > 0) {
        (completedNodes.toDouble() / totalNodes * 100).toInt()
    } else 0
}

/**
 * 节点实例扩展函数
 */

/**
 * 检查节点是否正在运行
 */
fun BusinessFlowNodeInstance.isRunning(): Boolean {
    return nodeStatus == NodeInstanceStatus.RUNNING.value
}

/**
 * 检查节点是否已完成
 */
fun BusinessFlowNodeInstance.isCompleted(): Boolean {
    return nodeStatus == NodeInstanceStatus.COMPLETED.value
}

/**
 * 检查节点是否失败
 */
fun BusinessFlowNodeInstance.isFailed(): Boolean {
    return nodeStatus == NodeInstanceStatus.FAILED.value
}

/**
 * 检查节点是否被跳过
 */
fun BusinessFlowNodeInstance.isSkipped(): Boolean {
    return nodeStatus == NodeInstanceStatus.SKIPPED.value
}

/**
 * 获取节点执行时长
 */
fun BusinessFlowNodeInstance.getExecutionDuration(): Duration? {
    return if (startTime != null && endTime != null) {
        Duration.between(startTime, endTime)
    } else null
}

/**
 * 获取节点等待时长
 */
fun BusinessFlowNodeInstance.getWaitingDuration(): Duration? {
    return if (startTime != null) {
        val currentTime = endTime ?: LocalDateTime.now()
        Duration.between(createdTime, startTime)
    } else {
        Duration.between(createdTime, LocalDateTime.now())
    }
}

/**
 * 业务流定义扩展函数
 */

/**
 * 获取开始节点
 */
fun BusinessFlow.getStartNodes(): List<BusinessFlowNode> {
    return nodes.filter { it.isStart }
}

/**
 * 获取结束节点
 */
fun BusinessFlow.getEndNodes(): List<BusinessFlowNode> {
    return nodes.filter { it.isEnd }
}

/**
 * 根据单据类型获取节点
 */
fun BusinessFlow.getNodesByDocType(docType: String): List<BusinessFlowNode> {
    return nodes.filter { it.docType?.docType == docType }
}

/**
 * 根据节点类型获取节点
 */
fun BusinessFlow.getNodesByType(nodeType: String): List<BusinessFlowNode> {
    return nodes.filter { it.nodeType == nodeType }
}

/**
 * 获取节点的前置节点
 */
fun BusinessFlowNode.getPreviousNodes(): List<BusinessFlowNode> {
    return incomingConnections.map { it.sourceNode }
}

/**
 * 获取节点的后续节点
 */
fun BusinessFlowNode.getNextNodes(): List<BusinessFlowNode> {
    return outgoingConnections.map { it.targetNode }
}

/**
 * 检查节点是否可以从指定节点到达
 */
fun BusinessFlowNode.isReachableFrom(sourceNode: BusinessFlowNode): Boolean {
    val visited = mutableSetOf<String>()
    
    fun dfs(current: BusinessFlowNode): Boolean {
        if (current.nodeCode == this.nodeCode) return true
        if (current.nodeCode in visited) return false
        
        visited.add(current.nodeCode)
        return current.getNextNodes().any { dfs(it) }
    }
    
    return dfs(sourceNode)
}

/**
 * 业务流引擎扩展函数
 */

/**
 * 批量启动流程
 */
suspend fun BusinessFlowEngine.startBatch(
    requests: List<Triple<String, String, Map<String, Any>?>>
): List<BusinessFlowInstance> {
    return requests.map { (flowCode, rootDocId, initData) ->
        startFlow(flowCode, rootDocId, null, initData)
    }
}

/**
 * 根据条件统计流程实例数量
 */
suspend fun BusinessFlowEngine.countFlowInstances(
    flowCodes: List<String>? = null,
    statuses: List<String>? = null,
    dateRange: DateRange? = null
): Long {
    return findFlowInstances(
        FlowInstanceQuery(
            flowCodes = flowCodes,
            statuses = statuses,
            startTimeRange = dateRange
        )
    ).toList().size.toLong()
}

/**
 * 获取流程的所有活跃实例
 */
suspend fun BusinessFlowEngine.getActiveInstances(flowCode: String): List<BusinessFlowInstance> {
    return findFlowInstances(
        FlowInstanceQuery(
            flowCodes = listOf(flowCode),
            statuses = listOf(
                FlowInstanceStatus.RUNNING.value,
                FlowInstanceStatus.PAUSED.value
            )
        )
    ).toList()
}

/**
 * 获取超时的流程实例
 */
suspend fun BusinessFlowEngine.getTimeoutInstances(
    timeoutHours: Long = 24
): List<BusinessFlowInstance> {
    val cutoffTime = LocalDateTime.now().minusHours(timeoutHours)
    return findFlowInstances(
        FlowInstanceQuery(
            statuses = listOf(FlowInstanceStatus.RUNNING.value),
            startTimeRange = DateRange(null, cutoffTime)
        )
    ).toList()
}

/**
 * Flow扩展函数
 */

/**
 * 过滤指定状态的流程实例
 */
fun Flow<BusinessFlowInstance>.filterByStatus(vararg statuses: String): Flow<BusinessFlowInstance> {
    return filter { it.instanceStatus in statuses }
}

/**
 * 过滤指定流程类型的实例
 */
fun Flow<BusinessFlowInstance>.filterByFlowCode(vararg flowCodes: String): Flow<BusinessFlowInstance> {
    return filter { it.businessFlow.flowCode in flowCodes }
}

/**
 * 过滤指定时间范围的实例
 */
fun Flow<BusinessFlowInstance>.filterByDateRange(
    start: LocalDateTime?,
    end: LocalDateTime?
): Flow<BusinessFlowInstance> {
    return filter { instance ->
        val startTime = instance.startTime
        when {
            start != null && end != null -> startTime != null && startTime.isAfter(start) && startTime.isBefore(end)
            start != null -> startTime != null && startTime.isAfter(start)
            end != null -> startTime != null && startTime.isBefore(end)
            else -> true
        }
    }
}

/**
 * 映射为流程摘要
 */
fun Flow<BusinessFlowInstance>.mapToSummary(): Flow<FlowInstanceSummary> {
    return map { instance ->
        FlowInstanceSummary(
            instanceCode = instance.instanceCode,
            instanceName = instance.instanceName,
            flowCode = instance.businessFlow.flowCode,
            flowName = instance.businessFlow.flowName,
            status = instance.instanceStatus,
            progress = instance.getProgressPercentage(),
            startTime = instance.startTime,
            endTime = instance.endTime,
            duration = instance.getDuration(),
            currentNodes = instance.getCurrentNodes().map { it.flowNode.nodeName },
            rootDocId = instance.rootDocId
        )
    }
}

/**
 * 数据类定义
 */

/**
 * 流程实例摘要
 */
data class FlowInstanceSummary(
    val instanceCode: String,
    val instanceName: String?,
    val flowCode: String,
    val flowName: String,
    val status: String,
    val progress: Int,
    val startTime: LocalDateTime?,
    val endTime: LocalDateTime?,
    val duration: Duration?,
    val currentNodes: List<String>,
    val rootDocId: String
)

/**
 * 流程性能统计
 */
data class FlowPerformanceStats(
    val flowCode: String,
    val totalInstances: Long,
    val completedInstances: Long,
    val failedInstances: Long,
    val averageDuration: Duration?,
    val minDuration: Duration?,
    val maxDuration: Duration?,
    val successRate: Double
)

/**
 * 节点性能统计
 */
data class NodePerformanceStats(
    val nodeCode: String,
    val nodeName: String,
    val totalExecutions: Long,
    val successfulExecutions: Long,
    val failedExecutions: Long,
    val averageExecutionTime: Duration?,
    val averageWaitingTime: Duration?,
    val successRate: Double
)

/**
 * 工具函数
 */

/**
 * 计算流程性能统计
 */
suspend fun BusinessFlowEngine.calculateFlowPerformanceStats(
    flowCode: String,
    dateRange: DateRange? = null
): FlowPerformanceStats {
    val instances = findFlowInstances(
        FlowInstanceQuery(
            flowCodes = listOf(flowCode),
            startTimeRange = dateRange
        )
    ).toList()
    
    val completed = instances.filter { it.isCompleted() }
    val failed = instances.filter { it.hasError() || it.isTerminated() }
    
    val durations = completed.mapNotNull { it.getDuration() }
    val averageDuration = if (durations.isNotEmpty()) {
        Duration.ofMillis(durations.map { it.toMillis() }.average().toLong())
    } else null
    
    return FlowPerformanceStats(
        flowCode = flowCode,
        totalInstances = instances.size.toLong(),
        completedInstances = completed.size.toLong(),
        failedInstances = failed.size.toLong(),
        averageDuration = averageDuration,
        minDuration = durations.minOrNull(),
        maxDuration = durations.maxOrNull(),
        successRate = if (instances.isNotEmpty()) {
            completed.size.toDouble() / instances.size
        } else 0.0
    )
}

/**
 * 计算节点性能统计
 */
fun List<BusinessFlowNodeInstance>.calculateNodePerformanceStats(): Map<String, NodePerformanceStats> {
    return groupBy { it.flowNode.nodeCode }.mapValues { (nodeCode, nodeInstances) ->
        val successful = nodeInstances.filter { it.isCompleted() }
        val failed = nodeInstances.filter { it.isFailed() }
        
        val executionTimes = successful.mapNotNull { it.getExecutionDuration() }
        val waitingTimes = nodeInstances.mapNotNull { it.getWaitingDuration() }
        
        val averageExecutionTime = if (executionTimes.isNotEmpty()) {
            Duration.ofMillis(executionTimes.map { it.toMillis() }.average().toLong())
        } else null
        
        val averageWaitingTime = if (waitingTimes.isNotEmpty()) {
            Duration.ofMillis(waitingTimes.map { it.toMillis() }.average().toLong())
        } else null
        
        NodePerformanceStats(
            nodeCode = nodeCode,
            nodeName = nodeInstances.first().flowNode.nodeName,
            totalExecutions = nodeInstances.size.toLong(),
            successfulExecutions = successful.size.toLong(),
            failedExecutions = failed.size.toLong(),
            averageExecutionTime = averageExecutionTime,
            averageWaitingTime = averageWaitingTime,
            successRate = if (nodeInstances.isNotEmpty()) {
                successful.size.toDouble() / nodeInstances.size
            } else 0.0
        )
    }
}
