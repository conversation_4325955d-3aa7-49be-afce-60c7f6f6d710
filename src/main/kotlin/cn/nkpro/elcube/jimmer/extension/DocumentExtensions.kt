package cn.nkpro.elcube.jimmer.extension

import cn.nkpro.elcube.jimmer.engine.DocumentContext
import cn.nkpro.elcube.jimmer.engine.DocumentEngine
import cn.nkpro.elcube.jimmer.model.Document
import cn.nkpro.elcube.jimmer.model.DocCard
import cn.nkpro.elcube.jimmer.model.DocState
import cn.nkpro.elcube.jimmer.model.DocType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 单据扩展函数
 */

/**
 * 检查单据是否处于指定状态
 */
fun Document.isInState(stateCode: String): Boolean {
    return currentState?.stateCode == stateCode
}

/**
 * 检查单据是否处于任一指定状态
 */
fun Document.isInAnyState(vararg stateCodes: String): Boolean {
    return currentState?.stateCode in stateCodes
}

/**
 * 检查单据是否为初始状态
 */
fun Document.isInitialState(): Boolean {
    return currentState?.isInitial == true
}

/**
 * 检查单据是否为终止状态
 */
fun Document.isFinalState(): Boolean {
    return currentState?.isFinal == true
}

/**
 * 获取单据的年龄（天数）
 */
fun Document.getAgeInDays(): Long {
    return java.time.Duration.between(createdTime, LocalDateTime.now()).toDays()
}

/**
 * 检查单据是否过期
 */
fun Document.isExpired(): Boolean {
    return expireDate?.isBefore(LocalDateTime.now()) == true
}

/**
 * 获取单据的有效期剩余天数
 */
fun Document.getRemainingDays(): Long? {
    return expireDate?.let { 
        java.time.Duration.between(LocalDateTime.now(), it).toDays()
    }
}

/**
 * 单据类型扩展函数
 */

/**
 * 获取初始状态
 */
fun DocType.getInitialState(): DocState? {
    return states.find { it.isInitial }
}

/**
 * 获取终止状态列表
 */
fun DocType.getFinalStates(): List<DocState> {
    return states.filter { it.isFinal }
}

/**
 * 根据位置获取卡片
 */
fun DocType.getCardsByPosition(position: String): List<DocCard> {
    return cards.filter { it.cardPosition == position && it.enabled }
}

/**
 * 根据类型获取卡片
 */
fun DocType.getCardsByType(cardType: String): List<DocCard> {
    return cards.filter { it.cardType == cardType && it.enabled }
}

/**
 * 状态扩展函数
 */

/**
 * 检查是否可以转换到目标状态
 */
fun DocState.canTransitionTo(targetState: String): Boolean {
    return transitions.any { it.toState.stateCode == targetState }
}

/**
 * 获取可转换的目标状态
 */
fun DocState.getAvailableTransitions(): List<DocState> {
    return transitions.map { it.toState }
}

/**
 * 文档引擎扩展函数
 */

/**
 * 批量创建单据
 */
suspend fun DocumentEngine.createBatch(
    requests: List<Triple<String, String?, suspend DocumentContext.() -> Unit>>
): List<Document> {
    return requests.map { (docType, preDocId, init) ->
        createDocument(docType, preDocId, "BATCH", init)
    }
}

/**
 * 根据条件统计单据数量
 */
suspend fun DocumentEngine.countDocuments(
    docTypes: List<String>? = null,
    states: List<String>? = null,
    partnerId: String? = null
): Long {
    return findDocuments(
        cn.nkpro.elcube.jimmer.engine.DocumentQuery(
            docTypes = docTypes,
            states = states,
            partnerId = partnerId
        )
    ).toList().size.toLong()
}

/**
 * 获取单据的前置单据链
 */
suspend fun DocumentEngine.getDocumentChain(docId: String): List<Document> {
    val chain = mutableListOf<Document>()
    var current = getDocument(docId)
    
    while (current != null) {
        chain.add(0, current) // 添加到链的开头
        current = current.preDocId?.let { getDocument(it) }
    }
    
    return chain
}

/**
 * 获取单据的后续单据树
 */
suspend fun DocumentEngine.getDocumentTree(docId: String): DocumentTree {
    val root = getDocument(docId) ?: throw IllegalArgumentException("单据不存在: $docId")
    
    suspend fun buildTree(doc: Document): DocumentTree {
        val children = doc.nextDocuments.map { buildTree(it) }
        return DocumentTree(doc, children)
    }
    
    return buildTree(root)
}

/**
 * Flow扩展函数
 */

/**
 * 过滤指定状态的单据
 */
fun Flow<Document>.filterByState(vararg stateCodes: String): Flow<Document> {
    return filter { it.currentState?.stateCode in stateCodes }
}

/**
 * 过滤指定交易伙伴的单据
 */
fun Flow<Document>.filterByPartner(partnerId: String): Flow<Document> {
    return filter { it.partnerId == partnerId }
}

/**
 * 过滤指定日期范围的单据
 */
fun Flow<Document>.filterByDateRange(
    start: LocalDateTime?,
    end: LocalDateTime?
): Flow<Document> {
    return filter { doc ->
        val docDate = doc.docDate
        when {
            start != null && end != null -> docDate != null && docDate.isAfter(start) && docDate.isBefore(end)
            start != null -> docDate != null && docDate.isAfter(start)
            end != null -> docDate != null && docDate.isBefore(end)
            else -> true
        }
    }
}

/**
 * 过滤指定金额范围的单据
 */
fun Flow<Document>.filterByAmountRange(
    minAmount: BigDecimal?,
    maxAmount: BigDecimal?
): Flow<Document> {
    return filter { doc ->
        val amount = doc.docAmount
        when {
            minAmount != null && maxAmount != null -> amount != null && amount >= minAmount && amount <= maxAmount
            minAmount != null -> amount != null && amount >= minAmount
            maxAmount != null -> amount != null && amount <= maxAmount
            else -> true
        }
    }
}

/**
 * 映射为单据摘要
 */
fun Flow<Document>.mapToSummary(): Flow<DocumentSummary> {
    return map { doc ->
        DocumentSummary(
            docId = doc.docId,
            docNo = doc.docNo,
            docName = doc.docName,
            docType = doc.docType.docName,
            currentState = doc.currentState?.stateName,
            partnerName = doc.partnerName,
            docAmount = doc.docAmount,
            currency = doc.currency,
            docDate = doc.docDate,
            createdTime = doc.createdTime
        )
    }
}

/**
 * 数据类定义
 */

/**
 * 单据树结构
 */
data class DocumentTree(
    val document: Document,
    val children: List<DocumentTree> = emptyList()
) {
    /**
     * 获取树的深度
     */
    fun getDepth(): Int {
        return if (children.isEmpty()) 1 else 1 + children.maxOf { it.getDepth() }
    }
    
    /**
     * 获取所有叶子节点
     */
    fun getLeafNodes(): List<Document> {
        return if (children.isEmpty()) {
            listOf(document)
        } else {
            children.flatMap { it.getLeafNodes() }
        }
    }
    
    /**
     * 遍历所有节点
     */
    fun traverse(): List<Document> {
        return listOf(document) + children.flatMap { it.traverse() }
    }
}

/**
 * 单据摘要
 */
data class DocumentSummary(
    val docId: String,
    val docNo: String?,
    val docName: String?,
    val docType: String,
    val currentState: String?,
    val partnerName: String?,
    val docAmount: BigDecimal?,
    val currency: String?,
    val docDate: LocalDateTime?,
    val createdTime: LocalDateTime
)

/**
 * 工具函数
 */

/**
 * 安全的字符串转BigDecimal
 */
fun String?.toBigDecimalOrNull(): BigDecimal? {
    return try {
        this?.let { BigDecimal(it) }
    } catch (e: NumberFormatException) {
        null
    }
}

/**
 * 安全的字符串转LocalDateTime
 */
fun String?.toLocalDateTimeOrNull(): LocalDateTime? {
    return try {
        this?.let { LocalDateTime.parse(it) }
    } catch (e: Exception) {
        null
    }
}
