package cn.nkpro.elcube.jimmer.schema.model
import cn.nkpro.elcube.jimmer.schema.page.SchemaChain
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1

/**
 * 单据链接定义DSL - 类型安全版本
 */
@SchemaChain
class Link {

    var linkName: String = ""
    var description: String = ""
    var enabled: Boolean = true

    val linkRules = mutableListOf<LinkRuleDsl<*, *>>()

    /**
     * 定义链接规则 - 类型安全版本
     */
    inline fun <reified S : Any, reified T : Any> rule(
        name: String,
        sourceType: KClass<S>,
        targetType: KClass<T>,
        noinline init: LinkRuleDsl<S, T>.() -> Unit
    ) {
        val ruleDsl = LinkRuleDsl<S, T>().apply {
            ruleName = name
            this.sourceType = sourceType
            this.targetType = targetType
            init()
        }
        linkRules.add(ruleDsl)
    }

    /**
     * 获取所有链接规则（用于关联关系解析）
     */
    fun getLinkRules(): List<LinkRuleDsl<*, *>> {
        return linkRules.toList()
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "linkName" to linkName,
            "description" to description,
            "enabled" to enabled,
            "linkRules" to linkRules.map { it.build() }
        )
    }
}

/**
 * 链接规则DSL - 类型安全版本
 */
@SchemaChain
class LinkRuleDsl<S : Any, T : Any> {

    var ruleName: String = ""
    var description: String = ""
    var enabled: Boolean = true
    var order: Int = 0

    /**
     * 来源单据类型
     */
    lateinit var sourceType: KClass<S>

    /**
     * 目标单据类型
     */
    lateinit var targetType: KClass<T>

    /**
     * 来源单据状态条件（类型安全）
     */
    var sourceStatusCondition: ((S) -> Boolean)? = null

    /**
     * 生成条件（类型安全）
     */
    var generateCondition: ((S) -> Boolean)? = null

    /**
     * 是否允许重复生成
     */
    var allowDuplicate: Boolean = false

    /**
     * 生成模式
     */
    var generateMode: String = "MANUAL"  // MANUAL, AUTO, BATCH

    /**
     * 生成处理器
     */
    var handler: String = ""

    private val fieldMappings = mutableListOf<TypeSafeFieldMappingDsl<S, T>>()
    private val validations = mutableListOf<TypeSafeLinkValidationDsl<S, T>>()

    /**
     * 定义字段映射 - 类型安全版本
     */
    fun <R> fieldMapping(
        sourceField: KProperty1<S, R>,
        targetField: KProperty1<T, R>,
        init: TypeSafeFieldMappingDsl<S, T>.() -> Unit = {}
    ) {
        val mappingDsl = TypeSafeFieldMappingDsl<S, T>().apply {
            this.sourceField = sourceField
            this.targetField = targetField
            init()
        }
        fieldMappings.add(mappingDsl)
    }

    /**
     * 定义字段映射 - 支持类型转换
     */
    fun <R, V> fieldMapping(
        sourceField: KProperty1<S, R>,
        targetField: KProperty1<T, V>,
        transformer: (R) -> V,
        init: TypeSafeFieldMappingDsl<S, T>.() -> Unit = {}
    ) {
        val mappingDsl = TypeSafeFieldMappingDsl<S, T>().apply {
            this.sourceField = sourceField
            this.targetField = targetField
            this.transformer = { source -> transformer(sourceField.get(source)) }
            init()
        }
        fieldMappings.add(mappingDsl)
    }

    /**
     * 定义验证规则 - 类型安全版本
     */
    fun validation(name: String, init: TypeSafeLinkValidationDsl<S, T>.() -> Unit) {
        val validationDsl = TypeSafeLinkValidationDsl<S, T>().apply {
            validationName = name
            init()
        }
        validations.add(validationDsl)
    }

    /**
     * 设置来源状态条件 - 类型安全版本
     */
    fun fromStatus(condition: (S) -> Boolean) {
        sourceStatusCondition = condition
    }

    /**
     * 设置生成条件 - 类型安全版本
     */
    fun condition(predicate: (S) -> Boolean) {
        generateCondition = predicate
    }

    /**
     * 设置自动生成
     */
    fun autoGenerate() {
        generateMode = "AUTO"
    }

    /**
     * 设置手动生成
     */
    fun manualGenerate() {
        generateMode = "MANUAL"
    }

    /**
     * 设置批量生成
     */
    fun batchGenerate() {
        generateMode = "BATCH"
    }

    /**
     * 获取字段映射信息（用于关联关系解析）
     */
    fun getFieldMappings(): List<TypeSafeFieldMappingDsl<S, T>> {
        return fieldMappings.toList()
    }

    /**
     * 获取验证规则信息（用于关联关系解析）
     */
    fun getValidations(): List<TypeSafeLinkValidationDsl<S, T>> {
        return validations.toList()
    }

    /**
     * 获取条件信息（用于关联关系解析）
     */
    fun getConditions(): List<String> {
        val conditions = mutableListOf<String>()
        sourceStatusCondition?.let { conditions.add("sourceStatusCondition") }
        generateCondition?.let { conditions.add("generateCondition") }
        return conditions
    }

    fun build(): LinkRuleDsl<S,T> {
        return this
    }
}

/**
 * 类型安全的字段映射DSL
 */
@SchemaChain
class TypeSafeFieldMappingDsl<S : Any, T : Any> {

    var sourceField: KProperty1<S, *>? = null
    var targetField: KProperty1<T, *>? = null
    var mappingType: String = "DIRECT"  // DIRECT, TRANSFORM, CALCULATE
    var transformer: ((S) -> Any?)? = null
    var defaultValue: Any? = null
    var required: Boolean = false

    /**
     * 直接映射
     */
    fun direct() {
        mappingType = "DIRECT"
    }

    /**
     * 转换映射 - 使用lambda
     */
    fun transform(transformerFn: (S) -> Any?) {
        mappingType = "TRANSFORM"
        transformer = transformerFn
    }

    /**
     * 计算映射 - 基于源对象计算
     */
    fun calculate(calculatorFn: (S) -> Any?) {
        mappingType = "CALCULATE"
        transformer = calculatorFn
    }

    /**
     * 设置默认值
     */
    fun default(value: Any?) {
        defaultValue = value
    }

    fun build(): TypeSafeFieldMappingDsl<S , T > {
        return this
    }
}

/**
 * 类型安全的链接验证DSL
 */
@SchemaChain
class TypeSafeLinkValidationDsl<S : Any, T : Any> {

    var validationName: String = ""
    var validationType: String = "CONDITION"  // CONDITION, UNIQUE, CUSTOM
    var validationCondition: ((S) -> Boolean)? = null
    var uniqueFields: List<KProperty1<T, *>> = emptyList()
    var customValidator: ((S, T) -> Boolean)? = null
    var message: String = ""
    var severity: String = "ERROR"  // ERROR, WARNING, INFO
    var enabled: Boolean = true

    /**
     * 条件验证 - 类型安全
     */
    fun condition(predicate: (S) -> Boolean, msg: String = "") {
        validationType = "CONDITION"
        validationCondition = predicate
        message = msg
    }

    /**
     * 唯一性验证 - 类型安全
     */
    fun unique(vararg fields: KProperty1<T, *>, msg: String = "") {
        validationType = "UNIQUE"
        uniqueFields = fields.toList()
        message = msg
    }

    /**
     * 自定义验证 - 类型安全
     */
    fun custom(validator: (S, T) -> Boolean, msg: String = "") {
        validationType = "CUSTOM"
        customValidator = validator
        message = msg
    }

    /**
     * 设置为警告
     */
    fun warning() {
        severity = "WARNING"
    }

    /**
     * 设置为信息
     */
    fun info() {
        severity = "INFO"
    }

    fun build(): TypeSafeLinkValidationDsl<S,T> {
        return this
    }
}


abstract class AbstractLinkDefinition {

    /**
     * 定义链接规则
     */
    abstract fun defineLink(): Link.() -> Unit

    /**
     * 构建链接定义
     */
    fun buildLinkDefinition(): Map<String, Any> {
        val link = Link().apply {
            defineLink().invoke(this)
        }
        return link.build()
    }
}

