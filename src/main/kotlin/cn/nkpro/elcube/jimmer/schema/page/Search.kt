package cn.nkpro.elcube.jimmer.schema.page

import cn.nkpro.elcube.jimmer.schema.Action
import cn.nkpro.elcube.jimmer.schema.Column
import cn.nkpro.elcube.jimmer.schema.Filter
import cn.nkpro.elcube.jimmer.schema.Specification

class Search {

    // 表格配置
    var pagination: Boolean = true
    var pageSize: Int = 20
    var sortable: Boolean = true
    var filterable: Boolean = true
    var selectable: Boolean = true
    var exportable: Boolean = true

    // 搜索配置
    var searchEnabled: Boolean = true
    var advancedSearch: Boolean = true
    var quickSearch: Boolean = true

    private val columns = mutableListOf<Column>()
    private val searchFields = mutableListOf<Specification>()
    private val actions = mutableListOf<Action>()
    private val filters = mutableListOf<Filter>()

    /**
     * 定义列
     */
    fun column(field: String, init: () -> Unit) {
        val column = Column().apply {
            label = generateLabel(field)
            fieldName = field
            init()
        }
        columns.add(column)
    }

    /**
     * 定义搜索字段
     */
    fun searchField(field: String, init: Specification.() -> Unit) {
        val specification = Specification().apply {
            fieldName = field
            init()
        }
        searchFields.add(specification)
    }

    /**
     * 定义动作
     */
    fun action(name: String, init: Action.() -> Unit) {
        val action = Action().apply {
            actionName = name
            init()
        }
        actions.add(action)
    }

    /**
     * 定义过滤器
     */
    fun filter(field: String, init: Filter.() -> Unit) {
        val filter = Filter().apply {
            fieldName = field
            init()
        }
        filters.add(filter)
    }

    /**
     * 快速配置 - 标准列表
     */
    fun standardList(vararg fields: String) {
        fields.forEach { field ->
            column(field) {
                sortable = true
                filterable = true
            }
        }

        // 标准动作
        action("view") {
            label = "查看"
            icon = "el-icon-view"
            type = "link"
            position = "row"
        }

        action("edit") {
            label = "编辑"
            icon = "el-icon-edit"
            type = "link"
            position = "row"
            condition = "row.editable"
        }

        action("delete") {
            label = "删除"
            icon = "el-icon-delete"
            type = "link"
            position = "row"
            condition = "row.deletable"
            confirm = true
        }
    }

    /**
     * 快速配置 - 简单搜索
     */
    fun simpleSearch(vararg fields: String) {
        searchEnabled = true
        quickSearch = true

        fields.forEach { field ->
            searchField(field) {
                label = generateLabel(field)
                type = "input"
                operator = "like"
            }
        }
    }

    private fun generateLabel(field: String): String {
        return field.replaceFirstChar { it.uppercase() }
    }

    fun build(): UiDefinition {
        return UiDefinition(
            layouts = emptyList(),
            components = listOf(
                ComponentDefinition(
                    componentName = "dataTable",
                    componentType = "TABLE",
                    title = null,
                    description = null,
                    dataSource = null,
                    dataPath = null,
                    visibleWhen = null,
                    enabledWhen = null,
                    config = mapOf(
                        "pagination" to pagination,
                        "pageSize" to pageSize,
                        "sortable" to sortable,
                        "filterable" to filterable,
                        "columns" to columns,
                        "actions" to actions,
                        "searchFields" to searchFields,
                        "filters" to filters
                    ),
                    events = emptyMap(),
                    slots = emptyMap()
                )
            ),
            themes = emptyMap()
        )
    }
}