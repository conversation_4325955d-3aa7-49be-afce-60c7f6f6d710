package cn.nkpro.elcube.jimmer.schema.page

import cn.nkpro.elcube.jimmer.schema.Action
import cn.nkpro.elcube.jimmer.schema.EditSectionDsl
import cn.nkpro.elcube.jimmer.schema.ValidationDsl

class Edit {

    var layout: String = "form"
    var labelWidth: String = "120px"
    var validateOnRuleChange: Boolean = true
    var showMessage: Boolean = true

    private val sections = mutableListOf<EditSectionDsl>()
    private val actions = mutableListOf<Action>()
    private val validations = mutableListOf<ValidationDsl>()

    /**
     * 定义区域
     */
    fun section(name: String, init: EditSectionDsl.() -> Unit) {
        val sectionDsl = EditSectionDsl().apply {
            sectionName = name
            init()
        }
        sections.add(sectionDsl)
    }

    /**
     * 定义动作
     */
    fun action(name: String, init: Action.() -> Unit) {
        val action = Action().apply {
            actionName = name
            init()
        }
        actions.add(action)
    }

    /**
     * 定义验证
     */
    fun validation(field: String, init: ValidationDsl.() -> Unit) {
        val validationDsl = ValidationDsl().apply {
            fieldName = field
            init()
        }
        validations.add(validationDsl)
    }

    /**
     * 快速配置 - 标准编辑页
     */
    fun standardEdit(vararg fields: String) {
        section("basic") {
            title = "基本信息"
            span = 24

            fields.forEach { field ->
                field(field) {
                    label = generateLabel(field)
                    required = isRequiredField(field)
                }
            }
        }

        // 标准动作
        action("save") {
            label = "保存"
            icon = "el-icon-check"
            type = "button"
            style = "primary"
        }

        action("cancel") {
            label = "取消"
            icon = "el-icon-close"
            type = "button"
        }
    }

    private fun generateLabel(field: String): String {
        return field.replaceFirstChar { it.uppercase() }
    }

    private fun isRequiredField(field: String): Boolean {
        return field in listOf("name", "title", "code")
    }

    fun build(): UiDefinition {
        return UiDefinition(
            layouts = listOf(
                LayoutDefinition(
                    layoutName = "edit",
                    layoutType = layout,
                    responsive = true,
                    sections = sections.map { it.build() },
                    config = mapOf(
                        "labelWidth" to labelWidth,
                        "actions" to actions,
                        "validations" to validations
                    )
                )
            ),
            components = emptyList(),
            themes = emptyMap()
        )
    }
}