package cn.nkpro.elcube.jimmer.schema.page

import kotlin.reflect.KClass

/**
 * ELCube Schema核心定义
 * 清洁架构：LIST、VIEW、EDIT、MODEL四种Schema类型
 */

/**
 * Schema类型枚举
 */
enum class SchemaType(val value: String) {
    LIST("LIST"),       // 列表页Schema（包含搜索功能）
    VIEW("VIEW"),       // 查看页Schema（详情页）
    EDIT("EDIT"),       // 编辑页Schema（新增/编辑）
    MODEL("MODEL")      // 模型Schema（业务逻辑定义）
}

/**
 * 模型注解 - 重命名自SchemaBinding，更清晰的命名
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class Model(
    /**
     * 模型名称
     */
    val name: String = "",

    /**
     * 模型描述
     */
    val desc: String = "",

    /**
     * 模型分类
     */
    val category: String = "BUSINESS",

    /**
     * 模型版本
     */
    val version: String = "1.0.0",

    /**
     * 是否启用
     */
    val enabled: Boolean = true,

    /**
     * 权限配置
     */
    val power: PowerConfig = PowerConfig(),

    /**
     * 列表视图配置
     */
    val views: ViewsConfig = ViewsConfig(),

    /**
     * 状态流定义类
     */
    val state: KClass<*> = Nothing::class,

    /**
     * 生命周期定义类
     */
    val lifecycle: KClass<*> = Nothing::class,

    /**
     * 单据链接定义类
     */
    val link: KClass<*> = Nothing::class,

    /**
     * 业务模型Schema类
     */
    val schema: KClass<*> = Nothing::class,

    /**
     * 数据代理类
     */
    val dataProxy: KClass<*> = Nothing::class,

    /**
     * 自定义扩展参数
     */
    val params: Array<KV> = []
)

/**
 * 权限配置
 */
annotation class PowerConfig(
    /**
     * 是否可新增
     */
    val add: Boolean = true,

    /**
     * 是否可编辑
     */
    val edit: Boolean = true,

    /**
     * 是否可删除
     */
    val delete: Boolean = true,

    /**
     * 是否可查看
     */
    val view: Boolean = true,

    /**
     * 是否可导出
     */
    val export: Boolean = true,

    /**
     * 是否可导入
     */
    val import: Boolean = false,

    /**
     * 是否可打印
     */
    val print: Boolean = true,

    /**
     * 权限表达式
     */
    val expression: String = ""
)

/**
 * 视图配置
 */
annotation class ViewsConfig(
    /**
     * 列表页Schema类
     */
    val list: KClass<*> = Nothing::class,

    /**
     * 详情页Schema类
     */
    val view: KClass<*> = Nothing::class,

    /**
     * 编辑页Schema类
     */
    val edit: KClass<*> = Nothing::class,

    /**
     * 是否启用分页
     */
    val pagination: Boolean = true,

    /**
     * 默认页大小
     */
    val pageSize: Int = 20,

    /**
     * 是否可排序
     */
    val sortable: Boolean = true,

    /**
     * 是否可过滤
     */
    val filterable: Boolean = true,

    /**
     * 是否可选择
     */
    val selectable: Boolean = true,

    /**
     * 默认排序字段
     */
    val orderBy: String = "",

    /**
     * 排序方向
     */
    val orderDirection: String = "desc"
)

/**
 * 键值对配置
 */
annotation class KV(
    val key: String,
    val value: String
)

/**
 * Schema实现接口
 */
interface SchemaImplementation {
    val schemaId: String
    val schemaName: String
    val schemaType: SchemaType
    fun buildUiDefinition(): UiDefinition
}

/**
 * 列表页Schema接口
 */
interface ListSchemaImplementation : SchemaImplementation {
    override val schemaType: SchemaType get() = SchemaType.LIST
    fun defineList(): Search.() -> Unit
}

/**
 * 查看页Schema接口
 */
interface ViewSchemaImplementation : SchemaImplementation {
    override val schemaType: SchemaType get() = SchemaType.VIEW
    fun defineView(): ViewSchemaDsl.() -> Unit
}

/**
 * 编辑页Schema接口
 */
interface EditSchemaImplementation : SchemaImplementation {
    override val schemaType: SchemaType get() = SchemaType.EDIT
    fun defineEdit(): Edit.() -> Unit
}
/**
 * 抽象Schema基类
 */
abstract class AbstractSchemaImplementation<T : Any>(
    override val schemaId: String,
    override val schemaName: String,
    protected val modelClass: KClass<T>
) : SchemaImplementation

/**
 * 抽象列表页Schema
 */
abstract class AbstractListSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>
) : AbstractSchemaImplementation<T>(schemaId, schemaName, modelClass), ListSchemaImplementation {
    
    override fun buildUiDefinition(): UiDefinition {
        val listDsl = Search().apply {
            defineList().invoke(this)
        }
        return listDsl.build()
    }
}

/**
 * 抽象查看页Schema
 */
abstract class AbstractViewSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>
) : AbstractSchemaImplementation<T>(schemaId, schemaName, modelClass), ViewSchemaImplementation {
    
    override fun buildUiDefinition(): UiDefinition {
        val viewDsl = ViewSchemaDsl().apply {
            defineView().invoke(this)
        }
        return viewDsl.build()
    }
}

/**
 * 抽象编辑页Schema
 */
abstract class AbstractEditSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>
) : AbstractSchemaImplementation<T>(schemaId, schemaName, modelClass), EditSchemaImplementation {
    
    override fun buildUiDefinition(): UiDefinition {
        val editDsl = Edit().apply {
            defineEdit().invoke(this)
        }
        return editDsl.build()
    }
}

/**
 * UI定义数据结构
 */
data class UiDefinition(
    val layouts: List<LayoutDefinition>,
    val components: List<ComponentDefinition>,
    val themes: Map<String, Any>
)

data class LayoutDefinition(
    val layoutName: String,
    val layoutType: String,
    val responsive: Boolean,
    val sections: List<SectionDefinition>,
    val config: Map<String, Any>
)

data class SectionDefinition(
    val sectionName: String,
    val title: String?,
    val span: Int,
    val order: Int,
    val fields: List<FieldDefinition>
)

data class FieldDefinition(
    val fieldName: String,
    val label: String,
    val type: String,
    val required: Boolean,
    val readonly: Boolean,
    val span: Int,
    val order: Int,
    val config: Map<String, Any>
)

data class ComponentDefinition(
    val componentName: String,
    val componentType: String,
    val title: String?,
    val description: String?,
    val dataSource: String?,
    val dataPath: String?,
    val visibleWhen: String?,
    val enabledWhen: String?,
    val config: Map<String, Any>,
    val events: Map<String, String>,
    val slots: Map<String, Any>
)

/**
 * 验证规则
 */
data class ValidationRule(
    val rule: String,
    val message: String
)

/**
 * 表格动作配置
 */
data class TableActionConfig(
    val actionName: String,
    val actionLabel: String,
    val position: String,
    val icon: String?,
    val condition: String?,
    val handler: String
)

/**
 * DSL标记注解
 */
@DslMarker
annotation class SchemaChain
