package cn.nkpro.elcube.jimmer.schema.model

import cn.nkpro.elcube.jimmer.schema.page.SchemaChain

/**
* 生命周期DSL
*/
@SchemaChain
class LifeCycle {
    var enabled: Boolean = true
    var autoArchive: Boolean = false
    var archiveDays: Int = 365

    private val hooks = mutableListOf<LifeCycleHook>()

    fun hook(event: String, init: LifeCycleHook.() -> Unit) {
        val hookDsl = LifeCycleHook().apply {
            eventName = event
            init()
        }
        hooks.add(hookDsl)
    }

    fun standardLifeCycle() {
        hook("beforeCreate") {
            handler = "generateId"
            order = 1
        }

        hook("afterCreate") {
            handler = "sendNotification"
            order = 1
        }

        hook("beforeUpdate") {
            handler = "validateData"
            order = 1
        }

        hook("afterUpdate") {
            handler = "updateIndex"
            order = 1
        }

        hook("beforeDelete") {
            handler = "checkReferences"
            order = 1
        }
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "enabled" to enabled,
            "autoArchive" to autoArchive,
            "archiveDays" to archiveDays,
            "hooks" to hooks.map { it.build() }
        )
    }
}


/**
 * 生命周期钩子DSL
 */
@SchemaChain
class LifeCycleHook {
    var eventName: String = ""
    var handler: String = ""
    var condition: String? = null
    var async: Boolean = false
    var order: Int = 0

    fun build(): LifeCycleHook {
        return this
    }
}


/**
 * 生命周期定义基类
 */
abstract class AbstractLifecycleDefinition {

    /**
     * 定义生命周期
     */
    abstract fun defineLifecycle(): LifeCycle.() -> Unit

    /**
     * 构建生命周期定义
     */
    fun buildLifecycleDefinition(): Map<String, Any> {
        val lifecycleDsl = LifeCycle().apply {
            defineLifecycle().invoke(this)
        }
        return lifecycleDsl.build()
    }
}