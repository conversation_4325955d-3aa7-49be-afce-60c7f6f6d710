package cn.nkpro.elcube.jimmer.schema

import cn.nkpro.elcube.jimmer.schema.page.FieldDefinition
import cn.nkpro.elcube.jimmer.schema.page.SchemaChain
import cn.nkpro.elcube.jimmer.schema.page.SectionDefinition
import cn.nkpro.elcube.jimmer.schema.page.ValidationRule

/**
 * DSL组件实现
 * 包含各种DSL的具体实现类
 */

/**
 * 列DSL
 */
@SchemaChain
class Column {
    var fieldName: String = ""
    var label: String = ""
    var width: String? = null
    var minWidth: String? = null
    var align: String = "left"
    var sortable: Boolean = true
    var filterable: Boolean = true
    var resizable: Boolean = true
    var fixed: String? = null
    var visible: Boolean = true
    var order: Int = 0
    var format: String? = null
    var formatter: String? = null
    var editable: Boolean = false
    var editType: String = "input"
    var visibleWhen: String? = null
    var editableWhen: String? = null
    var className: String? = null
    var headerClassName: String? = null
}

/**
 * 搜索字段DSL
 */
@SchemaChain
class Specification {
    var fieldName: String = ""
    var label: String = ""
    var type: String = "input"
    var operator: String = "eq"
    var placeholder: String = ""
    var required: Boolean = false
    var span: Int = 6
    var order: Int = 0
    var options: List<Map<String, Any>> = emptyList()
    var dataSource: String? = null
    var valueField: String = "value"
    var labelField: String = "label"
    var dateFormat: String = "YYYY-MM-DD"
    var dateType: String = "date"
    var min: Number? = null
    var max: Number? = null
    var precision: Int = 2
    var visibleWhen: String? = null
    
    fun input(placeholder: String = "") {
        type = "input"
        this.placeholder = placeholder
    }
    
    fun select(dataSource: String, valueField: String = "value", labelField: String = "label") {
        type = "select"
        this.dataSource = dataSource
        this.valueField = valueField
        this.labelField = labelField
    }
    
    fun dateRange(format: String = "YYYY-MM-DD") {
        type = "daterange"
        dateFormat = format
        operator = "between"
    }
    
    fun numberRange(min: Number? = null, max: Number? = null) {
        type = "numberrange"
        this.min = min
        this.max = max
        operator = "between"
    }
}

/**
 * 动作DSL
 */
@SchemaChain
class Action {
    var actionName: String = ""
    var label: String = ""
    var icon: String? = null
    var type: String = "button"
    var style: String = "default"
    var size: String = "default"
    var position: String = "toolbar"
    var order: Int = 0
    var handler: String? = null
    var url: String? = null
    var method: String = "GET"
    var confirm: Boolean = false
    var confirmMessage: String = "确定要执行此操作吗？"
    var condition: String? = null
    var visibleWhen: String? = null
    var enabledWhen: String? = null
    var permission: String? = null
    var roles: List<String> = emptyList()
    var config: Map<String, Any> = emptyMap()
    
    fun primary() { style = "primary" }
    fun danger() { style = "danger" }
    fun link() { type = "link" }
    fun dropdown() { type = "dropdown" }
    fun toolbar() { position = "toolbar" }
    fun row() { position = "row" }
    fun needConfirm(message: String = "确定要执行此操作吗？") {
        confirm = true
        confirmMessage = message
    }
}

/**
 * 过滤器DSL
 */
@SchemaChain
class Filter {
    var fieldName: String = ""
    var label: String = ""
    var type: String = "select"
    var multiple: Boolean = false
    var options: List<Map<String, Any>> = emptyList()
    var dataSource: String? = null
    var defaultValue: Any? = null
    
    fun option(value: Any, label: String) {
        options = options + mapOf("value" to value, "label" to label)
    }
}

/**
 * 查看页区域DSL
 */
@SchemaChain
class ViewSectionDsl {
    var sectionName: String = ""
    var title: String? = null
    var span: Int = 12
    var order: Int = 0
    var collapsible: Boolean = false
    var defaultCollapsed: Boolean = false
    var border: Boolean = true
    
    private val fields = mutableListOf<ViewFieldDsl>()
    
    fun field(name: String, init: ViewFieldDsl.() -> Unit) {
        val fieldDsl = ViewFieldDsl().apply {
            fieldName = name
            init()
        }
        fields.add(fieldDsl)
    }
    
    fun build(): SectionDefinition {
        return SectionDefinition(
            sectionName = sectionName,
            title = title,
            span = span,
            order = order,
            fields = fields.map { it.build() }
        )
    }
}

/**
 * 查看页字段DSL
 */
@SchemaChain
class ViewFieldDsl {
    var fieldName: String = ""
    var label: String = ""
    var span: Int = 6
    var order: Int = 0
    var readonly: Boolean = true
    var visible: Boolean = true
    var format: String = ""
    var formatter: String = ""
    var prefix: String = ""
    var suffix: String = ""
    var visibleWhen: String = ""
    var className: String = ""
    var labelClassName: String = ""
    
    fun currency(symbol: String = "¥") {
        format = "#,##0.00"
        prefix = symbol
    }
    
    fun percentage() {
        suffix = "%"
    }
    
    fun date(format: String = "YYYY-MM-DD") {
        this.format = format
    }
    
    fun datetime(format: String = "YYYY-MM-DD HH:mm:ss") {
        this.format = format
    }
    
    fun build(): FieldDefinition {
        return FieldDefinition(
            fieldName = fieldName,
            label = label,
            type = "display",
            required = false,
            readonly = readonly,
            span = span,
            order = order,
            config = mapOf(
                "format" to format,
                "prefix" to prefix,
                "suffix" to suffix,
                "visible" to visible,
                "visibleWhen" to visibleWhen
            ).filterValues { it != null }
        )
    }
}

/**
 * 编辑页区域DSL
 */
@SchemaChain
class EditSectionDsl {
    var sectionName: String = ""
    var title: String? = null
    var span: Int = 24
    var order: Int = 0
    var collapsible: Boolean = false
    var defaultCollapsed: Boolean = false
    
    private val fields = mutableListOf<EditFieldDsl>()
    
    fun field(name: String, init: EditFieldDsl.() -> Unit) {
        val fieldDsl = EditFieldDsl().apply {
            fieldName = name
            init()
        }
        fields.add(fieldDsl)
    }
    
    fun build(): SectionDefinition {
        return SectionDefinition(
            sectionName = sectionName,
            title = title,
            span = span,
            order = order,
            fields = fields.map { it.build() }
        )
    }
}

/**
 * 编辑页字段DSL
 */
@SchemaChain
class EditFieldDsl {
    var fieldName: String = ""
    var label: String = ""
    var type: String = "input"
    var span: Int = 12
    var order: Int = 0
    var required: Boolean = false
    var readonly: Boolean = false
    var disabled: Boolean = false
    var visible: Boolean = true
    var placeholder: String = ""
    var maxLength: Int = -1
    var minLength: Int = -1
    var rows: Int = 3
    var options: List<Map<String, Any>> = emptyList()
    var dataSource: String = ""
    var valueField: String = "value"
    var labelField: String = "label"
    var multiple: Boolean = false
    var min: Number = 0
    var max: Number = 0
    var precision: Int = 2
    var step: Number = 1
    var dateFormat: String = "YYYY-MM-DD"
    var dateType: String = "date"
    var visibleWhen: String = ""
    var readonlyWhen: String = ""
    var requiredWhen: String = ""
    var rules: List<String> = emptyList()
    
    private var config: MutableMap<String, Any> = mutableMapOf()
    
    fun input(placeholder: String = "", maxLength: Int = 0 ) {
        type = "input"
        this.placeholder = placeholder
        this.maxLength = maxLength
    }
    
    fun textarea(rows: Int = 3, placeholder: String = "") {
        type = "textarea"
        this.rows = rows
        this.placeholder = placeholder
    }
    
    fun select(dataSource: String, valueField: String = "value", labelField: String = "label") {
        type = "select"
        this.dataSource = dataSource
        this.valueField = valueField
        this.labelField = labelField
    }
    
    fun multiSelect(dataSource: String, valueField: String = "value", labelField: String = "label") {
        type = "select"
        multiple = true
        this.dataSource = dataSource
        this.valueField = valueField
        this.labelField = labelField
    }
    
    fun number(min: Number = 0, max: Number = 0, precision: Int = 2) {
        type = "number"
        this.min = min
        this.max = max
        this.precision = precision
    }
    
    fun date(format: String = "YYYY-MM-DD") {
        type = "date"
        dateFormat = format
    }
    
    fun datetime(format: String = "YYYY-MM-DD HH:mm:ss") {
        type = "datetime"
        dateFormat = format
    }
    
    fun switch() {
        type = "switch"
    }
    
    fun radio(options: List<Pair<Any, String>>) {
        type = "radio"
        this.options = options.map { mapOf("value" to it.first, "label" to it.second) }
    }
    
    fun checkbox(options: List<Pair<Any, String>>) {
        type = "checkbox"
        this.options = options.map { mapOf("value" to it.first, "label" to it.second) }
    }
    
    fun upload(accept: String = "*", multiple: Boolean = false) {
        type = "upload"
        this.multiple = multiple
        config["accept"] = accept
    }
    
    fun build(): FieldDefinition {
        return FieldDefinition(
            fieldName = fieldName,
            label = label,
            type = type,
            required = required,
            readonly = readonly,
            span = span,
            order = order,
            config = mapOf(
                "placeholder" to placeholder,
                "maxLength" to maxLength,
                "rows" to rows,
                "dataSource" to dataSource,
                "valueField" to valueField,
                "labelField" to labelField,
                "multiple" to multiple,
                "min" to min,
                "max" to max,
                "precision" to precision,
                "dateFormat" to dateFormat,
                "visible" to visible,
                "visibleWhen" to visibleWhen,
                "readonlyWhen" to readonlyWhen,
                "requiredWhen" to requiredWhen
            ).filterValues { it != null } + config
        )
    }
}

/**
 * 验证DSL
 */
@SchemaChain
class ValidationDsl {
    var fieldName: String = ""
    var rules: MutableList<ValidationRule> = mutableListOf()
    
    fun required(message: String = "此字段为必填项") {
        rules.add(ValidationRule("required", message))
    }
    
    fun minLength(length: Int, message: String = "长度不能少于${length}个字符") {
        rules.add(ValidationRule("minLength:$length", message))
    }
    
    fun maxLength(length: Int, message: String = "长度不能超过${length}个字符") {
        rules.add(ValidationRule("maxLength:$length", message))
    }
    
    fun pattern(regex: String, message: String = "格式不正确") {
        rules.add(ValidationRule("pattern:$regex", message))
    }
    
    fun email(message: String = "邮箱格式不正确") {
        rules.add(ValidationRule("email", message))
    }
    
    fun phone(message: String = "手机号格式不正确") {
        rules.add(ValidationRule("phone", message))
    }
    
    fun custom(rule: String, message: String) {
        rules.add(ValidationRule(rule, message))
    }
}
