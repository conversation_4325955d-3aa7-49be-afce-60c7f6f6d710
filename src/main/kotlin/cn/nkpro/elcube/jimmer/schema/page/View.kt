package cn.nkpro.elcube.jimmer.schema.page

import cn.nkpro.elcube.jimmer.schema.Action
import cn.nkpro.elcube.jimmer.schema.ViewSectionDsl

class ViewSchemaDsl {

    var layout: String = "grid"
    var responsive: Boolean = true
    var printable: Boolean = true
    var exportable: Boolean = true

    private val sections = mutableListOf<ViewSectionDsl>()
    private val actions = mutableListOf<Action>()

    /**
     * 定义区域
     */
    fun section(name: String, init: ViewSectionDsl.() -> Unit) {
        val sectionDsl = ViewSectionDsl().apply {
            sectionName = name
            init()
        }
        sections.add(sectionDsl)
    }

    /**
     * 定义动作
     */
    fun action(name: String, init: Action.() -> Unit) {
        val action = Action().apply {
            actionName = name
            init()
        }
        actions.add(action)
    }

    /**
     * 快速配置 - 标准查看页
     */
    fun standardView(vararg fields: String) {
        section("basic") {
            title = "基本信息"
            span = 24

            fields.forEach { field ->
                field(field) {
                    label = generateLabel(field)
                    readonly = true
                }
            }
        }

        // 标准动作
        action("edit") {
            label = "编辑"
            icon = "el-icon-edit"
            type = "button"
            condition = "data.editable"
        }

        action("print") {
            label = "打印"
            icon = "el-icon-printer"
            type = "button"
        }
    }

    private fun generateLabel(field: String): String {
        return field.replaceFirstChar { it.uppercase() }
    }

    fun build(): UiDefinition {
        return UiDefinition(
            layouts = listOf(
                LayoutDefinition(
                    layoutName = "view",
                    layoutType = layout,
                    responsive = responsive,
                    sections = sections.map { it.build() },
                    config = mapOf(
                        "actions" to actions
                    )
                )
            ),
            components = emptyList(),
            themes = emptyMap()
        )
    }
}