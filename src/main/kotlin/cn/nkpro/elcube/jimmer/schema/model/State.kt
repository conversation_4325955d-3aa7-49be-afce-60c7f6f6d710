package cn.nkpro.elcube.jimmer.schema.model

import cn.nkpro.elcube.jimmer.schema.page.SchemaChain
import kotlin.reflect.KProperty1

@SchemaChain
class State {
    var flowName: String = ""
    var description: String = ""
    var enabled: Boolean = true

    private val states = mutableListOf<StateDsl<T>>()
    private val transitions = mutableListOf<TransitionDsl>()

    fun state(code: String, init: StateDsl.() -> Unit) {
        val stateDsl = StateDsl().apply {
            stateCode = code
            init()
        }
        states.add(stateDsl)
    }

    fun transition(from: String, to: String, init: TransitionDsl.() -> Unit = {}) {
        val transitionDsl = TransitionDsl().apply {
            fromState = from
            toState = to
            init()
        }
        transitions.add(transitionDsl)
    }

    fun standardFlow() {
        state("DRAFT") {
            stateName = "草稿"
            isInitial = true
            stateColor = "#909399"
        }

        state("ACTIVE") {
            stateName = "生效"
            stateColor = "#67C23A"
        }

        state("INACTIVE") {
            stateName = "失效"
            isFinal = true
            stateColor = "#F56C6C"
        }

        transition("DRAFT", "ACTIVE") {
            transitionName = "提交"
            condition = "hasPermission('SUBMIT')"
        }

        transition("ACTIVE", "INACTIVE") {
            transitionName = "作废"
            condition = "hasPermission('CANCEL')"
        }
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "flowName" to flowName,
            "description" to description,
            "enabled" to enabled,
            "states" to states.map { it.build() },
            "transitions" to transitions.map { it.build() }
        )
    }
}

/**
 * 状态DSL - 类型安全版本
 */
@SchemaChain
class StateDsl<T : Any> {
    var stateCode: String = ""
    var stateName: String = ""
    var stateDesc: String? = null
    var stateColor: String? = null
    var isInitial: Boolean = false
    var isFinal: Boolean = false
    var order: Int = 0

    private val actions = mutableListOf<StateActionDsl<T>>()
    private val fieldRules = mutableListOf<FieldRuleDsl<T>>()

    fun action(name: String, init: StateActionDsl<T>.() -> Unit) {
        val actionDsl = StateActionDsl<T>().apply {
            actionName = name
            init()
        }
        actions.add(actionDsl)
    }

    fun fieldRule(field: KProperty1<T, *>, init: FieldRuleDsl<T>.() -> Unit) {
        val ruleDsl = FieldRuleDsl<T>().apply {
            fieldProperty = field
            init()
        }
        fieldRules.add(ruleDsl)
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "stateCode" to stateCode,
            "stateName" to stateName,
            "stateDesc" to stateDesc,
            "stateColor" to stateColor,
            "isInitial" to isInitial,
            "isFinal" to isFinal,
            "order" to order,
            "actions" to actions.map { it.build() },
            "fieldRules" to fieldRules.map { it.build() }
        )
    }
}

/**
 * 状态转换DSL
 */
@SchemaChain
class TransitionDsl<S,T> {
    var fromState: String = ""
    var toState: String = ""
    var transitionName: String = ""
    var condition: String? = null
    var handler: String? = null
    var order: Int = 0

    fun build(): Map<String, Any> {
        return mapOf(
            "fromState" to fromState,
            "toState" to toState,
            "transitionName" to transitionName,
            "condition" to condition,
            "handler" to handler,
            "order" to order
        )
    }
}

/**
 * 状态动作DSL - 类型安全版本
 */
@SchemaChain
class StateActionDsl<T : Any> {
    var actionName: String = ""
    var actionLabel: String = ""
    var actionType: String = "BUTTON"
    var targetState: String? = null
    var actionCondition: ((T) -> Boolean)? = null
    var handler: String? = null
    var icon: String? = null
    var order: Int = 0

    /**
     * 设置动作条件 - 类型安全
     */
    fun condition(predicate: (T) -> Boolean) {
        actionCondition = predicate
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "actionName" to actionName,
            "actionLabel" to actionLabel,
            "actionType" to actionType,
            "targetState" to targetState,
            "condition" to condition,
            "handler" to handler,
            "icon" to icon,
            "order" to order
        )
    }
}

/**
 * 字段规则DSL - 类型安全版本
 */
@SchemaChain
class FieldRuleDsl<T : Any> {
    var fieldProperty: KProperty1<T, *>? = null
    var visible: Boolean = true
    var readonly: Boolean = false
    var required: Boolean = false
    var visibleCondition: ((T) -> Boolean)? = null
    var readonlyCondition: ((T) -> Boolean)? = null
    var requiredCondition: ((T) -> Boolean)? = null

    /**
     * 设置可见条件 - 类型安全
     */
    fun visibleWhen(predicate: (T) -> Boolean) {
        visibleCondition = predicate
    }

    /**
     * 设置只读条件 - 类型安全
     */
    fun readonlyWhen(predicate: (T) -> Boolean) {
        readonlyCondition = predicate
    }

    /**
     * 设置必填条件 - 类型安全
     */
    fun requiredWhen(predicate: (T) -> Boolean) {
        requiredCondition = predicate
    }

    fun build(): Map<String, Any> {
        return mapOf(
            "fieldName" to fieldName,
            "visible" to visible,
            "readonly" to readonly,
            "required" to required,
            "visibleCondition" to visibleCondition,
            "readonlyCondition" to readonlyCondition,
            "requiredCondition" to requiredCondition
        )
    }
}


/**
 * 状态流定义基类
 */
abstract class AbstractStateDefinition {

    /**
     * 定义状态流
     */
    abstract fun defineState(): State.() -> Unit

    /**
     * 构建状态流定义
     */
    fun buildStateDefinition(): Map<String, Any> {
        val stateDsl = State().apply {
            defineState().invoke(this)
        }
        return stateDsl.build()
    }
}

