package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*

/**
 * 单据卡片定义
 */
@Entity
@Table(name = "nk_doc_card")
interface DocCard : BusinessEntity {
    
    /**
     * 卡片编码
     */
    @Key
    @Column(name = "card_code")
    val cardCode: String
    
    /**
     * 卡片名称
     */
    @Column(name = "card_name")
    val cardName: String
    
    /**
     * 卡片描述
     */
    @Column(name = "card_desc")
    val cardDesc: String?
    
    /**
     * 卡片类型
     */
    @Column(name = "card_type")
    val cardType: String
    
    /**
     * 卡片位置
     */
    @Column(name = "card_position")
    val cardPosition: String
    
    /**
     * 所属单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType
    
    /**
     * 组件名称
     */
    @Column(name = "component_name")
    val componentName: String
    
    /**
     * 卡片配置JSON
     */
    @Column(name = "card_config")
    val cardConfig: String?
    
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    val enabled: Boolean
    
    /**
     * 是否必填
     */
    @Column(name = "required")
    val required: Boolean
    
    /**
     * 是否只读
     */
    @Column(name = "readonly")
    val readonly: Boolean
    
    /**
     * 是否可见
     */
    @Column(name = "visible")
    val visible: Boolean
    
    /**
     * 显示条件表达式
     */
    @Column(name = "visible_expr")
    val visibleExpr: String?
    
    /**
     * 只读条件表达式
     */
    @Column(name = "readonly_expr")
    val readonlyExpr: String?
    
    /**
     * 必填条件表达式
     */
    @Column(name = "required_expr")
    val requiredExpr: String?
    
    /**
     * 计算表达式
     */
    @Column(name = "calc_expr")
    val calcExpr: String?
    
    /**
     * 验证表达式
     */
    @Column(name = "validate_expr")
    val validateExpr: String?
    
    /**
     * 卡片字段定义
     */
    @OneToMany(mappedBy = "card")
    val fields: List<DocCardField>
    
    /**
     * 卡片数据
     */
    @OneToMany(mappedBy = "card")
    val cardData: List<DocumentCardData>
}

/**
 * 卡片字段定义
 */
@Entity
@Table(name = "nk_doc_card_field")
interface DocCardField : BusinessEntity {
    
    /**
     * 字段编码
     */
    @Key
    @Column(name = "field_code")
    val fieldCode: String
    
    /**
     * 字段名称
     */
    @Column(name = "field_name")
    val fieldName: String
    
    /**
     * 字段类型
     */
    @Column(name = "field_type")
    val fieldType: String
    
    /**
     * 所属卡片
     */
    @ManyToOne
    @JoinColumn(name = "card_id")
    val card: DocCard
    
    /**
     * 字段长度
     */
    @Column(name = "field_length")
    val fieldLength: Int?
    
    /**
     * 小数位数
     */
    @Column(name = "decimal_places")
    val decimalPlaces: Int?
    
    /**
     * 默认值
     */
    @Column(name = "default_value")
    val defaultValue: String?
    
    /**
     * 是否必填
     */
    @Column(name = "required")
    val required: Boolean
    
    /**
     * 是否只读
     */
    @Column(name = "readonly")
    val readonly: Boolean
    
    /**
     * 是否可见
     */
    @Column(name = "visible")
    val visible: Boolean
    
    /**
     * 字段配置JSON
     */
    @Column(name = "field_config")
    val fieldConfig: String?
}
