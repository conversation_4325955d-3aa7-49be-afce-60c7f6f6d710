package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*
import java.time.LocalDateTime

/**
 * 基础实体接口，定义所有实体的公共字段
 */
@MappedSuperclass
interface BaseEntity {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: String
    
    /**
     * 创建时间
     */
    @Column(name = "created_time")
    val createdTime: LocalDateTime
    
    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    val updatedTime: LocalDateTime
    
    /**
     * 创建人
     */
    @Column(name = "created_by")
    val createdBy: String?
    
    /**
     * 更新人
     */
    @Column(name = "updated_by")
    val updatedBy: String?
    
    /**
     * 版本号（乐观锁）
     */
    @Version
    val version: Int
    
    /**
     * 是否删除
     */
    @LogicalDeleted("true")
    val deleted: Boolean
}

/**
 * 审计实体接口，包含审计相关字段
 */
@MappedSuperclass
interface AuditableEntity : BaseEntity {
    
    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    val tenantId: String?
    
    /**
     * 组织ID
     */
    @Column(name = "org_id")
    val orgId: String?
    
    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    val deptId: String?
}

/**
 * 业务实体接口，包含业务相关字段
 */
@MappedSuperclass
interface BusinessEntity : AuditableEntity {
    
    /**
     * 业务编码
     */
    @Column(name = "business_code")
    val businessCode: String?
    
    /**
     * 业务名称
     */
    @Column(name = "business_name")
    val businessName: String?
    
    /**
     * 业务状态
     */
    @Column(name = "business_status")
    val businessStatus: String?
    
    /**
     * 排序号
     */
    @Column(name = "sort_order")
    val sortOrder: Int?
    
    /**
     * 备注
     */
    @Column(name = "remark")
    val remark: String?
}
