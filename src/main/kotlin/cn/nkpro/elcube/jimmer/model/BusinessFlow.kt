package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*
import java.time.LocalDateTime

/**
 * 业务流定义
 */
@Entity
@Table(name = "nk_business_flow")
interface BusinessFlow : BusinessEntity {
    
    /**
     * 业务流编码
     */
    @Key
    @Column(name = "flow_code")
    val flowCode: String
    
    /**
     * 业务流名称
     */
    @Column(name = "flow_name")
    val flowName: String
    
    /**
     * 业务流描述
     */
    @Column(name = "flow_desc")
    val flowDesc: String?
    
    /**
     * 业务流类型
     */
    @Column(name = "flow_type")
    val flowType: String
    
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    val enabled: Boolean
    
    /**
     * 是否系统内置
     */
    @Column(name = "system_built_in")
    val systemBuiltIn: Boolean
    
    /**
     * 业务流配置JSON
     */
    @Column(name = "flow_config")
    val flowConfig: String?
    
    /**
     * 业务流节点
     */
    @OneToMany(mappedBy = "businessFlow")
    val nodes: List<BusinessFlowNode>
    
    /**
     * 业务流连接
     */
    @OneToMany(mappedBy = "businessFlow")
    val connections: List<BusinessFlowConnection>
    
    /**
     * 业务流实例
     */
    @OneToMany(mappedBy = "businessFlow")
    val instances: List<BusinessFlowInstance>
}

/**
 * 业务流节点
 */
@Entity
@Table(name = "nk_business_flow_node")
interface BusinessFlowNode : BusinessEntity {
    
    /**
     * 节点编码
     */
    @Key
    @Column(name = "node_code")
    val nodeCode: String
    
    /**
     * 节点名称
     */
    @Column(name = "node_name")
    val nodeName: String
    
    /**
     * 节点类型
     */
    @Column(name = "node_type")
    val nodeType: String
    
    /**
     * 所属业务流
     */
    @ManyToOne
    @JoinColumn(name = "business_flow_id")
    val businessFlow: BusinessFlow
    
    /**
     * 关联单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType?
    
    /**
     * 节点位置X
     */
    @Column(name = "position_x")
    val positionX: Int?
    
    /**
     * 节点位置Y
     */
    @Column(name = "position_y")
    val positionY: Int?
    
    /**
     * 是否开始节点
     */
    @Column(name = "is_start")
    val isStart: Boolean
    
    /**
     * 是否结束节点
     */
    @Column(name = "is_end")
    val isEnd: Boolean
    
    /**
     * 节点配置JSON
     */
    @Column(name = "node_config")
    val nodeConfig: String?
    
    /**
     * 创建条件表达式
     */
    @Column(name = "create_condition")
    val createCondition: String?
    
    /**
     * 自动创建表达式
     */
    @Column(name = "auto_create_expr")
    val autoCreateExpr: String?
    
    /**
     * 数据映射表达式
     */
    @Column(name = "data_mapping_expr")
    val dataMappingExpr: String?
    
    /**
     * 源连接
     */
    @OneToMany(mappedBy = "targetNode")
    val incomingConnections: List<BusinessFlowConnection>
    
    /**
     * 目标连接
     */
    @OneToMany(mappedBy = "sourceNode")
    val outgoingConnections: List<BusinessFlowConnection>
    
    /**
     * 节点实例
     */
    @OneToMany(mappedBy = "flowNode")
    val nodeInstances: List<BusinessFlowNodeInstance>
}

/**
 * 业务流连接
 */
@Entity
@Table(name = "nk_business_flow_connection")
interface BusinessFlowConnection : BaseEntity {
    
    /**
     * 连接编码
     */
    @Key
    @Column(name = "connection_code")
    val connectionCode: String
    
    /**
     * 连接名称
     */
    @Column(name = "connection_name")
    val connectionName: String?
    
    /**
     * 所属业务流
     */
    @ManyToOne
    @JoinColumn(name = "business_flow_id")
    val businessFlow: BusinessFlow
    
    /**
     * 源节点
     */
    @ManyToOne
    @JoinColumn(name = "source_node_id")
    val sourceNode: BusinessFlowNode
    
    /**
     * 目标节点
     */
    @ManyToOne
    @JoinColumn(name = "target_node_id")
    val targetNode: BusinessFlowNode
    
    /**
     * 连接类型
     */
    @Column(name = "connection_type")
    val connectionType: String
    
    /**
     * 触发条件表达式
     */
    @Column(name = "trigger_condition")
    val triggerCondition: String?
    
    /**
     * 数据传递表达式
     */
    @Column(name = "data_transfer_expr")
    val dataTransferExpr: String?
    
    /**
     * 是否自动触发
     */
    @Column(name = "auto_trigger")
    val autoTrigger: Boolean
    
    /**
     * 延迟时间（分钟）
     */
    @Column(name = "delay_minutes")
    val delayMinutes: Int?
    
    /**
     * 连接配置JSON
     */
    @Column(name = "connection_config")
    val connectionConfig: String?
}

/**
 * 业务流实例
 */
@Entity
@Table(name = "nk_business_flow_instance")
interface BusinessFlowInstance : BusinessEntity {
    
    /**
     * 实例编码
     */
    @Key
    @Column(name = "instance_code")
    val instanceCode: String
    
    /**
     * 实例名称
     */
    @Column(name = "instance_name")
    val instanceName: String?
    
    /**
     * 所属业务流
     */
    @ManyToOne
    @JoinColumn(name = "business_flow_id")
    val businessFlow: BusinessFlow
    
    /**
     * 根单据ID
     */
    @Column(name = "root_doc_id")
    val rootDocId: String
    
    /**
     * 根单据
     */
    @ManyToOne
    @JoinColumn(name = "root_doc_id", referencedColumnName = "doc_id")
    val rootDocument: Document
    
    /**
     * 实例状态
     */
    @Column(name = "instance_status")
    val instanceStatus: String
    
    /**
     * 开始时间
     */
    @Column(name = "start_time")
    val startTime: LocalDateTime?
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    val endTime: LocalDateTime?
    
    /**
     * 实例数据JSON
     */
    @Column(name = "instance_data")
    val instanceData: String?
    
    /**
     * 节点实例
     */
    @OneToMany(mappedBy = "flowInstance")
    val nodeInstances: List<BusinessFlowNodeInstance>
    
    /**
     * 流程日志
     */
    @OneToMany(mappedBy = "flowInstance")
    val logs: List<BusinessFlowLog>
}

/**
 * 业务流节点实例
 */
@Entity
@Table(name = "nk_business_flow_node_instance")
interface BusinessFlowNodeInstance : BaseEntity {
    
    /**
     * 节点实例编码
     */
    @Key
    @Column(name = "node_instance_code")
    val nodeInstanceCode: String
    
    /**
     * 所属流程实例
     */
    @ManyToOne
    @JoinColumn(name = "flow_instance_id")
    val flowInstance: BusinessFlowInstance
    
    /**
     * 所属流程节点
     */
    @ManyToOne
    @JoinColumn(name = "flow_node_id")
    val flowNode: BusinessFlowNode
    
    /**
     * 关联单据ID
     */
    @Column(name = "doc_id")
    val docId: String?
    
    /**
     * 关联单据
     */
    @ManyToOne
    @JoinColumn(name = "doc_id", referencedColumnName = "doc_id")
    val document: Document?
    
    /**
     * 节点状态
     */
    @Column(name = "node_status")
    val nodeStatus: String
    
    /**
     * 开始时间
     */
    @Column(name = "start_time")
    val startTime: LocalDateTime?
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    val endTime: LocalDateTime?
    
    /**
     * 节点数据JSON
     */
    @Column(name = "node_data")
    val nodeData: String?
    
    /**
     * 错误信息
     */
    @Column(name = "error_message")
    val errorMessage: String?
}

/**
 * 业务流日志
 */
@Entity
@Table(name = "nk_business_flow_log")
interface BusinessFlowLog : BaseEntity {
    
    /**
     * 所属流程实例
     */
    @ManyToOne
    @JoinColumn(name = "flow_instance_id")
    val flowInstance: BusinessFlowInstance
    
    /**
     * 日志类型
     */
    @Column(name = "log_type")
    val logType: String
    
    /**
     * 日志级别
     */
    @Column(name = "log_level")
    val logLevel: String
    
    /**
     * 日志消息
     */
    @Column(name = "log_message")
    val logMessage: String
    
    /**
     * 相关节点实例
     */
    @ManyToOne
    @JoinColumn(name = "node_instance_id")
    val nodeInstance: BusinessFlowNodeInstance?
    
    /**
     * 日志数据JSON
     */
    @Column(name = "log_data")
    val logData: String?
    
    /**
     * 日志时间
     */
    @Column(name = "log_time")
    val logTime: LocalDateTime
}
