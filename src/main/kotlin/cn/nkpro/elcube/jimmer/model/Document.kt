package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 单据实体
 */
@Entity
@Table(name = "nk_document")
interface Document : BusinessEntity {
    
    /**
     * 单据ID（业务主键）
     */
    @Key
    @Column(name = "doc_id")
    val docId: String
    
    /**
     * 单据编号
     */
    @Column(name = "doc_no")
    val docNo: String?
    
    /**
     * 单据名称
     */
    @Column(name = "doc_name")
    val docName: String?
    
    /**
     * 单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType
    
    /**
     * 当前状态
     */
    @ManyToOne
    @JoinColumn(name = "current_state_id")
    val currentState: DocState?
    
    /**
     * 前置单据ID
     */
    @Column(name = "pre_doc_id")
    val preDocId: String?
    
    /**
     * 前置单据
     */
    @ManyToOne
    @JoinColumn(name = "pre_doc_id", referencedColumnName = "doc_id")
    val preDocument: Document?
    
    /**
     * 后续单据
     */
    @OneToMany(mappedBy = "preDocument")
    val nextDocuments: List<Document>
    
    /**
     * 交易伙伴ID
     */
    @Column(name = "partner_id")
    val partnerId: String?
    
    /**
     * 交易伙伴名称
     */
    @Column(name = "partner_name")
    val partnerName: String?
    
    /**
     * 单据金额
     */
    @Column(name = "doc_amount")
    val docAmount: BigDecimal?
    
    /**
     * 币种
     */
    @Column(name = "currency")
    val currency: String?
    
    /**
     * 单据日期
     */
    @Column(name = "doc_date")
    val docDate: LocalDateTime?
    
    /**
     * 生效日期
     */
    @Column(name = "effective_date")
    val effectiveDate: LocalDateTime?
    
    /**
     * 到期日期
     */
    @Column(name = "expire_date")
    val expireDate: LocalDateTime?
    
    /**
     * 单据数据JSON
     */
    @Column(name = "doc_data")
    val docData: String?
    
    /**
     * 扩展属性JSON
     */
    @Column(name = "ext_props")
    val extProps: String?
    
    /**
     * 单据卡片数据
     */
    @OneToMany(mappedBy = "document")
    val cardData: List<DocumentCardData>
    
    /**
     * 单据历史记录
     */
    @OneToMany(mappedBy = "document")
    val histories: List<DocumentHistory>
    
    /**
     * 单据索引数据
     */
    @OneToMany(mappedBy = "document")
    val indexData: List<DocumentIndexData>
}

/**
 * 单据卡片数据
 */
@Entity
@Table(name = "nk_document_card_data")
interface DocumentCardData : BaseEntity {
    
    /**
     * 所属单据
     */
    @ManyToOne
    @JoinColumn(name = "document_id")
    val document: Document
    
    /**
     * 卡片定义
     */
    @ManyToOne
    @JoinColumn(name = "card_id")
    val card: DocCard
    
    /**
     * 卡片数据JSON
     */
    @Column(name = "card_data")
    val cardData: String?
    
    /**
     * 卡片状态
     */
    @Column(name = "card_status")
    val cardStatus: String?
}

/**
 * 单据历史记录
 */
@Entity
@Table(name = "nk_document_history")
interface DocumentHistory : BaseEntity {
    
    /**
     * 所属单据
     */
    @ManyToOne
    @JoinColumn(name = "document_id")
    val document: Document
    
    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    val operationType: String
    
    /**
     * 操作描述
     */
    @Column(name = "operation_desc")
    val operationDesc: String?
    
    /**
     * 操作前状态
     */
    @Column(name = "before_state")
    val beforeState: String?
    
    /**
     * 操作后状态
     */
    @Column(name = "after_state")
    val afterState: String?
    
    /**
     * 操作数据JSON
     */
    @Column(name = "operation_data")
    val operationData: String?
    
    /**
     * 操作人
     */
    @Column(name = "operator")
    val operator: String?
    
    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    val operationTime: LocalDateTime
}
