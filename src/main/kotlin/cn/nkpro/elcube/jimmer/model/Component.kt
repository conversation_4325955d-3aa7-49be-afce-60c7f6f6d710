package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*

/**
 * 组件定义
 */
@Entity
@Table(name = "nk_component")
interface Component : BusinessEntity {
    
    /**
     * 组件编码
     */
    @Key
    @Column(name = "component_code")
    val componentCode: String
    
    /**
     * 组件名称
     */
    @Column(name = "component_name")
    val componentName: String
    
    /**
     * 组件类型
     */
    @Column(name = "component_type")
    val componentType: String
    
    /**
     * 组件分类
     */
    @Column(name = "component_category")
    val componentCategory: String?
    
    /**
     * 组件版本
     */
    @Column(name = "component_version")
    val componentVersion: String
    
    /**
     * 组件描述
     */
    @Column(name = "component_desc")
    val componentDesc: String?
    
    /**
     * 组件图标
     */
    @Column(name = "component_icon")
    val componentIcon: String?
    
    /**
     * 是否系统内置
     */
    @Column(name = "system_built_in")
    val systemBuiltIn: Boolean
    
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    val enabled: Boolean
    
    /**
     * Groovy脚本
     */
    @Column(name = "groovy_script")
    val groovyScript: String?
    
    /**
     * Vue主组件
     */
    @Column(name = "vue_main")
    val vueMain: String?
    
    /**
     * Vue定义组件
     */
    @Column(name = "vue_defs")
    val vueDefs: String?
    
    /**
     * 组件配置Schema
     */
    @Column(name = "config_schema")
    val configSchema: String?
    
    /**
     * 组件依赖
     */
    @OneToMany(mappedBy = "component")
    val dependencies: List<ComponentDependency>
    
    /**
     * 组件版本历史
     */
    @OneToMany(mappedBy = "component")
    val versions: List<ComponentVersion>
}

/**
 * 组件依赖
 */
@Entity
@Table(name = "nk_component_dependency")
interface ComponentDependency : BaseEntity {
    
    /**
     * 组件
     */
    @ManyToOne
    @JoinColumn(name = "component_id")
    val component: Component
    
    /**
     * 依赖组件编码
     */
    @Column(name = "dependency_code")
    val dependencyCode: String
    
    /**
     * 依赖版本
     */
    @Column(name = "dependency_version")
    val dependencyVersion: String?
    
    /**
     * 依赖类型
     */
    @Column(name = "dependency_type")
    val dependencyType: String
}

/**
 * 组件版本
 */
@Entity
@Table(name = "nk_component_version")
interface ComponentVersion : BaseEntity {
    
    /**
     * 组件
     */
    @ManyToOne
    @JoinColumn(name = "component_id")
    val component: Component
    
    /**
     * 版本号
     */
    @Column(name = "version_number")
    val versionNumber: String
    
    /**
     * 版本描述
     */
    @Column(name = "version_desc")
    val versionDesc: String?
    
    /**
     * 是否当前版本
     */
    @Column(name = "is_current")
    val isCurrent: Boolean
    
    /**
     * 发布时间
     */
    @Column(name = "release_time")
    val releaseTime: java.time.LocalDateTime?
    
    /**
     * 变更日志
     */
    @Column(name = "change_log")
    val changeLog: String?
}

/**
 * 组件实例
 */
@Entity
@Table(name = "nk_component_instance")
interface ComponentInstance : BaseEntity {
    
    /**
     * 实例编码
     */
    @Key
    @Column(name = "instance_code")
    val instanceCode: String
    
    /**
     * 实例名称
     */
    @Column(name = "instance_name")
    val instanceName: String
    
    /**
     * 组件
     */
    @ManyToOne
    @JoinColumn(name = "component_id")
    val component: Component
    
    /**
     * 实例配置JSON
     */
    @Column(name = "instance_config")
    val instanceConfig: String?
    
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    val enabled: Boolean
    
    /**
     * 运行状态
     */
    @Column(name = "run_status")
    val runStatus: String?
    
    /**
     * 最后运行时间
     */
    @Column(name = "last_run_time")
    val lastRunTime: java.time.LocalDateTime?
    
    /**
     * 运行日志
     */
    @OneToMany(mappedBy = "instance")
    val runLogs: List<ComponentRunLog>
}

/**
 * 组件运行日志
 */
@Entity
@Table(name = "nk_component_run_log")
interface ComponentRunLog : BaseEntity {
    
    /**
     * 组件实例
     */
    @ManyToOne
    @JoinColumn(name = "instance_id")
    val instance: ComponentInstance
    
    /**
     * 日志级别
     */
    @Column(name = "log_level")
    val logLevel: String
    
    /**
     * 日志消息
     */
    @Column(name = "log_message")
    val logMessage: String
    
    /**
     * 异常堆栈
     */
    @Column(name = "exception_stack")
    val exceptionStack: String?
    
    /**
     * 日志时间
     */
    @Column(name = "log_time")
    val logTime: java.time.LocalDateTime
}
