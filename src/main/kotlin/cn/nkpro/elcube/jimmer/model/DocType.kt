package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*

/**
 * 单据类型定义
 */
@Entity
@Table(name = "nk_doc_type")
interface DocType : BusinessEntity {
    
    /**
     * 单据类型编码
     */
    @Key
    @Column(name = "doc_type")
    val docType: String
    
    /**
     * 单据类型名称
     */
    @Column(name = "doc_name")
    val docName: String
    
    /**
     * 单据描述
     */
    @Column(name = "doc_desc")
    val docDesc: String?
    
    /**
     * 单据图标
     */
    @Column(name = "doc_icon")
    val docIcon: String?
    
    /**
     * 单据颜色
     */
    @Column(name = "doc_color")
    val docColor: String?
    
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    val enabled: Boolean
    
    /**
     * 是否系统内置
     */
    @Column(name = "system_built_in")
    val systemBuiltIn: Boolean
    
    /**
     * 单据配置JSON
     */
    @Column(name = "doc_config")
    val docConfig: String?
    
    /**
     * 单据状态定义
     */
    @OneToMany(mappedBy = "docType")
    val states: List<DocState>
    
    /**
     * 单据卡片定义
     */
    @OneToMany(mappedBy = "docType")
    val cards: List<DocCard>
    
    /**
     * 单据索引定义
     */
    @OneToMany(mappedBy = "docType")
    val indexes: List<DocIndex>
    
    /**
     * 单据实例
     */
    @OneToMany(mappedBy = "docType")
    val documents: List<Document>
}

/**
 * 单据状态定义
 */
@Entity
@Table(name = "nk_doc_state")
interface DocState : BusinessEntity {
    
    /**
     * 状态编码
     */
    @Key
    @Column(name = "state_code")
    val stateCode: String
    
    /**
     * 状态名称
     */
    @Column(name = "state_name")
    val stateName: String
    
    /**
     * 状态描述
     */
    @Column(name = "state_desc")
    val stateDesc: String?
    
    /**
     * 状态颜色
     */
    @Column(name = "state_color")
    val stateColor: String?
    
    /**
     * 是否初始状态
     */
    @Column(name = "is_initial")
    val isInitial: Boolean
    
    /**
     * 是否终止状态
     */
    @Column(name = "is_final")
    val isFinal: Boolean
    
    /**
     * 所属单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType
    
    /**
     * 状态转换规则
     */
    @OneToMany(mappedBy = "fromState")
    val transitions: List<StateTransition>
}

/**
 * 状态转换规则
 */
@Entity
@Table(name = "nk_state_transition")
interface StateTransition : BaseEntity {
    
    /**
     * 源状态
     */
    @ManyToOne
    @JoinColumn(name = "from_state_id")
    val fromState: DocState
    
    /**
     * 目标状态
     */
    @ManyToOne
    @JoinColumn(name = "to_state_id")
    val toState: DocState
    
    /**
     * 转换条件
     */
    @Column(name = "condition_expr")
    val conditionExpr: String?
    
    /**
     * 转换动作
     */
    @Column(name = "action_expr")
    val actionExpr: String?
    
    /**
     * 是否需要审批
     */
    @Column(name = "need_approval")
    val needApproval: Boolean
    
    /**
     * 审批流程ID
     */
    @Column(name = "workflow_id")
    val workflowId: String?
}
