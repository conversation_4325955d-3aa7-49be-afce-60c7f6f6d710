package cn.nkpro.elcube.jimmer.model

import org.babyfish.jimmer.sql.*
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 单据索引定义
 */
@Entity
@Table(name = "nk_doc_index")
interface DocIndex : BusinessEntity {
    
    /**
     * 索引编码
     */
    @Key
    @Column(name = "index_code")
    val indexCode: String
    
    /**
     * 索引名称
     */
    @Column(name = "index_name")
    val indexName: String
    
    /**
     * 索引类型
     */
    @Column(name = "index_type")
    val indexType: String
    
    /**
     * 所属单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType
    
    /**
     * 提取表达式
     */
    @Column(name = "extract_expr")
    val extractExpr: String
    
    /**
     * 是否可搜索
     */
    @Column(name = "searchable")
    val searchable: Boolean
    
    /**
     * 是否可排序
     */
    @Column(name = "sortable")
    val sortable: Boolean
    
    /**
     * 是否可聚合
     */
    @Column(name = "aggregatable")
    val aggregatable: Boolean
    
    /**
     * 索引数据
     */
    @OneToMany(mappedBy = "index")
    val indexData: List<DocumentIndexData>
}

/**
 * 单据索引数据
 */
@Entity
@Table(name = "nk_document_index_data")
interface DocumentIndexData : BaseEntity {
    
    /**
     * 所属单据
     */
    @ManyToOne
    @JoinColumn(name = "document_id")
    val document: Document
    
    /**
     * 索引定义
     */
    @ManyToOne
    @JoinColumn(name = "index_id")
    val index: DocIndex
    
    /**
     * 字符串值
     */
    @Column(name = "string_value")
    val stringValue: String?
    
    /**
     * 数值
     */
    @Column(name = "number_value")
    val numberValue: BigDecimal?
    
    /**
     * 日期值
     */
    @Column(name = "date_value")
    val dateValue: LocalDateTime?
    
    /**
     * 布尔值
     */
    @Column(name = "boolean_value")
    val booleanValue: Boolean?
    
    /**
     * JSON值
     */
    @Column(name = "json_value")
    val jsonValue: String?
}

/**
 * 搜索配置
 */
@Entity
@Table(name = "nk_search_config")
interface SearchConfig : BusinessEntity {
    
    /**
     * 搜索配置编码
     */
    @Key
    @Column(name = "search_code")
    val searchCode: String
    
    /**
     * 搜索配置名称
     */
    @Column(name = "search_name")
    val searchName: String
    
    /**
     * 目标单据类型
     */
    @ManyToOne
    @JoinColumn(name = "doc_type_id")
    val docType: DocType
    
    /**
     * 搜索条件JSON
     */
    @Column(name = "search_conditions")
    val searchConditions: String?
    
    /**
     * 显示字段JSON
     */
    @Column(name = "display_fields")
    val displayFields: String?
    
    /**
     * 排序配置JSON
     */
    @Column(name = "sort_config")
    val sortConfig: String?
    
    /**
     * 是否公开
     */
    @Column(name = "is_public")
    val isPublic: Boolean
    
    /**
     * 搜索历史
     */
    @OneToMany(mappedBy = "searchConfig")
    val searchHistories: List<SearchHistory>
}

/**
 * 搜索历史
 */
@Entity
@Table(name = "nk_search_history")
interface SearchHistory : BaseEntity {
    
    /**
     * 搜索配置
     */
    @ManyToOne
    @JoinColumn(name = "search_config_id")
    val searchConfig: SearchConfig
    
    /**
     * 搜索条件JSON
     */
    @Column(name = "search_params")
    val searchParams: String?
    
    /**
     * 搜索结果数量
     */
    @Column(name = "result_count")
    val resultCount: Long?
    
    /**
     * 搜索耗时（毫秒）
     */
    @Column(name = "search_time")
    val searchTime: Long?
    
    /**
     * 搜索用户
     */
    @Column(name = "search_user")
    val searchUser: String?
}
