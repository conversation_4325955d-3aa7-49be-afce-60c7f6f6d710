package cn.nkpro.elcube.jimmer.annotation

import kotlin.reflect.KClass

/**
 * ELCube字段注解系统
 * 参考Erupt的优秀设计，结合Kotlin特性进行优化
 */

/**
 * ELCube字段注解 - 核心字段注解
 * 参考 @EruptField 设计
 */
@Target(AnnotationTarget.FIELD, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ElcubeField(
    /**
     * 列表视图配置
     */
    val views: Array<ViewConfig> = [],
    
    /**
     * 编辑配置
     */
    val edit: EditConfig = EditConfig(),
    
    /**
     * 搜索配置
     */
    val search: SearchConfig = SearchConfig(),
    
    /**
     * 显示顺序
     */
    val sort: Int = 1000,
    
    /**
     * 自定义扩展参数
     */
    val params: Array<KV> = []
)

/**
 * 视图配置注解 - 对应Erupt的@View
 */
annotation class ViewConfig(
    /**
     * 列标题
     */
    val title: String,
    
    /**
     * 描述
     */
    val desc: String = "",
    
    /**
     * 列宽度
     */
    val width: String = "",
    
    /**
     * 显示类型
     */
    val type: ViewType = ViewType.AUTO,
    
    /**
     * 是否显示
     */
    val show: Boolean = true,
    
    /**
     * 是否可排序
     */
    val sortable: Boolean = false,
    
    /**
     * 是否可导出
     */
    val export: Boolean = true,
    
    /**
     * 样式类名
     */
    val className: String = "",
    
    /**
     * 格式化模板
     */
    val template: String = "",
    
    /**
     * 对齐方式
     */
    val align: String = "left",
    
    /**
     * 是否固定列
     */
    val fixed: String = ""
)

/**
 * 编辑配置注解 - 对应Erupt的@Edit
 */
annotation class EditConfig(
    /**
     * 字段标题
     */
    val title: String = "",
    
    /**
     * 描述
     */
    val desc: String = "",
    
    /**
     * 是否必填
     */
    val notNull: Boolean = false,
    
    /**
     * 是否显示
     */
    val show: Boolean = true,
    
    /**
     * 是否只读
     */
    val readonly: ReadonlyConfig = ReadonlyConfig(),
    
    /**
     * 占位符
     */
    val placeholder: String = "",
    
    /**
     * 编辑类型
     */
    val type: EditType = EditType.AUTO,
    
    /**
     * 输入框配置
     */
    val inputType: InputTypeConfig = InputTypeConfig(),
    
    /**
     * 数字输入配置
     */
    val numberType: NumberTypeConfig = NumberTypeConfig(),
    
    /**
     * 日期配置
     */
    val dateType: DateTypeConfig = DateTypeConfig(),
    
    /**
     * 选择配置
     */
    val choiceType: ChoiceTypeConfig = ChoiceTypeConfig(),
    
    /**
     * 多选配置
     */
    val multiChoiceType: MultiChoiceTypeConfig = MultiChoiceTypeConfig(),
    
    /**
     * 布尔配置
     */
    val boolType: BoolTypeConfig = BoolTypeConfig(),
    
    /**
     * 引用表格配置
     */
    val referenceTableType: ReferenceTableTypeConfig = ReferenceTableTypeConfig(),
    
    /**
     * 附件上传配置
     */
    val attachmentType: AttachmentTypeConfig = AttachmentTypeConfig()
)

/**
 * 搜索配置注解
 */
annotation class SearchConfig(
    /**
     * 是否启用搜索
     */
    val enable: Boolean = false,
    
    /**
     * 是否模糊搜索
     */
    val vague: Boolean = false,
    
    /**
     * 搜索操作符
     */
    val operator: String = "eq"
)

/**
 * 只读配置
 */
annotation class ReadonlyConfig(
    /**
     * 新增时只读
     */
    val add: Boolean = false,
    
    /**
     * 编辑时只读
     */
    val edit: Boolean = false
)

/**
 * 视图类型枚举
 */
enum class ViewType {
    AUTO,           // 自动检测
    TEXT,           // 文本
    NUMBER,         // 数字
    DATE,           // 日期
    BOOLEAN,        // 布尔
    IMAGE,          // 图片
    LINK,           // 链接
    TAG,            // 标签
    PROGRESS,       // 进度条
    RATE,           // 评分
    COLOR,          // 颜色
    CURRENCY,       // 货币
    PERCENTAGE      // 百分比
}

/**
 * 编辑类型枚举 - 参考Erupt的EditType
 */
enum class EditType {
    AUTO,               // 自动检测
    INPUT,              // 文本输入
    TEXTAREA,           // 多行文本
    NUMBER,             // 数字输入
    SLIDER,             // 滑块
    DATE,               // 日期选择
    DATETIME,           // 日期时间选择
    TIME,               // 时间选择
    BOOLEAN,            // 布尔开关
    CHOICE,             // 单选下拉
    MULTI_CHOICE,       // 多选下拉
    RADIO,              // 单选按钮
    CHECKBOX,           // 多选框
    TAGS,               // 标签选择
    COLOR,              // 颜色选择
    RATE,               // 评分
    ATTACHMENT,         // 附件上传
    IMAGE,              // 图片上传
    HTML_EDITOR,        // 富文本编辑器
    CODE_EDITOR,        // 代码编辑器
    MARKDOWN,           // Markdown编辑器
    MAP,                // 地图选择
    REFERENCE_TABLE,    // 引用表格
    REFERENCE_TREE,     // 引用树
    HIDDEN,             // 隐藏
    DIVIDE,             // 分割线
    EMPTY               // 空占位
}

/**
 * 输入框类型配置
 */
annotation class InputTypeConfig(
    /**
     * 最大长度
     */
    val length: Int = 255,
    
    /**
     * 输入类型
     */
    val type: String = "text",
    
    /**
     * 是否占满整行
     */
    val fullSpan: Boolean = false,
    
    /**
     * 正则表达式
     */
    val regex: String = "",
    
    /**
     * 自动去除空格
     */
    val autoTrim: Boolean = true,
    
    /**
     * 前缀
     */
    val prefix: String = "",
    
    /**
     * 后缀
     */
    val suffix: String = ""
)

/**
 * 数字类型配置
 */
annotation class NumberTypeConfig(
    /**
     * 最大值
     */
    val max: Long = Long.MAX_VALUE,
    
    /**
     * 最小值
     */
    val min: Long = Long.MIN_VALUE,
    
    /**
     * 精度
     */
    val precision: Int = 2,
    
    /**
     * 步长
     */
    val step: Double = 1.0
)

/**
 * 日期类型配置
 */
annotation class DateTypeConfig(
    /**
     * 日期类型
     */
    val type: String = "date",
    
    /**
     * 日期格式
     */
    val format: String = "YYYY-MM-DD",
    
    /**
     * 是否显示时间
     */
    val showTime: Boolean = false
)

/**
 * 选择类型配置
 */
annotation class ChoiceTypeConfig(
    /**
     * 选项值
     */
    val vl: Array<VL> = [],
    
    /**
     * 数据源
     */
    val dataSource: String = "",
    
    /**
     * 值字段
     */
    val valueField: String = "value",
    
    /**
     * 标签字段
     */
    val labelField: String = "label"
)

/**
 * 多选类型配置
 */
annotation class MultiChoiceTypeConfig(
    /**
     * 选项值
     */
    val vl: Array<VL> = [],
    
    /**
     * 数据源
     */
    val dataSource: String = "",
    
    /**
     * 值字段
     */
    val valueField: String = "value",
    
    /**
     * 标签字段
     */
    val labelField: String = "label"
)

/**
 * 布尔类型配置
 */
annotation class BoolTypeConfig(
    /**
     * 真值标签
     */
    val trueText: String = "是",
    
    /**
     * 假值标签
     */
    val falseText: String = "否"
)

/**
 * 引用表格类型配置
 */
annotation class ReferenceTableTypeConfig(
    /**
     * 引用的实体类
     */
    val entity: KClass<*> = Nothing::class,
    
    /**
     * 显示字段
     */
    val displayField: String = "",
    
    /**
     * 值字段
     */
    val valueField: String = "id"
)

/**
 * 附件类型配置
 */
annotation class AttachmentTypeConfig(
    /**
     * 文件类型限制
     */
    val accept: String = "*",
    
    /**
     * 是否多文件
     */
    val multiple: Boolean = false,
    
    /**
     * 最大文件大小(MB)
     */
    val maxSize: Int = 10
)

/**
 * 值标签对
 */
annotation class VL(
    val value: String,
    val label: String
)

/**
 * 键值对
 */
annotation class KV(
    val key: String,
    val value: String
)
