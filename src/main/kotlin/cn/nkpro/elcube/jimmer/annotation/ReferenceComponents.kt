package cn.nkpro.elcube.jimmer.annotation

import kotlin.reflect.KClass

/**
 * 对象引用组件注解
 * 类似 Erupt 的对象组件功能，支持各种引用类型
 */

/**
 * 引用显示模式
 */
enum class ReferenceDisplayMode {
    SELECT,         // 下拉选择
    MODAL,          // 弹窗选择
    AUTOCOMPLETE,   // 自动完成
    TREE_SELECT,    // 树形选择
    CASCADER,       // 级联选择
    TRANSFER,       // 穿梭框
    TABLE_SELECT    // 表格选择
}

/**
 * 引用数据源类型
 */
enum class ReferenceDataSource {
    ENTITY,         // 实体数据源
    ENUM,           // 枚举数据源
    DICT,           // 字典数据源
    API,            // API数据源
    STATIC          // 静态数据源
}

/**
 * 多对一表引用
 * 用于引用其他实体的单个记录
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ManyToOneReference(
    /**
     * 引用的目标实体类
     */
    val targetEntity: KClass<*>,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.SELECT,
    
    /**
     * 显示字段（在目标实体中用于显示的字段）
     */
    val displayField: String = "name",
    
    /**
     * 值字段（在目标实体中用作值的字段，通常是ID）
     */
    val valueField: String = "id",
    
    /**
     * 查询条件（过滤数据的条件）
     */
    val condition: String = "",
    
    /**
     * 排序字段
     */
    val orderBy: String = "",
    
    /**
     * 是否允许清空
     */
    val allowClear: Boolean = true,
    
    /**
     * 占位符文本
     */
    val placeholder: String = "请选择",
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 搜索字段
     */
    val searchFields: Array<String> = [],
    
    /**
     * 依赖字段（当依赖字段变化时重新加载数据）
     */
    val dependsOn: Array<String> = [],
    
    /**
     * 是否懒加载
     */
    val lazy: Boolean = false
)

/**
 * 多对一树引用
 * 用于引用树形结构实体的单个节点
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ManyToOneTreeReference(
    /**
     * 引用的目标实体类
     */
    val targetEntity: KClass<*>,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.TREE_SELECT,
    
    /**
     * 显示字段
     */
    val displayField: String = "name",
    
    /**
     * 值字段
     */
    val valueField: String = "id",
    
    /**
     * 父级字段（在目标实体中表示父级关系的字段）
     */
    val parentField: String = "parentId",
    
    /**
     * 子级字段（在目标实体中表示子级关系的字段）
     */
    val childrenField: String = "children",
    
    /**
     * 根节点条件
     */
    val rootCondition: String = "parentId IS NULL",
    
    /**
     * 查询条件
     */
    val condition: String = "",
    
    /**
     * 是否显示根节点
     */
    val showRoot: Boolean = true,
    
    /**
     * 是否只能选择叶子节点
     */
    val leafOnly: Boolean = false,
    
    /**
     * 是否支持多选（树形多选）
     */
    val multiple: Boolean = false,
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 是否展开所有节点
     */
    val expandAll: Boolean = false,
    
    /**
     * 默认展开层级
     */
    val defaultExpandLevel: Int = 1
)

/**
 * 多对多表引用
 * 用于引用其他实体的多个记录
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ManyToManyReference(
    /**
     * 引用的目标实体类
     */
    val targetEntity: KClass<*>,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.TRANSFER,
    
    /**
     * 显示字段
     */
    val displayField: String = "name",
    
    /**
     * 值字段
     */
    val valueField: String = "id",
    
    /**
     * 中间表名（多对多关联表）
     */
    val joinTable: String = "",
    
    /**
     * 中间表中指向当前实体的外键字段
     */
    val joinColumn: String = "",
    
    /**
     * 中间表中指向目标实体的外键字段
     */
    val inverseJoinColumn: String = "",
    
    /**
     * 查询条件
     */
    val condition: String = "",
    
    /**
     * 排序字段
     */
    val orderBy: String = "",
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 搜索字段
     */
    val searchFields: Array<String> = [],
    
    /**
     * 最大选择数量
     */
    val maxCount: Int = -1,
    
    /**
     * 最小选择数量
     */
    val minCount: Int = 0,
    
    /**
     * 穿梭框标题
     */
    val titles: Array<String> = ["可选项", "已选项"],
    
    /**
     * 是否显示搜索框
     */
    val showSearch: Boolean = true
)

/**
 * 多对多树引用
 * 用于引用树形结构实体的多个节点
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ManyToManyTreeReference(
    /**
     * 引用的目标实体类
     */
    val targetEntity: KClass<*>,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.TREE_SELECT,
    
    /**
     * 显示字段
     */
    val displayField: String = "name",
    
    /**
     * 值字段
     */
    val valueField: String = "id",
    
    /**
     * 父级字段
     */
    val parentField: String = "parentId",
    
    /**
     * 子级字段
     */
    val childrenField: String = "children",
    
    /**
     * 根节点条件
     */
    val rootCondition: String = "parentId IS NULL",
    
    /**
     * 中间表名
     */
    val joinTable: String = "",
    
    /**
     * 中间表中指向当前实体的外键字段
     */
    val joinColumn: String = "",
    
    /**
     * 中间表中指向目标实体的外键字段
     */
    val inverseJoinColumn: String = "",
    
    /**
     * 查询条件
     */
    val condition: String = "",
    
    /**
     * 是否显示根节点
     */
    val showRoot: Boolean = true,
    
    /**
     * 是否只能选择叶子节点
     */
    val leafOnly: Boolean = false,
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 是否展开所有节点
     */
    val expandAll: Boolean = false,
    
    /**
     * 默认展开层级
     */
    val defaultExpandLevel: Int = 1,
    
    /**
     * 最大选择数量
     */
    val maxCount: Int = -1,
    
    /**
     * 最小选择数量
     */
    val minCount: Int = 0,
    
    /**
     * 是否支持父子关联选择
     */
    val checkStrictly: Boolean = false
)

/**
 * 级联引用
 * 用于级联选择（如省市区选择）
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class CascaderReference(
    /**
     * 引用的目标实体类
     */
    val targetEntity: KClass<*>,
    
    /**
     * 显示字段
     */
    val displayField: String = "name",
    
    /**
     * 值字段
     */
    val valueField: String = "id",
    
    /**
     * 父级字段
     */
    val parentField: String = "parentId",
    
    /**
     * 层级字段（表示层级的字段）
     */
    val levelField: String = "level",
    
    /**
     * 根节点条件
     */
    val rootCondition: String = "parentId IS NULL",
    
    /**
     * 查询条件
     */
    val condition: String = "",
    
    /**
     * 是否只能选择叶子节点
     */
    val leafOnly: Boolean = true,
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 占位符文本
     */
    val placeholder: String = "请选择",
    
    /**
     * 是否允许清空
     */
    val allowClear: Boolean = true,
    
    /**
     * 分隔符（用于显示选择路径）
     */
    val separator: String = " / ",
    
    /**
     * 是否展示完整路径
     */
    val showFullPath: Boolean = true
)

/**
 * 字典引用
 * 用于引用字典数据
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class DictReference(
    /**
     * 字典编码
     */
    val dictCode: String,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.SELECT,
    
    /**
     * 是否允许清空
     */
    val allowClear: Boolean = true,
    
    /**
     * 占位符文本
     */
    val placeholder: String = "请选择",
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = false,
    
    /**
     * 是否支持多选
     */
    val multiple: Boolean = false
)

/**
 * API引用
 * 用于通过API获取引用数据
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiReference(
    /**
     * API地址
     */
    val apiUrl: String,
    
    /**
     * 显示模式
     */
    val displayMode: ReferenceDisplayMode = ReferenceDisplayMode.SELECT,
    
    /**
     * 显示字段
     */
    val displayField: String = "label",
    
    /**
     * 值字段
     */
    val valueField: String = "value",
    
    /**
     * 请求方法
     */
    val method: String = "GET",
    
    /**
     * 请求参数
     */
    val params: Array<String> = [],
    
    /**
     * 是否支持搜索
     */
    val searchable: Boolean = true,
    
    /**
     * 搜索参数名
     */
    val searchParam: String = "keyword",
    
    /**
     * 是否懒加载
     */
    val lazy: Boolean = true,
    
    /**
     * 缓存时间（秒）
     */
    val cacheSeconds: Int = 300
)

/**
 * 引用配置
 * 用于统一配置引用组件的行为
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class ReferenceConfig(
    /**
     * 默认分页大小
     */
    val defaultPageSize: Int = 20,
    
    /**
     * 最大分页大小
     */
    val maxPageSize: Int = 100,
    
    /**
     * 是否启用缓存
     */
    val enableCache: Boolean = true,
    
    /**
     * 默认缓存时间（秒）
     */
    val defaultCacheSeconds: Int = 300,
    
    /**
     * 是否启用权限检查
     */
    val enablePermissionCheck: Boolean = true
)
