package cn.nkpro.elcube.jimmer.annotation

import cn.nkpro.elcube.jimmer.schema.*
import cn.nkpro.elcube.jimmer.schema.page.AbstractEditSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractListSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractViewSchema
import cn.nkpro.elcube.jimmer.schema.page.Edit
import cn.nkpro.elcube.jimmer.schema.page.Search
import cn.nkpro.elcube.jimmer.schema.page.ViewSchemaDsl
import org.springframework.stereotype.Component
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties

/**
 * 注解处理器
 * 将@ElcubeField注解转换为Schema DSL
 */
@Component
class AnnotationProcessor {
    
    /**
     * 从注解生成列表Schema
     */
    fun generateListSchemaFromAnnotations(modelClass: KClass<*>): Search.() -> Unit = {
        val properties = modelClass.memberProperties
        
        properties.forEach { property ->
            val elcubeField = property.findAnnotation<ElcubeField>()
            if (elcubeField != null && elcubeField.views.isNotEmpty()) {
                elcubeField.views.forEach { viewConfig ->
                    if (viewConfig.show) {
                        column(property.name) {
                            label = viewConfig.title
                            width = viewConfig.width.takeIf { it.isNotEmpty() }
                            align = viewConfig.align
                            sortable = viewConfig.sortable
                            filterable = true
                            visible = viewConfig.show
                            format = viewConfig.template.takeIf { it.isNotEmpty() }
                            className = viewConfig.className.takeIf { it.isNotEmpty() }
                            fixed = viewConfig.fixed.takeIf { it.isNotEmpty() }
                        }
                    }
                }
                
                // 添加搜索字段
                if (elcubeField.search.enable) {
                    searchField(property.name) {
                        label = elcubeField.views.firstOrNull()?.title ?: property.name
                        operator = elcubeField.search.operator
                        
                        when (elcubeField.edit.type) {
                            EditType.INPUT, EditType.TEXTAREA -> input()
                            EditType.NUMBER -> numberRange()
                            EditType.DATE -> dateRange()
                            EditType.CHOICE -> select("", "value", "label")
                            else -> input()
                        }
                    }
                }
            }
        }
        
        // 添加标准动作
        action("view") {
            label = "查看"
            icon = "el-icon-view"
            type = "link"
            position = "row"
        }
        
        action("edit") {
            label = "编辑"
            icon = "el-icon-edit"
            type = "link"
            position = "row"
        }
        
        action("delete") {
            label = "删除"
            icon = "el-icon-delete"
            type = "link"
            position = "row"
            danger()
            needConfirm("确定要删除吗？")
        }
    }
    
    /**
     * 从注解生成查看Schema
     */
    fun generateViewSchemaFromAnnotations(modelClass: KClass<*>): ViewSchemaDsl.() -> Unit = {
        val properties = modelClass.memberProperties
        
        section("basic") {
            title = "基本信息"
            
            properties.forEach { property ->
                val elcubeField = property.findAnnotation<ElcubeField>()
                if (elcubeField != null && elcubeField.edit.show) {
                    field(property.name) {
                        label = elcubeField.edit.title.takeIf { it.isNotEmpty() } 
                            ?: elcubeField.views.firstOrNull()?.title 
                            ?: property.name
                        readonly = true
                        
                        // 根据类型设置格式
                        when (elcubeField.edit.type) {
                            EditType.DATE -> date()
                            EditType.DATETIME -> datetime()
                            EditType.NUMBER -> {
                                if (property.name.contains("amount", true) || 
                                    property.name.contains("price", true)) {
                                    currency("¥")
                                }
                            }
                            EditType.BOOLEAN -> {}
                            else -> {}
                        }
                    }
                }
            }
        }
        
        // 添加标准动作
        action("edit") {
            label = "编辑"
            icon = "el-icon-edit"
            primary()
        }
        
        action("print") {
            label = "打印"
            icon = "el-icon-printer"
        }
    }
    
    /**
     * 从注解生成编辑Schema
     */
    fun generateEditSchemaFromAnnotations(modelClass: KClass<*>): Edit.() -> Unit = {
        val properties = modelClass.memberProperties
        
        section("basic") {
            title = "基本信息"
            
            properties.forEach { property ->
                val elcubeField = property.findAnnotation<ElcubeField>()
                if (elcubeField != null && elcubeField.edit.show) {
                    field(property.name) {
                        label = elcubeField.edit.title.takeIf { it.isNotEmpty() } 
                            ?: elcubeField.views.firstOrNull()?.title 
                            ?: property.name
                        required = elcubeField.edit.notNull
                        readonly = elcubeField.edit.readonly.add || elcubeField.edit.readonly.edit
                        placeholder = elcubeField.edit.placeholder
                        
                        // 根据编辑类型配置字段
                        when (elcubeField.edit.type) {
                            EditType.INPUT -> {
                                val inputConfig = elcubeField.edit.inputType
                                input(inputConfig.regex, inputConfig.length)
                            }
                            EditType.TEXTAREA -> textarea()
                            EditType.NUMBER -> {
                                val numberConfig = elcubeField.edit.numberType
                                number(numberConfig.min, numberConfig.max, numberConfig.precision)
                            }
                            EditType.DATE -> {
                                val dateConfig = elcubeField.edit.dateType
                                date(dateConfig.format)
                            }
                            EditType.DATETIME -> {
                                val dateConfig = elcubeField.edit.dateType
                                datetime(dateConfig.format)
                            }
                            EditType.BOOLEAN -> switch()
                            EditType.CHOICE -> {
                                val choiceConfig = elcubeField.edit.choiceType
                                if (choiceConfig.dataSource.isNotEmpty()) {
                                    select(choiceConfig.dataSource, choiceConfig.valueField, choiceConfig.labelField)
                                } else if (choiceConfig.vl.isNotEmpty()) {
                                    radio(choiceConfig.vl.map { it.value to it.label })
                                }
                            }
                            EditType.MULTI_CHOICE -> {
                                val multiChoiceConfig = elcubeField.edit.multiChoiceType
                                if (multiChoiceConfig.dataSource.isNotEmpty()) {
                                    multiSelect(multiChoiceConfig.dataSource, multiChoiceConfig.valueField, multiChoiceConfig.labelField)
                                } else if (multiChoiceConfig.vl.isNotEmpty()) {
                                    checkbox(multiChoiceConfig.vl.map { it.value to it.label })
                                }
                            }
                            EditType.ATTACHMENT -> {
                                val attachmentConfig = elcubeField.edit.attachmentType
                                upload(attachmentConfig.accept, attachmentConfig.multiple)
                            }
                            EditType.HIDDEN -> {
                                visible = false
                            }
                            else -> input()
                        }
                    }
                    
                    // 添加验证规则
                    if (elcubeField.edit.notNull) {
                        validation(property.name) {
                            required("${elcubeField.edit.title}不能为空")
                        }
                    }
                    
                    // 根据类型添加特定验证
                    when (elcubeField.edit.type) {
                        EditType.INPUT -> {
                            val inputConfig = elcubeField.edit.inputType
                            if (inputConfig.regex.isNotEmpty()) {
                                validation(property.name) {
                                    pattern(inputConfig.regex, "格式不正确")
                                }
                            }
                            if (inputConfig.length > 0) {
                                validation(property.name) {
                                    maxLength(inputConfig.length)
                                }
                            }
                        }
                        EditType.NUMBER -> {
                            val numberConfig = elcubeField.edit.numberType
                            validation(property.name) {
                                custom("min:${numberConfig.min}", "不能小于${numberConfig.min}")
                                custom("max:${numberConfig.max}", "不能大于${numberConfig.max}")
                            }
                        }
                        else -> {}
                    }
                }
            }
        }
        
        // 添加标准动作
        action("save") {
            label = "保存"
            icon = "el-icon-check"
            primary()
        }
        
        action("cancel") {
            label = "取消"
            icon = "el-icon-close"
        }
    }
}

/**
 * 注解驱动的Schema生成器
 */
@Component
class AnnotationSchemaGenerator(
    private val annotationProcessor: AnnotationProcessor
) {
    
    /**
     * 为指定模型类生成注解驱动的Schema
     */
    fun generateSchemasForModel(modelClass: KClass<*>): AnnotationDrivenSchemas {
        return AnnotationDrivenSchemas(
            modelClass = modelClass,
            listSchemaDefinition = annotationProcessor.generateListSchemaFromAnnotations(modelClass),
            viewSchemaDefinition = annotationProcessor.generateViewSchemaFromAnnotations(modelClass),
            editSchemaDefinition = annotationProcessor.generateEditSchemaFromAnnotations(modelClass)
        )
    }
}

/**
 * 注解驱动的Schema定义
 */
data class AnnotationDrivenSchemas(
    val modelClass: KClass<*>,
    val listSchemaDefinition: Search.() -> Unit,
    val viewSchemaDefinition: ViewSchemaDsl.() -> Unit,
    val editSchemaDefinition: Edit.() -> Unit
)

/**
 * 注解驱动的列表Schema
 */
class AnnotationDrivenListSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>,
    private val schemaDefinition: Search.() -> Unit
) : AbstractListSchema<T>(schemaId, schemaName, modelClass) {
    
    override fun defineList(): Search.() -> Unit = schemaDefinition
}

/**
 * 注解驱动的查看Schema
 */
class AnnotationDrivenViewSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>,
    private val schemaDefinition: ViewSchemaDsl.() -> Unit
) : AbstractViewSchema<T>(schemaId, schemaName, modelClass) {
    
    override fun defineView(): ViewSchemaDsl.() -> Unit = schemaDefinition
}

/**
 * 注解驱动的编辑Schema
 */
class AnnotationDrivenEditSchema<T : Any>(
    schemaId: String,
    schemaName: String,
    modelClass: KClass<T>,
    private val schemaDefinition: Edit.() -> Unit
) : AbstractEditSchema<T>(schemaId, schemaName, modelClass) {
    
    override fun defineEdit(): Edit.() -> Unit = schemaDefinition
}
