# ELCube 完整DSL设计方案

## 概述

这是一个完整的DSL设计方案，用于表达ELCube对象的STATE_FLOW（状态流）、BIZ_FLOW（业务流）和UI的关系。该DSL具有以下特点：

- **统一性**：一个DSL描述对象的所有方面
- **类型安全**：基于Kotlin的强类型系统
- **可扩展性**：支持自定义组件和扩展
- **声明式**：通过配置而非编程实现业务逻辑
- **响应式**：UI与数据状态自动同步

## 核心设计理念

### 1. 对象驱动设计

每个ELCube对象都是一个完整的业务实体，包含：
- **数据模型**：对象的属性和结构
- **状态流**：对象的生命周期管理
- **业务流**：对象间的协作关系
- **用户界面**：对象的展示和交互
- **权限控制**：对象的访问控制

### 2. 状态驱动UI

UI的显示和行为根据对象的状态动态调整：
```kotlin
state("DRAFT") {
    fieldVisible("orderNo", "false") // 草稿状态不显示订单号
    fieldReadonly("orderDate", "false") // 可以修改订单日期
    
    action("submit") {
        condition = "order.totalAmount > 0"
        targetState = "PENDING_APPROVAL"
    }
}
```

### 3. 数据绑定机制

UI组件与数据源自动绑定：
```kotlin
field("customerId") {
    select("customers", "id", "name") // 绑定到customers数据源
    visibleWhen = "order.type == 'EXTERNAL'" // 条件显示
}

dataSource("customers") {
    api("/api/customers")
    cacheEnabled = true
}
```

## DSL结构说明

### 主要组成部分

```kotlin
elcubeObject("SALES_ORDER") {
    // 1. 基本信息
    objectName = "销售订单"
    objectType = "DOCUMENT"
    
    // 2. 状态流定义
    stateFlow { ... }
    
    // 3. 业务流定义  
    bizFlow { ... }
    
    // 4. UI定义
    ui { ... }
    
    // 5. 数据源定义
    dataSource("name") { ... }
    
    // 6. 权限定义
    permission("role") { ... }
}
```

### 状态流DSL

定义对象的生命周期和状态转换：

```kotlin
stateFlow {
    state("DRAFT") {
        stateName = "草稿"
        isInitial = true
        
        // UI配置
        fieldVisible("field1", "condition")
        fieldReadonly("field2", "condition")
        
        // 状态动作
        action("submit") {
            actionLabel = "提交"
            targetState = "PENDING"
            condition = "validation.passed"
        }
    }
    
    // 状态转换
    transition("DRAFT", "PENDING") {
        condition = "data.valid"
        needApproval = true
    }
}
```

### 业务流DSL

定义对象间的协作关系：

```kotlin
bizFlow {
    flowCode = "SALES_PROCESS"
    autoStart = true
    
    // 上游对象
    upstream("QUOTATION") {
        condition = "quotation.accepted"
        dataMapping = "{ customerId: quotation.customerId }"
    }
    
    // 下游对象
    downstream("DELIVERY") {
        condition = "order.confirmed"
        autoTrigger = true
        delayMinutes = 60
    }
}
```

### UI DSL

定义用户界面的布局和交互：

```kotlin
ui {
    layout("main") {
        grid(12) {
            section("header") {
                title = "基本信息"
                span = 12
                
                field("orderNo") {
                    label = "订单编号"
                    readonly = true
                    span = 6
                }
                
                field("customerId") {
                    label = "客户"
                    select("customers", "id", "name")
                    required = true
                    span = 6
                }
            }
        }
    }
    
    component("orderItems") {
        table {
            column("productCode") {
                label = "产品编码"
                editable("select", "products")
            }
            
            column("quantity") {
                label = "数量"
                editable("number")
                format("#,##0.00")
            }
        }
    }
}
```

## 核心特性

### 1. 条件表达式系统

支持丰富的条件表达式来控制UI行为：

```kotlin
// 字段可见性
visibleWhen = "order.type == 'EXTERNAL' && user.hasRole('SALES')"

// 字段只读性
readonlyWhen = "order.status != 'DRAFT' || !user.canEdit"

// 动作条件
condition = "order.totalAmount > 0 && order.items.size() > 0"
```

### 2. 数据源绑定

支持多种数据源类型：

```kotlin
// API数据源
dataSource("customers") {
    api("/api/customers") {
        param("status", "ACTIVE")
        header("Authorization", "Bearer #{token}")
    }
    cacheEnabled = true
}

// SQL数据源
dataSource("products") {
    sql("SELECT id, name FROM products WHERE status = 'ACTIVE'")
}

// 静态数据源
dataSource("priorities") {
    static(listOf(
        mapOf("value" to "HIGH", "label" to "高"),
        mapOf("value" to "LOW", "label" to "低")
    ))
}

// 表达式数据源
dataSource("orderItems") {
    expression("order.items")
}
```

### 3. 组件扩展机制

支持自定义UI组件：

```kotlin
// 高级表格
component("salesTable") {
    table {
        virtualScroll = true
        groupBy("region", "salesRep")
        
        column("amount") {
            progressBar(100, "#67C23A")
            conditionalFormat {
                condition = "value > 100000"
                highlight("#FFF2E8", "#FA8C16")
            }
        }
        
        toolbarAction("export") {
            label = "导出"
            handler = "exportData()"
        }
    }
}

// 仪表板
component("dashboard") {
    dashboard {
        widget("totalSales") {
            metric("#{data.total}", "万元", "#{data.trend}")
        }
        
        widget("chart") {
            chart("line") {
                series("sales") {
                    dataField = "amount"
                }
            }
        }
    }
}
```

### 4. 权限控制

细粒度的权限控制：

```kotlin
permission("SALES_REP") {
    canRead = true
    canWrite = true
    
    field("totalAmount") {
        editable = false
        condition = "order.status == 'DRAFT'"
    }
    
    action("approve", false)
    action("submit", true)
}
```

## 使用示例

### 完整的销售订单定义

```kotlin
val salesOrder = elcubeObject("SALES_ORDER") {
    objectName = "销售订单"
    
    stateFlow {
        linearStates(
            "DRAFT" to "草稿",
            "PENDING" to "待审核", 
            "APPROVED" to "已审核",
            "COMPLETED" to "已完成"
        )
        
        state("DRAFT") {
            fieldVisible("orderNo", "false")
            action("submit") {
                targetState = "PENDING"
                condition = "order.valid"
            }
        }
    }
    
    bizFlow {
        upstream("QUOTATION") {
            condition = "quotation.accepted"
        }
        downstream("DELIVERY") {
            condition = "order.approved"
            autoTrigger = true
        }
    }
    
    ui {
        layout("main") {
            section("header") {
                field("customerId") {
                    select("customers")
                    required = true
                }
                field("orderDate") {
                    datePicker()
                    required = true
                }
            }
        }
        
        component("items") {
            table {
                column("productCode") {
                    editable("select", "products")
                }
                column("quantity") {
                    editable("number")
                }
            }
        }
    }
    
    dataSource("customers") {
        api("/api/customers")
    }
    
    permission("SALES_REP") {
        canRead = true
        canWrite = true
        action("submit", true)
    }
}
```

## 扩展能力

### 1. 自定义组件

可以轻松扩展新的UI组件类型：

```kotlin
// 自定义地图组件
fun mapComponent(name: String, init: MapComponentDsl.() -> Unit) {
    // 实现地图组件DSL
}

// 自定义图表组件  
fun customChart(name: String, init: CustomChartDsl.() -> Unit) {
    // 实现自定义图表DSL
}
```

### 2. 自定义字段类型

支持扩展新的字段类型：

```kotlin
field("location") {
    fieldType = "MAP_PICKER" // 自定义字段类型
    ui("mapType", "baidu")
    ui("zoom", 15)
}
```

### 3. 自定义表达式函数

可以注册自定义的表达式函数：

```kotlin
// 注册自定义函数
registerFunction("calculateTax") { amount, rate ->
    amount * rate / 100
}

// 在表达式中使用
field("tax") {
    expression = "calculateTax(order.amount, order.taxRate)"
}
```

## 总结

这个DSL设计方案提供了：

1. **完整性**：覆盖了ELCube对象的所有方面
2. **一致性**：统一的语法和概念模型
3. **灵活性**：支持复杂的业务场景
4. **可扩展性**：易于添加新功能和组件
5. **类型安全**：编译时检查，减少错误
6. **声明式**：通过配置实现业务逻辑

通过这个DSL，开发者可以用声明式的方式定义复杂的业务对象，包括其状态流转、业务协作和用户界面，大大提高了开发效率和代码质量。
