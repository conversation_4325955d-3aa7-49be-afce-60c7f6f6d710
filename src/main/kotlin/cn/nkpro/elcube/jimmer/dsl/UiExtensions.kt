package cn.nkpro.elcube.jimmer.dsl

/**
 * UI扩展DSL，支持更丰富的UI组件和交互
 */

/**
 * 扩展UI组件DSL
 */
@ElcubeObjectDslMarker
class ExtendedUiDsl {
    
    /**
     * 高级表格组件
     */
    fun advancedTable(name: String, init: AdvancedTableDsl.() -> Unit) {
        val tableDsl = AdvancedTableDsl().apply {
            componentName = name
            init()
        }
        // 注册到组件系统
    }
    
    /**
     * 仪表板组件
     */
    fun dashboard(name: String, init: DashboardDsl.() -> Unit) {
        val dashboardDsl = DashboardDsl().apply {
            componentName = name
            init()
        }
    }
    
    /**
     * 工作流图组件
     */
    fun workflowDiagram(name: String, init: WorkflowDiagramDsl.() -> Unit) {
        val workflowDsl = WorkflowDiagramDsl().apply {
            componentName = name
            init()
        }
    }
    
    /**
     * 甘特图组件
     */
    fun ganttChart(name: String, init: GanttChartDsl.() -> Unit) {
        val ganttDsl = GanttChartDsl().apply {
            componentName = name
            init()
        }
    }
}

/**
 * 高级表格DSL
 */
@ElcubeObjectDslMarker
class AdvancedTableDsl {
    var componentName: String = ""
    var virtualScroll: Boolean = false
    var treeData: Boolean = false
    var grouping: Boolean = false
    var pivotMode: Boolean = false
    var exportEnabled: Boolean = true
    var importEnabled: Boolean = false
    
    private val columns = mutableListOf<AdvancedColumnDsl>()
    private val toolbarActions = mutableListOf<ToolbarActionDsl>()
    private val contextMenus = mutableListOf<ContextMenuDsl>()
    private val aggregations = mutableListOf<AggregationDsl>()
    
    fun column(field: String, init: AdvancedColumnDsl.() -> Unit) {
        val columnDsl = AdvancedColumnDsl().apply {
            fieldPath = field
            init()
        }
        columns.add(columnDsl)
    }
    
    fun toolbarAction(name: String, init: ToolbarActionDsl.() -> Unit) {
        val actionDsl = ToolbarActionDsl().apply {
            actionName = name
            init()
        }
        toolbarActions.add(actionDsl)
    }
    
    fun contextMenu(name: String, init: ContextMenuDsl.() -> Unit) {
        val menuDsl = ContextMenuDsl().apply {
            menuName = name
            init()
        }
        contextMenus.add(menuDsl)
    }
    
    fun aggregation(field: String, init: AggregationDsl.() -> Unit) {
        val aggDsl = AggregationDsl().apply {
            fieldPath = field
            init()
        }
        aggregations.add(aggDsl)
    }
    
    /**
     * 启用树形数据
     */
    fun treeData(childrenField: String = "children", hasChildrenField: String = "hasChildren") {
        treeData = true
        config("childrenField", childrenField)
        config("hasChildrenField", hasChildrenField)
    }
    
    /**
     * 启用分组
     */
    fun groupBy(vararg fields: String) {
        grouping = true
        config("groupFields", fields.toList())
    }
    
    /**
     * 启用透视表模式
     */
    fun pivotTable(rowFields: List<String>, columnFields: List<String>, valueFields: List<String>) {
        pivotMode = true
        config("pivotConfig", mapOf(
            "rowFields" to rowFields,
            "columnFields" to columnFields,
            "valueFields" to valueFields
        ))
    }
    
    private val config = mutableMapOf<String, Any>()
    
    fun config(key: String, value: Any) {
        config[key] = value
    }
}

/**
 * 高级列DSL
 */
@ElcubeObjectDslMarker
class AdvancedColumnDsl : TableColumnDsl() {
    var pinned: Boolean = false
    var groupable: Boolean = true
    var pivotable: Boolean = true
    var aggregatable: Boolean = false
    var cellRenderer: String? = null
    var headerRenderer: String? = null
    var cellEditor: String? = null
    
    // 条件格式化
    private val conditionalFormats = mutableListOf<ConditionalFormatDsl>()
    
    fun conditionalFormat(init: ConditionalFormatDsl.() -> Unit) {
        val formatDsl = ConditionalFormatDsl().apply(init)
        conditionalFormats.add(formatDsl)
    }
    
    /**
     * 自定义单元格渲染器
     */
    fun cellRenderer(renderer: String, config: Map<String, Any> = emptyMap()) {
        cellRenderer = renderer
        config("cellRendererConfig", config)
    }
    
    /**
     * 进度条渲染器
     */
    fun progressBar(max: Number = 100, color: String = "#409EFF") {
        cellRenderer("ProgressBarRenderer")
        config("progressConfig", mapOf("max" to max, "color" to color))
    }
    
    /**
     * 标签渲染器
     */
    fun tags(colorMapping: Map<String, String> = emptyMap()) {
        cellRenderer("TagRenderer")
        config("tagConfig", mapOf("colorMapping" to colorMapping))
    }
    
    /**
     * 图片渲染器
     */
    fun image(width: String = "50px", height: String = "50px") {
        cellRenderer("ImageRenderer")
        config("imageConfig", mapOf("width" to width, "height" to height))
    }
    
    /**
     * 链接渲染器
     */
    fun link(urlTemplate: String, target: String = "_blank") {
        cellRenderer("LinkRenderer")
        config("linkConfig", mapOf("urlTemplate" to urlTemplate, "target" to target))
    }
}

/**
 * 条件格式化DSL
 */
@ElcubeObjectDslMarker
class ConditionalFormatDsl {
    var condition: String = ""
    var backgroundColor: String? = null
    var textColor: String? = null
    var fontWeight: String? = null
    var icon: String? = null
    
    fun highlight(bgColor: String, textColor: String = "#FFFFFF") {
        backgroundColor = bgColor
        this.textColor = textColor
    }
    
    fun bold() {
        fontWeight = "bold"
    }
    
    fun icon(iconClass: String) {
        icon = iconClass
    }
}

/**
 * 工具栏动作DSL
 */
@ElcubeObjectDslMarker
class ToolbarActionDsl {
    var actionName: String = ""
    var label: String = ""
    var icon: String? = null
    var type: String = "button" // button, dropdown, separator
    var position: String = "left" // left, right
    var condition: String? = null
    var handler: String? = null
    
    private val dropdownItems = mutableListOf<DropdownItemDsl>()
    
    fun dropdownItem(name: String, init: DropdownItemDsl.() -> Unit) {
        val itemDsl = DropdownItemDsl().apply {
            itemName = name
            init()
        }
        dropdownItems.add(itemDsl)
    }
}

/**
 * 下拉菜单项DSL
 */
@ElcubeObjectDslMarker
class DropdownItemDsl {
    var itemName: String = ""
    var label: String = ""
    var icon: String? = null
    var condition: String? = null
    var handler: String? = null
    var separator: Boolean = false
}

/**
 * 右键菜单DSL
 */
@ElcubeObjectDslMarker
class ContextMenuDsl {
    var menuName: String = ""
    var condition: String? = null
    
    private val items = mutableListOf<ContextMenuItemDsl>()
    
    fun item(name: String, init: ContextMenuItemDsl.() -> Unit) {
        val itemDsl = ContextMenuItemDsl().apply {
            itemName = name
            init()
        }
        items.add(itemDsl)
    }
}

/**
 * 右键菜单项DSL
 */
@ElcubeObjectDslMarker
class ContextMenuItemDsl {
    var itemName: String = ""
    var label: String = ""
    var icon: String? = null
    var condition: String? = null
    var handler: String? = null
    var separator: Boolean = false
}

/**
 * 聚合DSL
 */
@ElcubeObjectDslMarker
class AggregationDsl {
    var fieldPath: String = ""
    var function: String = "sum" // sum, avg, count, min, max
    var label: String? = null
    var formatter: String? = null
    var position: String = "footer" // footer, header, both
}

/**
 * 仪表板DSL
 */
@ElcubeObjectDslMarker
class DashboardDsl {
    var componentName: String = ""
    var layout: String = "grid" // grid, masonry, flex
    var columns: Int = 12
    var gap: String = "16px"
    var responsive: Boolean = true
    
    private val widgets = mutableListOf<WidgetDsl>()
    private val filters = mutableListOf<FilterDsl>()
    
    fun widget(name: String, init: WidgetDsl.() -> Unit) {
        val widgetDsl = WidgetDsl().apply {
            widgetName = name
            init()
        }
        widgets.add(widgetDsl)
    }
    
    fun filter(name: String, init: FilterDsl.() -> Unit) {
        val filterDsl = FilterDsl().apply {
            filterName = name
            init()
        }
        filters.add(filterDsl)
    }
}

/**
 * 仪表板小部件DSL
 */
@ElcubeObjectDslMarker
class WidgetDsl {
    var widgetName: String = ""
    var title: String = ""
    var type: String = "chart" // chart, metric, table, text
    var span: Int = 6
    var height: String = "300px"
    var refreshInterval: Int = 0 // 自动刷新间隔（秒）
    var dataSource: String? = null
    
    private val config = mutableMapOf<String, Any>()
    
    fun config(key: String, value: Any) {
        config[key] = value
    }
    
    /**
     * 指标卡片
     */
    fun metric(value: String, unit: String? = null, trend: String? = null) {
        type = "metric"
        config("value", value)
        unit?.let { config("unit", it) }
        trend?.let { config("trend", it) }
    }
    
    /**
     * 图表配置
     */
    fun chart(chartType: String, init: ChartDsl.() -> Unit) {
        type = "chart"
        val chartDsl = ChartDsl().apply {
            this.chartType = chartType
            init()
        }
        config("chartConfig", chartDsl)
    }
}

/**
 * 过滤器DSL
 */
@ElcubeObjectDslMarker
class FilterDsl {
    var filterName: String = ""
    var label: String = ""
    var type: String = "select" // select, date, daterange, input, number
    var dataSource: String? = null
    var defaultValue: Any? = null
    var multiple: Boolean = false
    
    private val config = mutableMapOf<String, Any>()
    
    fun config(key: String, value: Any) {
        config[key] = value
    }
}

/**
 * 工作流图DSL
 */
@ElcubeObjectDslMarker
class WorkflowDiagramDsl {
    var componentName: String = ""
    var readonly: Boolean = false
    var showGrid: Boolean = true
    var snapToGrid: Boolean = true
    var zoomEnabled: Boolean = true
    var panEnabled: Boolean = true
    
    private val nodeTypes = mutableListOf<NodeTypeDsl>()
    private val connectionTypes = mutableListOf<ConnectionTypeDsl>()
    
    fun nodeType(name: String, init: NodeTypeDsl.() -> Unit) {
        val nodeTypeDsl = NodeTypeDsl().apply {
            typeName = name
            init()
        }
        nodeTypes.add(nodeTypeDsl)
    }
    
    fun connectionType(name: String, init: ConnectionTypeDsl.() -> Unit) {
        val connectionTypeDsl = ConnectionTypeDsl().apply {
            typeName = name
            init()
        }
        connectionTypes.add(connectionTypeDsl)
    }
}

/**
 * 节点类型DSL
 */
@ElcubeObjectDslMarker
class NodeTypeDsl {
    var typeName: String = ""
    var shape: String = "rectangle" // rectangle, circle, diamond, ellipse
    var width: Int = 120
    var height: Int = 60
    var backgroundColor: String = "#FFFFFF"
    var borderColor: String = "#CCCCCC"
    var textColor: String = "#333333"
    var icon: String? = null
}

/**
 * 连接类型DSL
 */
@ElcubeObjectDslMarker
class ConnectionTypeDsl {
    var typeName: String = ""
    var style: String = "solid" // solid, dashed, dotted
    var color: String = "#666666"
    var width: Int = 2
    var arrowType: String = "arrow" // arrow, diamond, circle, none
}

/**
 * 甘特图DSL
 */
@ElcubeObjectDslMarker
class GanttChartDsl {
    var componentName: String = ""
    var timeScale: String = "day" // hour, day, week, month
    var showWeekends: Boolean = true
    var showToday: Boolean = true
    var editable: Boolean = false
    var zoomEnabled: Boolean = true
    
    private val columns = mutableListOf<GanttColumnDsl>()
    private val taskTypes = mutableListOf<TaskTypeDsl>()
    
    fun column(field: String, init: GanttColumnDsl.() -> Unit) {
        val columnDsl = GanttColumnDsl().apply {
            fieldPath = field
            init()
        }
        columns.add(columnDsl)
    }
    
    fun taskType(name: String, init: TaskTypeDsl.() -> Unit) {
        val taskTypeDsl = TaskTypeDsl().apply {
            typeName = name
            init()
        }
        taskTypes.add(taskTypeDsl)
    }
}

/**
 * 甘特图列DSL
 */
@ElcubeObjectDslMarker
class GanttColumnDsl {
    var fieldPath: String = ""
    var label: String = ""
    var width: String = "120px"
    var resizable: Boolean = true
    var tree: Boolean = false
}

/**
 * 任务类型DSL
 */
@ElcubeObjectDslMarker
class TaskTypeDsl {
    var typeName: String = ""
    var color: String = "#409EFF"
    var progressColor: String = "#67C23A"
    var height: Int = 20
    var shape: String = "rectangle" // rectangle, milestone, summary
}
