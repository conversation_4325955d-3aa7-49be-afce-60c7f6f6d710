package cn.nkpro.elcube.jimmer.relation

import cn.nkpro.elcube.jimmer.annotation.Model
import cn.nkpro.elcube.jimmer.schema.AbstractLinkDefinition
import cn.nkpro.elcube.jimmer.schema.LinkDsl
import org.springframework.stereotype.Component
import org.springframework.context.ApplicationContext
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 单据关联关系解析器
 * 基于 LinkDefinition 自动推导单据间的关联关系
 */

/**
 * 关联关系信息
 */
data class DocumentRelationInfo(
    val sourceDocumentType: KClass<*>,     // 来源单据类型
    val targetDocumentType: KClass<*>,     // 目标单据类型
    val relationName: String,              // 关系名称
    val description: String,               // 关系描述
    val direction: RelationDirection,      // 关系方向
    val fieldMappings: List<FieldMappingInfo>, // 字段映射信息
    val conditions: List<String>,          // 生成条件
    val validations: List<String>          // 验证规则
)

/**
 * 字段映射信息
 */
data class FieldMappingInfo(
    val sourceField: String,
    val targetField: String,
    val mappingType: String,
    val transformer: String?
)

/**
 * 关系方向
 */
enum class RelationDirection {
    FORWARD,   // 正向：A → B
    BACKWARD   // 反向：B ← A
}

/**
 * 关联单据查询结果
 */
data class RelatedDocument(
    val documentType: String,
    val documentNo: String,
    val documentId: String,
    val status: String,
    val statusName: String,
    val amount: BigDecimal,
    val createDate: LocalDateTime,
    val remark: String?,
    val relationName: String,
    val direction: RelationDirection
)

/**
 * 单据关联关系解析器
 */
@Component
class DocumentRelationResolver(
    private val applicationContext: ApplicationContext
) {
    
    private val relationCache = mutableMapOf<KClass<*>, List<DocumentRelationInfo>>()
    
    /**
     * 获取指定单据类型的所有关联关系
     */
    fun getDocumentRelations(documentType: KClass<*>): List<DocumentRelationInfo> {
        return relationCache.getOrPut(documentType) {
            resolveDocumentRelations(documentType)
        }
    }
    
    /**
     * 解析单据的关联关系
     */
    private fun resolveDocumentRelations(documentType: KClass<*>): List<DocumentRelationInfo> {
        val relations = mutableListOf<DocumentRelationInfo>()
        
        // 1. 从当前单据的 LinkDefinition 解析正向关系
        val forwardRelations = resolveForwardRelations(documentType)
        relations.addAll(forwardRelations)
        
        // 2. 从其他单据的 LinkDefinition 解析反向关系
        val backwardRelations = resolveBackwardRelations(documentType)
        relations.addAll(backwardRelations)
        
        return relations
    }
    
    /**
     * 解析正向关系（当前单据 → 其他单据）
     */
    private fun resolveForwardRelations(documentType: KClass<*>): List<DocumentRelationInfo> {
        val relations = mutableListOf<DocumentRelationInfo>()
        
        // 获取当前单据的 @Model 注解
        val modelAnnotation = documentType.findAnnotation<Model>()
        val linkDefinitionClass = modelAnnotation?.link
        
        if (linkDefinitionClass != null) {
            // 获取 LinkDefinition 实例
            val linkDefinition = applicationContext.getBean(linkDefinitionClass.java) as AbstractLinkDefinition
            
            // 解析 LinkDefinition 中定义的规则
            val linkDsl = LinkDsl()
            linkDefinition.defineLink().invoke(linkDsl)
            
            // 从 LinkDsl 中提取关联关系信息
            val linkRules = extractLinkRules(linkDsl)
            
            linkRules.forEach { rule ->
                if (rule.sourceType == documentType) {
                    relations.add(DocumentRelationInfo(
                        sourceDocumentType = rule.sourceType,
                        targetDocumentType = rule.targetType,
                        relationName = rule.ruleName,
                        description = rule.description,
                        direction = RelationDirection.FORWARD,
                        fieldMappings = rule.fieldMappings,
                        conditions = rule.conditions,
                        validations = rule.validations
                    ))
                }
            }
        }
        
        return relations
    }
    
    /**
     * 解析反向关系（其他单据 → 当前单据）
     */
    private fun resolveBackwardRelations(documentType: KClass<*>): List<DocumentRelationInfo> {
        val relations = mutableListOf<DocumentRelationInfo>()
        
        // 获取所有带有 @Model 注解的类
        val allDocumentTypes = getAllDocumentTypes()
        
        allDocumentTypes.forEach { otherDocumentType ->
            if (otherDocumentType != documentType) {
                val otherModelAnnotation = otherDocumentType.findAnnotation<Model>()
                val otherLinkDefinitionClass = otherModelAnnotation?.link
                
                if (otherLinkDefinitionClass != null) {
                    val otherLinkDefinition = applicationContext.getBean(otherLinkDefinitionClass.java) as AbstractLinkDefinition
                    val otherLinkDsl = LinkDsl()
                    otherLinkDefinition.defineLink().invoke(otherLinkDsl)
                    
                    val otherLinkRules = extractLinkRules(otherLinkDsl)
                    
                    otherLinkRules.forEach { rule ->
                        if (rule.targetType == documentType) {
                            relations.add(DocumentRelationInfo(
                                sourceDocumentType = rule.sourceType,
                                targetDocumentType = rule.targetType,
                                relationName = rule.ruleName,
                                description = rule.description,
                                direction = RelationDirection.BACKWARD,
                                fieldMappings = rule.fieldMappings,
                                conditions = rule.conditions,
                                validations = rule.validations
                            ))
                        }
                    }
                }
            }
        }
        
        return relations
    }
    
    /**
     * 从 LinkDsl 中提取链接规则信息
     */
    private fun extractLinkRules(linkDsl: LinkDsl): List<LinkRuleInfo> {
        // 这里需要访问 LinkDsl 的内部状态
        // 实际实现需要在 LinkDsl 中提供相应的访问方法
        return linkDsl.getLinkRules().map { rule ->
            LinkRuleInfo(
                ruleName = rule.ruleName,
                description = rule.description,
                sourceType = rule.sourceType,
                targetType = rule.targetType,
                fieldMappings = rule.getFieldMappings().map { mapping ->
                    FieldMappingInfo(
                        sourceField = mapping.sourceField?.name ?: "",
                        targetField = mapping.targetField?.name ?: "",
                        mappingType = mapping.mappingType,
                        transformer = mapping.transformer?.toString()
                    )
                },
                conditions = rule.getConditions(),
                validations = rule.getValidations()
            )
        }
    }
    
    /**
     * 获取所有单据类型
     */
    private fun getAllDocumentTypes(): List<KClass<*>> {
        // 这里可以通过扫描包路径或者其他方式获取所有带有 @Model 注解的类
        // 简化实现，实际可以使用 Spring 的 ClassPathScanningCandidateComponentProvider
        return listOf(
            // 这里应该动态获取所有单据类型
        )
    }
}

/**
 * 链接规则信息（内部使用）
 */
data class LinkRuleInfo(
    val ruleName: String,
    val description: String,
    val sourceType: KClass<*>,
    val targetType: KClass<*>,
    val fieldMappings: List<FieldMappingInfo>,
    val conditions: List<String>,
    val validations: List<String>
)

/**
 * 通用关联单据查询服务
 */
@Component
class UniversalRelatedDocumentService(
    private val documentRelationResolver: DocumentRelationResolver,
    private val applicationContext: ApplicationContext
) {
    
    /**
     * 查询指定单据的所有关联单据
     */
    fun findRelatedDocuments(documentType: KClass<*>, documentId: String): List<RelatedDocument> {
        val relations = documentRelationResolver.getDocumentRelations(documentType)
        val relatedDocuments = mutableListOf<RelatedDocument>()
        
        relations.forEach { relation ->
            when (relation.direction) {
                RelationDirection.FORWARD -> {
                    // 查询正向关联的单据（当前单据生成的下游单据）
                    val downstreamDocs = findDownstreamDocuments(relation, documentId)
                    relatedDocuments.addAll(downstreamDocs)
                }
                RelationDirection.BACKWARD -> {
                    // 查询反向关联的单据（生成当前单据的上游单据）
                    val upstreamDocs = findUpstreamDocuments(relation, documentId)
                    relatedDocuments.addAll(upstreamDocs)
                }
            }
        }
        
        return relatedDocuments
    }
    
    /**
     * 查询下游单据
     */
    private fun findDownstreamDocuments(relation: DocumentRelationInfo, sourceDocumentId: String): List<RelatedDocument> {
        // 根据关联关系信息，动态构建查询
        // 这里需要使用 Jimmer 的动态查询能力
        
        // 示例实现（需要根据实际的 Repository 和查询逻辑调整）
        return emptyList()
    }
    
    /**
     * 查询上游单据
     */
    private fun findUpstreamDocuments(relation: DocumentRelationInfo, targetDocumentId: String): List<RelatedDocument> {
        // 根据关联关系信息，动态构建查询
        // 这里需要使用 Jimmer 的动态查询能力
        
        // 示例实现（需要根据实际的 Repository 和查询逻辑调整）
        return emptyList()
    }
    
    /**
     * 获取单据的关联关系摘要
     */
    fun getRelationSummary(documentType: KClass<*>): Map<String, Any> {
        val relations = documentRelationResolver.getDocumentRelations(documentType)
        
        return mapOf(
            "documentType" to documentType.simpleName,
            "totalRelations" to relations.size,
            "forwardRelations" to relations.count { it.direction == RelationDirection.FORWARD },
            "backwardRelations" to relations.count { it.direction == RelationDirection.BACKWARD },
            "relations" to relations.map { relation ->
                mapOf(
                    "name" to relation.relationName,
                    "description" to relation.description,
                    "direction" to relation.direction.name,
                    "targetType" to when (relation.direction) {
                        RelationDirection.FORWARD -> relation.targetDocumentType.simpleName
                        RelationDirection.BACKWARD -> relation.sourceDocumentType.simpleName
                    }
                )
            }
        )
    }
}

/**
 * 关联单据查询 REST API
 */
@Component
class UniversalRelatedDocumentController(
    private val universalRelatedDocumentService: UniversalRelatedDocumentService
) {
    
    /**
     * 查询指定单据的关联单据
     * GET /api/universal-relations/{documentType}/{documentId}
     */
    fun getRelatedDocuments(documentType: String, documentId: String): List<RelatedDocument> {
        val docType = Class.forName(documentType).kotlin
        return universalRelatedDocumentService.findRelatedDocuments(docType, documentId)
    }
    
    /**
     * 获取单据类型的关联关系摘要
     * GET /api/universal-relations/{documentType}/summary
     */
    fun getRelationSummary(documentType: String): Map<String, Any> {
        val docType = Class.forName(documentType).kotlin
        return universalRelatedDocumentService.getRelationSummary(docType)
    }
}
