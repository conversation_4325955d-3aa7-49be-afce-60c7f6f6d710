package cn.nkpro.elcube.jimmer.enums

/**
 * 状态枚举系统
 * 避免魔法字符串，提供类型安全的状态定义
 */

/**
 * 基础状态接口
 */
interface BaseStatus {
    val code: String
    val name: String
    val color: String?
    val isInitial: <PERSON><PERSON><PERSON> get() = false
    val isFinal: <PERSON><PERSON><PERSON> get() = false
}

/**
 * 销售订单状态枚举
 */
enum class SalesOrderStatus(
    override val code: String,
    override val name: String,
    override val color: String? = null,
    override val isInitial: Boolean = false,
    override val isFinal: Boolean = false
) : BaseStatus {
    
    DRAFT("DRAFT", "草稿", "#909399", isInitial = true),
    PENDING("PENDING", "待审核", "#E6A23C"),
    APPROVED("APPROVED", "已审核", "#67C23A"),
    SHIPPED("SHIPPED", "已发货", "#409EFF"),
    COMPLETED("COMPLETED", "已完成", "#67C23A", isFinal = true),
    CANCELLED("CANCELLED", "已取消", "#F56C6C", isFinal = true);
    
    companion object {
        fun fromCode(code: String): SalesOrderStatus? {
            return values().find { it.code == code }
        }
        
        fun getInitialStatus(): SalesOrderStatus {
            return values().find { it.isInitial } ?: DRAFT
        }
        
        fun getFinalStatuses(): List<SalesOrderStatus> {
            return values().filter { it.isFinal }
        }
        
        fun getActiveStatuses(): List<SalesOrderStatus> {
            return values().filter { !it.isFinal }
        }
    }
}

/**
 * 发货单状态枚举
 */
enum class ShipmentStatus(
    override val code: String,
    override val name: String,
    override val color: String? = null,
    override val isInitial: Boolean = false,
    override val isFinal: Boolean = false
) : BaseStatus {
    
    DRAFT("DRAFT", "草稿", "#909399", isInitial = true),
    PLANNED("PLANNED", "已计划", "#E6A23C"),
    SHIPPED("SHIPPED", "已发货", "#409EFF"),
    DELIVERED("DELIVERED", "已送达", "#67C23A", isFinal = true),
    CANCELLED("CANCELLED", "已取消", "#F56C6C", isFinal = true);
    
    companion object {
        fun fromCode(code: String): ShipmentStatus? {
            return values().find { it.code == code }
        }
        
        fun getInitialStatus(): ShipmentStatus {
            return values().find { it.isInitial } ?: DRAFT
        }
    }
}

/**
 * 发票状态枚举
 */
enum class InvoiceStatus(
    override val code: String,
    override val name: String,
    override val color: String? = null,
    override val isInitial: Boolean = false,
    override val isFinal: Boolean = false
) : BaseStatus {
    
    DRAFT("DRAFT", "草稿", "#909399", isInitial = true),
    ISSUED("ISSUED", "已开具", "#409EFF"),
    PAID("PAID", "已付款", "#67C23A", isFinal = true),
    CANCELLED("CANCELLED", "已取消", "#F56C6C", isFinal = true);
    
    companion object {
        fun fromCode(code: String): InvoiceStatus? {
            return values().find { it.code == code }
        }
    }
}

/**
 * 优先级枚举
 */
enum class Priority(
    val code: String,
    val name: String,
    val level: Int
) {
    LOW("LOW", "低", 1),
    MEDIUM("MEDIUM", "中", 2),
    HIGH("HIGH", "高", 3),
    URGENT("URGENT", "紧急", 4);
    
    companion object {
        fun fromCode(code: String): Priority? {
            return values().find { it.code == code }
        }
        
        fun getHighPriorities(): List<Priority> {
            return values().filter { it.level >= 3 }
        }
    }
}

/**
 * 付款状态枚举
 */
enum class PaymentStatus(
    val code: String,
    val name: String
) {
    UNPAID("UNPAID", "未付款"),
    PARTIAL("PARTIAL", "部分付款"),
    PAID("PAID", "已付款"),
    OVERDUE("OVERDUE", "逾期");
    
    companion object {
        fun fromCode(code: String): PaymentStatus? {
            return values().find { it.code == code }
        }
        
        fun getPaidStatuses(): List<PaymentStatus> {
            return listOf(PARTIAL, PAID)
        }
    }
}

/**
 * 发票类型枚举
 */
enum class InvoiceType(
    val code: String,
    val name: String
) {
    DIRECT("DIRECT", "直接开票"),
    AFTER_SHIPMENT("AFTER_SHIPMENT", "发货后开票"),
    MONTHLY("MONTHLY", "月结开票");
    
    companion object {
        fun fromCode(code: String): InvoiceType? {
            return values().find { it.code == code }
        }
    }
}

/**
 * 发货类型枚举
 */
enum class ShipmentType(
    val code: String,
    val name: String
) {
    SALES("SALES", "销售发货"),
    TRANSFER("TRANSFER", "调拨发货"),
    RETURN("RETURN", "退货发货");
    
    companion object {
        fun fromCode(code: String): ShipmentType? {
            return values().find { it.code == code }
        }
    }
}

/**
 * 状态转换规则
 */
object StatusTransitionRules {
    
    /**
     * 销售订单状态转换规则
     */
    val salesOrderTransitions = mapOf(
        SalesOrderStatus.DRAFT to listOf(SalesOrderStatus.PENDING, SalesOrderStatus.CANCELLED),
        SalesOrderStatus.PENDING to listOf(SalesOrderStatus.APPROVED, SalesOrderStatus.DRAFT),
        SalesOrderStatus.APPROVED to listOf(SalesOrderStatus.SHIPPED, SalesOrderStatus.CANCELLED),
        SalesOrderStatus.SHIPPED to listOf(SalesOrderStatus.COMPLETED),
        SalesOrderStatus.COMPLETED to emptyList(),
        SalesOrderStatus.CANCELLED to emptyList()
    )
    
    /**
     * 发货单状态转换规则
     */
    val shipmentTransitions = mapOf(
        ShipmentStatus.DRAFT to listOf(ShipmentStatus.PLANNED, ShipmentStatus.CANCELLED),
        ShipmentStatus.PLANNED to listOf(ShipmentStatus.SHIPPED, ShipmentStatus.CANCELLED),
        ShipmentStatus.SHIPPED to listOf(ShipmentStatus.DELIVERED),
        ShipmentStatus.DELIVERED to emptyList(),
        ShipmentStatus.CANCELLED to emptyList()
    )
    
    /**
     * 检查状态转换是否有效
     */
    fun isValidTransition(from: BaseStatus, to: BaseStatus): Boolean {
        return when (from) {
            is SalesOrderStatus -> {
                to is SalesOrderStatus && salesOrderTransitions[from]?.contains(to) == true
            }
            is ShipmentStatus -> {
                to is ShipmentStatus && shipmentTransitions[from]?.contains(to) == true
            }
            else -> false
        }
    }
    
    /**
     * 获取可转换的状态列表
     */
    fun getValidNextStatuses(currentStatus: BaseStatus): List<BaseStatus> {
        return when (currentStatus) {
            is SalesOrderStatus -> salesOrderTransitions[currentStatus] ?: emptyList()
            is ShipmentStatus -> shipmentTransitions[currentStatus] ?: emptyList()
            else -> emptyList()
        }
    }
}

/**
 * 状态工具类
 */
object StatusUtils {
    
    /**
     * 检查状态是否可以执行某个操作
     */
    fun canExecuteAction(status: BaseStatus, action: String): Boolean {
        return when (status) {
            is SalesOrderStatus -> {
                when (action) {
                    "edit" -> status in listOf(SalesOrderStatus.DRAFT, SalesOrderStatus.PENDING)
                    "submit" -> status == SalesOrderStatus.DRAFT
                    "approve" -> status == SalesOrderStatus.PENDING
                    "ship" -> status == SalesOrderStatus.APPROVED
                    "complete" -> status == SalesOrderStatus.SHIPPED
                    "cancel" -> status in listOf(SalesOrderStatus.DRAFT, SalesOrderStatus.PENDING, SalesOrderStatus.APPROVED)
                    else -> false
                }
            }
            is ShipmentStatus -> {
                when (action) {
                    "edit" -> status in listOf(ShipmentStatus.DRAFT, ShipmentStatus.PLANNED)
                    "plan" -> status == ShipmentStatus.DRAFT
                    "ship" -> status == ShipmentStatus.PLANNED
                    "deliver" -> status == ShipmentStatus.SHIPPED
                    "cancel" -> status in listOf(ShipmentStatus.DRAFT, ShipmentStatus.PLANNED)
                    else -> false
                }
            }
            else -> false
        }
    }
    
    /**
     * 获取状态的显示样式
     */
    fun getStatusStyle(status: BaseStatus): Map<String, String> {
        return mapOf(
            "color" to (status.color ?: "#909399"),
            "backgroundColor" to "${status.color ?: "#909399"}20",
            "borderColor" to (status.color ?: "#909399")
        )
    }
    
    /**
     * 检查是否可以生成下游单据
     */
    fun canGenerateDownstream(status: BaseStatus, targetType: String): Boolean {
        return when (status) {
            is SalesOrderStatus -> {
                when (targetType) {
                    "Shipment" -> status == SalesOrderStatus.APPROVED
                    "Invoice" -> status in listOf(SalesOrderStatus.APPROVED, SalesOrderStatus.SHIPPED)
                    else -> false
                }
            }
            is ShipmentStatus -> {
                when (targetType) {
                    "Invoice" -> status == ShipmentStatus.SHIPPED
                    else -> false
                }
            }
            else -> false
        }
    }
}
