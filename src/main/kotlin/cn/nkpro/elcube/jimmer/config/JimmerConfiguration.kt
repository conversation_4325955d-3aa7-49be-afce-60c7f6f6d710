package cn.nkpro.elcube.jimmer.config

import cn.nkpro.elcube.jimmer.component.ComponentFactory
import cn.nkpro.elcube.jimmer.component.DefaultComponentFactory
import cn.nkpro.elcube.jimmer.engine.CardEngine
import cn.nkpro.elcube.jimmer.engine.DocumentEngine
import cn.nkpro.elcube.jimmer.engine.DocumentEngineImpl
import cn.nkpro.elcube.jimmer.engine.ExpressionEngine
import org.babyfish.jimmer.spring.repository.config.EnableJimmerRepositories
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.transaction.annotation.EnableTransactionManagement

/**
 * Jimmer配置类
 */
@Configuration
@EnableJimmerRepositories(basePackages = ["cn.nkpro.elcube.jimmer.repository"])
@EnableTransactionManagement
class JimmerConfiguration {
    
    /**
     * 组件工厂Bean
     */
    @Bean
    @ConditionalOnMissingBean
    fun componentFactory(): ComponentFactory {
        return DefaultComponentFactory()
    }
    
    /**
     * 卡片引擎Bean
     */
    @Bean
    @ConditionalOnMissingBean
    fun cardEngine(
        componentFactory: ComponentFactory,
        expressionEngine: ExpressionEngine
    ): CardEngine {
        return DefaultCardEngine(componentFactory, expressionEngine)
    }
    
    /**
     * 表达式引擎Bean
     */
    @Bean
    @ConditionalOnMissingBean
    fun expressionEngine(): ExpressionEngine {
        return DefaultExpressionEngine()
    }
}

/**
 * 默认卡片引擎实现
 */
class DefaultCardEngine(
    private val componentFactory: ComponentFactory,
    private val expressionEngine: ExpressionEngine
) : CardEngine {
    
    override suspend fun calculateCard(
        context: cn.nkpro.elcube.jimmer.engine.DocumentContext,
        card: cn.nkpro.elcube.jimmer.model.DocCard,
        options: Map<String, Any>
    ) {
        val component = componentFactory.getComponent(card.componentName)
        if (component != null) {
            // 获取卡片数据
            val cardData = context.getCardData<Any>(card.cardCode)
            
            // 解析卡片配置
            val cardConfig = parseCardConfig(card.cardConfig)
            
            // 执行卡片计算
            // 这里需要根据具体的组件类型进行类型转换和计算
            // 由于泛型擦除，这里使用反射或者其他方式处理
            
            // 简化实现，实际需要更复杂的类型处理
            try {
                val method = component.javaClass.getMethod(
                    "calculate",
                    cn.nkpro.elcube.jimmer.engine.DocumentContext::class.java,
                    Any::class.java,
                    Any::class.java,
                    Boolean::class.java,
                    Map::class.java
                )
                
                val result = method.invoke(component, context, cardData, cardConfig, false, options)
                context.setCardData(card.cardCode, result)
            } catch (e: Exception) {
                // 处理异常
                println("卡片计算失败: ${card.cardCode}, 错误: ${e.message}")
            }
        }
    }
    
    private fun parseCardConfig(configJson: String?): Any {
        // 解析JSON配置为对象
        // 这里需要根据具体的配置格式进行解析
        return configJson?.let {
            // 使用Jackson或其他JSON库解析
            mapOf<String, Any>()
        } ?: mapOf<String, Any>()
    }
}

/**
 * 默认表达式引擎实现
 */
class DefaultExpressionEngine : ExpressionEngine {
    
    override suspend fun <T> evaluate(
        expression: String,
        context: cn.nkpro.elcube.jimmer.engine.DocumentContext
    ): T? {
        // 这里实现SpEL表达式解析
        // 可以使用Spring的SpEL或者其他表达式引擎
        
        // 简化实现
        return when (expression) {
            "now()" -> java.time.LocalDateTime.now() as? T
            "doc.docType.docName" -> context.document.docType.docName as? T
            "doc.docName" -> context.document.docName as? T
            "doc.partnerId" -> context.document.partnerId as? T
            "doc.partnerName" -> context.document.partnerName as? T
            "doc.docAmount" -> context.document.docAmount as? T
            else -> {
                // 更复杂的表达式解析
                parseComplexExpression(expression, context)
            }
        }
    }
    
    override fun toJson(obj: Any): String {
        // 将对象转换为JSON字符串
        // 可以使用Jackson或其他JSON库
        return when (obj) {
            is String -> "\"$obj\""
            is Number -> obj.toString()
            is Boolean -> obj.toString()
            is Map<*, *> -> {
                val entries = obj.entries.joinToString(",") { (k, v) ->
                    "\"$k\":${toJson(v ?: "null")}"
                }
                "{$entries}"
            }
            is List<*> -> {
                val items = obj.joinToString(",") { toJson(it ?: "null") }
                "[$items]"
            }
            else -> "\"$obj\""
        }
    }
    
    private suspend fun <T> parseComplexExpression(
        expression: String,
        context: cn.nkpro.elcube.jimmer.engine.DocumentContext
    ): T? {
        // 解析复杂表达式
        // 例如：doc.header.customerName, doc.items.sum(amount) 等
        
        val parts = expression.split(".")
        if (parts.size < 2) return null
        
        return when (parts[0]) {
            "doc" -> {
                when (parts[1]) {
                    "header" -> {
                        if (parts.size > 2) {
                            val headerData = context.getCardData<Map<String, Any>>("HEADER")
                            headerData?.get(parts[2]) as? T
                        } else null
                    }
                    "items" -> {
                        if (parts.size > 2) {
                            val itemsData = context.getCardData<List<Map<String, Any>>>("ITEMS")
                            when (parts[2]) {
                                "sum(amount)" -> {
                                    itemsData?.sumOf { 
                                        (it["amount"] as? Number)?.toDouble() ?: 0.0 
                                    } as? T
                                }
                                "count()" -> itemsData?.size as? T
                                else -> null
                            }
                        } else null
                    }
                    else -> null
                }
            }
            else -> null
        }
    }
}
