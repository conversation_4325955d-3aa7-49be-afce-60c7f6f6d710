package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.annotation.*
import cn.nkpro.elcube.jimmer.model.*
import cn.nkpro.elcube.jimmer.schema.*
import cn.nkpro.elcube.jimmer.enums.*
import cn.nkpro.elcube.jimmer.schema.page.AbstractEditSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractListSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractViewSchema
import cn.nkpro.elcube.jimmer.schema.page.Edit
import cn.nkpro.elcube.jimmer.schema.page.Model
import cn.nkpro.elcube.jimmer.schema.page.Search
import cn.nkpro.elcube.jimmer.schema.page.ViewSchemaDsl
import cn.nkpro.elcube.jimmer.schema.page.ViewsConfig
import org.springframework.stereotype.Component
import org.babyfish.jimmer.sql.*
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 完整的关联关系示例
 * 展示单据间的对象关联关系，而不仅仅是字符串字段关联
 */

/**
 * 销售订单实体 - 完整关联关系
 */
@Model(
    name = "销售订单",
    views = ViewsConfig(
        list = SalesOrderRelationListSchema::class,
        view = SalesOrderRelationViewSchema::class,
        edit = SalesOrderRelationEditSchema::class
    ),
    state = SalesOrderRelationStateDefinition::class,
    link = SalesOrderRelationLinkDefinition::class,
    schema = SalesOrderRelationModelSchema::class
)
@Entity
@Table(name = "t_sales_order_relation")
interface SalesOrderRelation : BusinessEntity {
    
    @Key
    val orderNo: String
    
    // 基础字段
    val customerName: String
    val totalAmount: BigDecimal
    val orderDate: LocalDateTime
    val deliveryDate: LocalDateTime
    val status: SalesOrderStatus
    val priority: Priority
    val paymentStatus: PaymentStatus
    val invoiceType: InvoiceType?
    val remark: String?
    
    // 关联关系：客户信息
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    // 反向关联：一对多关联到订单明细
    @OneToMany(mappedBy = "order")
    val orderItems: List<SalesOrderItem>
    
    // 反向关联：一对多关联到发货单
    @OneToMany(mappedBy = "sourceOrder")
    val shipments: List<ShipmentRelation>
    
    // 反向关联：一对多关联到发票
    @OneToMany(mappedBy = "sourceOrder")
    val invoices: List<InvoiceRelation>
    
    // 反向关联：一对多关联到付款单
    @OneToMany(mappedBy = "sourceOrder")
    val payments: List<PaymentRelation>
}

/**
 * 销售订单明细实体
 */
@Entity
@Table(name = "t_sales_order_item")
interface SalesOrderItem : BusinessEntity {
    
    @Key
    val itemId: String
    
    // 关联关系：多对一关联到订单
    @ManyToOne
    @JoinColumn(name = "order_id")
    val order: SalesOrderRelation
    
    // 关联关系：多对一关联到产品
    @ManyToOne
    @JoinColumn(name = "product_id")
    val product: Product
    
    val quantity: BigDecimal
    val unitPrice: BigDecimal
    val totalPrice: BigDecimal
    val remark: String?
}

/**
 * 发货单实体 - 完整关联关系
 */
@Model(
    name = "发货单",
    views = ViewsConfig(
        list = ShipmentRelationListSchema::class,
        view = ShipmentRelationViewSchema::class,
        edit = ShipmentRelationEditSchema::class
    ),
    state = ShipmentRelationStateDefinition::class,
    link = ShipmentRelationLinkDefinition::class,
    schema = ShipmentRelationModelSchema::class
)
@Entity
@Table(name = "t_shipment_relation")
interface ShipmentRelation : BusinessEntity {
    
    @Key
    val shipmentNo: String
    
    // 关联关系：多对一关联到销售订单
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderRelation?
    
    // 关联关系：多对一关联到客户
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    // 基础字段
    val customerName: String
    val totalAmount: BigDecimal
    val plannedDate: LocalDateTime
    val actualDate: LocalDateTime?
    val status: ShipmentStatus
    val shipmentType: ShipmentType
    val remark: String?
    
    // 反向关联：一对多关联到发货明细
    @OneToMany(mappedBy = "shipment")
    val shipmentItems: List<ShipmentItem>
    
    // 反向关联：一对多关联到发票
    @OneToMany(mappedBy = "sourceShipment")
    val invoices: List<InvoiceRelation>
}

/**
 * 发货明细实体
 */
@Entity
@Table(name = "t_shipment_item")
interface ShipmentItem : BusinessEntity {
    
    @Key
    val itemId: String
    
    // 关联关系：多对一关联到发货单
    @ManyToOne
    @JoinColumn(name = "shipment_id")
    val shipment: ShipmentRelation
    
    // 关联关系：多对一关联到订单明细
    @ManyToOne
    @JoinColumn(name = "order_item_id")
    val orderItem: SalesOrderItem?
    
    // 关联关系：多对一关联到产品
    @ManyToOne
    @JoinColumn(name = "product_id")
    val product: Product
    
    val quantity: BigDecimal
    val unitPrice: BigDecimal
    val totalPrice: BigDecimal
    val remark: String?
}

/**
 * 发票实体 - 完整关联关系
 */
@Model(
    name = "发票",
    views = ViewsConfig(
        list = InvoiceRelationListSchema::class,
        view = InvoiceRelationViewSchema::class,
        edit = InvoiceRelationEditSchema::class
    ),
    schema = InvoiceRelationModelSchema::class
)
@Entity
@Table(name = "t_invoice_relation")
interface InvoiceRelation : BusinessEntity {
    
    @Key
    val invoiceNo: String
    
    // 关联关系：多对一关联到销售订单
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderRelation?
    
    // 关联关系：多对一关联到发货单
    @ManyToOne
    @JoinColumn(name = "source_shipment_id")
    val sourceShipment: ShipmentRelation?
    
    // 关联关系：多对一关联到客户
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    // 基础字段
    val customerName: String
    val invoiceAmount: BigDecimal
    val taxRate: BigDecimal
    val totalAmount: BigDecimal
    val invoiceDate: LocalDateTime
    val status: InvoiceStatus
    val remark: String?
    
    // 反向关联：一对多关联到发票明细
    @OneToMany(mappedBy = "invoice")
    val invoiceItems: List<InvoiceItem>
    
    // 反向关联：一对多关联到付款单
    @OneToMany(mappedBy = "sourceInvoice")
    val payments: List<PaymentRelation>
}

/**
 * 发票明细实体
 */
@Entity
@Table(name = "t_invoice_item")
interface InvoiceItem : BusinessEntity {
    
    @Key
    val itemId: String
    
    // 关联关系：多对一关联到发票
    @ManyToOne
    @JoinColumn(name = "invoice_id")
    val invoice: InvoiceRelation
    
    // 关联关系：多对一关联到发货明细
    @ManyToOne
    @JoinColumn(name = "shipment_item_id")
    val shipmentItem: ShipmentItem?
    
    // 关联关系：多对一关联到产品
    @ManyToOne
    @JoinColumn(name = "product_id")
    val product: Product
    
    val quantity: BigDecimal
    val unitPrice: BigDecimal
    val taxAmount: BigDecimal
    val totalAmount: BigDecimal
    val remark: String?
}

/**
 * 付款单实体
 */
@Entity
@Table(name = "t_payment_relation")
interface PaymentRelation : BusinessEntity {
    
    @Key
    val paymentNo: String
    
    // 关联关系：多对一关联到销售订单
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderRelation?
    
    // 关联关系：多对一关联到发票
    @ManyToOne
    @JoinColumn(name = "source_invoice_id")
    val sourceInvoice: InvoiceRelation?
    
    // 关联关系：多对一关联到客户
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    val paymentAmount: BigDecimal
    val paymentDate: LocalDateTime
    val paymentMethod: String
    val status: String
    val remark: String?
}

/**
 * 客户实体
 */
@Entity
@Table(name = "t_customer")
interface Customer : BusinessEntity {
    
    @Key
    val customerCode: String
    
    val customerName: String
    val contactPerson: String?
    val phone: String?
    val email: String?
    val address: String?
    val creditLimit: BigDecimal?
    val status: String
    
    // 反向关联：一对多关联到销售订单
    @OneToMany(mappedBy = "customer")
    val orders: List<SalesOrderRelation>
    
    // 反向关联：一对多关联到发货单
    @OneToMany(mappedBy = "customer")
    val shipments: List<ShipmentRelation>
    
    // 反向关联：一对多关联到发票
    @OneToMany(mappedBy = "customer")
    val invoices: List<InvoiceRelation>
}

/**
 * 产品实体
 */
@Entity
@Table(name = "t_product")
interface Product : BusinessEntity {
    
    @Key
    val productCode: String
    
    val productName: String
    val specification: String?
    val unit: String
    val unitPrice: BigDecimal
    val status: String
    
    // 反向关联：一对多关联到订单明细
    @OneToMany(mappedBy = "product")
    val orderItems: List<SalesOrderItem>
    
    // 反向关联：一对多关联到发货明细
    @OneToMany(mappedBy = "product")
    val shipmentItems: List<ShipmentItem>
    
    // 反向关联：一对多关联到发票明细
    @OneToMany(mappedBy = "product")
    val invoiceItems: List<InvoiceItem>
}

// Schema实现类（简化）
@Component
class SalesOrderRelationListSchema : AbstractListSchema<SalesOrderRelation>(
    "sales-order-relation-list", "销售订单列表", SalesOrderRelation::class
) {
    override fun defineList(): Search.() -> Unit = {
        standardList("orderNo", "customerName", "totalAmount", "status")
    }
}

@Component
class SalesOrderRelationViewSchema : AbstractViewSchema<SalesOrderRelation>(
    "sales-order-relation-view", "销售订单详情", SalesOrderRelation::class
) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = {
        standardView("orderNo", "customerName", "totalAmount", "status")
    }
}

@Component
class SalesOrderRelationEditSchema : AbstractEditSchema<SalesOrderRelation>(
    "sales-order-relation-edit", "销售订单编辑", SalesOrderRelation::class
) {
    override fun defineEdit(): Edit.() -> Unit = {
        standardEdit("orderNo", "customerName", "totalAmount", "status")
    }
}

@Component
class SalesOrderRelationModelSchema : AbstractModelSchema<SalesOrderRelation>(
    "sales-order-relation-model", "销售订单业务模型", SalesOrderRelation::class
) {
    override fun defineModel(): ModelSchemaDsl.() -> Unit = {
        modelName = "销售订单"
        businessIndex("order_no_idx") { on("orderNo"); unique() }
    }
}

@Component
class SalesOrderRelationStateDefinition : AbstractStateDefinition() {
    override fun defineState(): StateFlowDsl.() -> Unit = {
        flowName = "销售订单状态流"
        standardFlow()
    }
}

@Component
class SalesOrderRelationLinkDefinition : AbstractLinkDefinition() {
    override fun defineLink(): LinkDsl.() -> Unit = {
        linkName = "销售订单链接"
        description = "基于对象关联的单据生成关系"

        // 销售订单 → 发货单（使用对象关联）
        rule("生成发货单", SalesOrderRelation::class, ShipmentRelation::class) {
            fromStatus { order ->
                order.status == SalesOrderStatus.APPROVED
            }

            condition { order ->
                order.totalAmount > BigDecimal.ZERO &&
                order.priority in Priority.getHighPriorities() &&
                order.customer != null  // 必须有客户信息
            }

            enabled = true
            manualGenerate()

            // 对象关联映射
            fieldMapping(SalesOrderRelation::id, ShipmentRelation::sourceOrder) { order ->
                order  // 直接传递对象引用
            }

            // 关联对象映射
            fieldMapping(SalesOrderRelation::customer, ShipmentRelation::customer)

            // 基础字段映射
            fieldMapping(SalesOrderRelation::orderNo, ShipmentRelation::shipmentNo) { order ->
                "SH-${order.orderNo}"  // 生成发货单号
            }
            fieldMapping(SalesOrderRelation::customerName, ShipmentRelation::customerName)
            fieldMapping(SalesOrderRelation::totalAmount, ShipmentRelation::totalAmount)
            fieldMapping(SalesOrderRelation::deliveryDate, ShipmentRelation::plannedDate)

            // 枚举值映射
            fieldMapping(SalesOrderRelation::status, ShipmentRelation::status) { order ->
                ShipmentStatus.DRAFT
            }

            fieldMapping(SalesOrderRelation::remark, ShipmentRelation::shipmentType) { order ->
                ShipmentType.SALES
            }

            // 验证规则
            validation("customer_check") {
                condition({ order -> order.customer != null }, "必须有客户信息才能生成发货单")
            }

            validation("order_items_check") {
                condition({ order -> order.orderItems.isNotEmpty() }, "订单必须有明细才能生成发货单")
            }

            validation("unique_check") {
                condition({ order ->
                    order.shipments.none { it.status != ShipmentStatus.CANCELLED }
                }, "该订单已有有效的发货单")
            }
        }

        // 销售订单 → 发票（直接开票）
        rule("生成发票", SalesOrderRelation::class, InvoiceRelation::class) {
            fromStatus { order ->
                order.status in listOf(SalesOrderStatus.APPROVED, SalesOrderStatus.SHIPPED) &&
                order.invoiceType == InvoiceType.DIRECT
            }

            condition { order ->
                order.totalAmount > BigDecimal.ZERO &&
                order.customer != null &&
                order.paymentStatus != PaymentStatus.OVERDUE
            }

            enabled = true
            manualGenerate()

            // 对象关联映射
            fieldMapping(SalesOrderRelation::id, InvoiceRelation::sourceOrder) { order ->
                order
            }
            fieldMapping(SalesOrderRelation::customer, InvoiceRelation::customer)

            // 基础字段映射
            fieldMapping(SalesOrderRelation::orderNo, InvoiceRelation::invoiceNo) { order ->
                "INV-${order.orderNo}"
            }
            fieldMapping(SalesOrderRelation::customerName, InvoiceRelation::customerName)
            fieldMapping(SalesOrderRelation::totalAmount, InvoiceRelation::invoiceAmount)

            // 计算映射
            fieldMapping(SalesOrderRelation::totalAmount, InvoiceRelation::totalAmount) { order ->
                order.totalAmount * BigDecimal("1.13")  // 含税总额
            }

            fieldMapping(SalesOrderRelation::status, InvoiceRelation::status) { order ->
                InvoiceStatus.DRAFT
            }

            // 验证规则
            validation("invoice_type_check") {
                condition({ order -> order.invoiceType == InvoiceType.DIRECT }, "只有直接开票类型可以生成发票")
            }

            validation("payment_status_check") {
                condition({ order -> order.paymentStatus != PaymentStatus.OVERDUE }, "逾期订单不能开票")
            }
        }
    }
}

// 其他Schema类的简化实现
@Component class ShipmentRelationListSchema : AbstractListSchema<ShipmentRelation>("shipment-relation-list", "发货单列表", ShipmentRelation::class) {
    override fun defineList(): Search.() -> Unit = { standardList("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentRelationViewSchema : AbstractViewSchema<ShipmentRelation>("shipment-relation-view", "发货单详情", ShipmentRelation::class) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = { standardView("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentRelationEditSchema : AbstractEditSchema<ShipmentRelation>("shipment-relation-edit", "发货单编辑", ShipmentRelation::class) {
    override fun defineEdit(): Edit.() -> Unit = { standardEdit("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentRelationModelSchema : AbstractModelSchema<ShipmentRelation>("shipment-relation-model", "发货单业务模型", ShipmentRelation::class) {
    override fun defineModel(): ModelSchemaDsl.() -> Unit = { modelName = "发货单" }
}
@Component class ShipmentRelationStateDefinition : AbstractStateDefinition() {
    override fun defineState(): StateFlowDsl.() -> Unit = { standardFlow() }
}
@Component class ShipmentRelationLinkDefinition : AbstractLinkDefinition() {
    override fun defineLink(): LinkDsl.() -> Unit = { standardLink() }
}

@Component class InvoiceRelationListSchema : AbstractListSchema<InvoiceRelation>("invoice-relation-list", "发票列表", InvoiceRelation::class) {
    override fun defineList(): Search.() -> Unit = { standardList("invoiceNo", "customerName", "totalAmount", "status") }
}
@Component class InvoiceRelationViewSchema : AbstractViewSchema<InvoiceRelation>("invoice-relation-view", "发票详情", InvoiceRelation::class) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = { standardView("invoiceNo", "customerName", "totalAmount", "status") }
}
@Component class InvoiceRelationEditSchema : AbstractEditSchema<InvoiceRelation>("invoice-relation-edit", "发票编辑", InvoiceRelation::class) {
    override fun defineEdit(): Edit.() -> Unit = { standardEdit("invoiceNo", "customerName", "totalAmount", "status") }
}
@Component class InvoiceRelationModelSchema : AbstractModelSchema<InvoiceRelation>("invoice-relation-model", "发票业务模型", InvoiceRelation::class) {
    override fun defineModel(): ModelSchemaDsl.() -> Unit = { modelName = "发票" }
}
