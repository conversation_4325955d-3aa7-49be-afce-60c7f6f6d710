package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.annotation.*
import cn.nkpro.elcube.jimmer.model.*
import cn.nkpro.elcube.jimmer.schema.*
import cn.nkpro.elcube.jimmer.enums.*
import cn.nkpro.elcube.jimmer.relation.*
import cn.nkpro.elcube.jimmer.schema.model.Link
import cn.nkpro.elcube.jimmer.schema.page.AbstractEditSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractListSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractViewSchema
import cn.nkpro.elcube.jimmer.schema.page.Edit
import cn.nkpro.elcube.jimmer.schema.page.Model
import cn.nkpro.elcube.jimmer.schema.page.Search
import cn.nkpro.elcube.jimmer.schema.page.ViewSchemaDsl
import cn.nkpro.elcube.jimmer.schema.page.ViewsConfig
import org.springframework.stereotype.Component
import org.babyfish.jimmer.sql.*
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.reflect.KClass

/**
 * 自动关联关系推导示例
 * 展示如何从 LinkDefinition 自动推导单据间的关联关系，无需用户手动定义注解
 */

/**
 * 销售订单实体 - 简洁版本，无需关联注解
 */
@Model(
    name = "销售订单",
    views = ViewsConfig(
        list = SalesOrderAutoListSchema::class,
        view = SalesOrderAutoViewSchema::class,
        edit = SalesOrderAutoEditSchema::class
    ),
    state = SalesOrderAutoStateDefinition::class,
    link = SalesOrderAutoLinkDefinition::class,  // 关键：通过这个自动推导关联关系
)
@Entity
@Table(name = "t_sales_order_auto")
interface SalesOrderAuto : BusinessEntity {
    
    @Key
    val orderNo: String
    
    val customerName: String
    val totalAmount: BigDecimal
    val orderDate: LocalDateTime
    val deliveryDate: LocalDateTime
    val status: SalesOrderStatus
    val priority: Priority
    val paymentStatus: PaymentStatus
    val invoiceType: InvoiceType?
    val remark: String?
    
    // 基础关联关系（JPA标准注解）
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    @OneToMany(mappedBy = "order")
    val orderItems: List<SalesOrderItemAuto>
    
    @OneToMany(mappedBy = "sourceOrder")
    val shipments: List<ShipmentAuto>
    
    @OneToMany(mappedBy = "sourceOrder")
    val invoices: List<InvoiceAuto>
    
    @OneToMany(mappedBy = "sourceOrder")
    val payments: List<PaymentAuto>
}

/**
 * 发货单实体 - 简洁版本
 */
@Model(
    name = "发货单",
    views = ViewsConfig(
        list = ShipmentAutoListSchema::class,
        view = ShipmentAutoViewSchema::class,
        edit = ShipmentAutoEditSchema::class
    ),
    state = ShipmentAutoStateDefinition::class,
    link = ShipmentAutoLinkDefinition::class,  // 关键：通过这个自动推导关联关系
)
@Entity
@Table(name = "t_shipment_auto")
interface ShipmentAuto : BusinessEntity {
    
    @Key
    val shipmentNo: String
    
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderAuto?
    
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    val customerName: String
    val totalAmount: BigDecimal
    val plannedDate: LocalDateTime
    val actualDate: LocalDateTime?
    val status: ShipmentStatus
    val shipmentType: ShipmentType
    val remark: String?
    
    @OneToMany(mappedBy = "shipment")
    val shipmentItems: List<ShipmentItemAuto>
    
    @OneToMany(mappedBy = "sourceShipment")
    val invoices: List<InvoiceAuto>
}

/**
 * 发票实体 - 简洁版本
 */
@Model(
    name = "发票",
    views = ViewsConfig(
        list = InvoiceAutoListSchema::class,
        view = InvoiceAutoViewSchema::class,
        edit = InvoiceAutoEditSchema::class
    ),
    link = InvoiceAutoLinkDefinition::class,  // 可选：如果发票还能生成其他单据
)
@Entity
@Table(name = "t_invoice_auto")
interface InvoiceAuto : BusinessEntity {
    
    @Key
    val invoiceNo: String
    
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderAuto?
    
    @ManyToOne
    @JoinColumn(name = "source_shipment_id")
    val sourceShipment: ShipmentAuto?
    
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    val customerName: String
    val invoiceAmount: BigDecimal
    val taxRate: BigDecimal
    val totalAmount: BigDecimal
    val invoiceDate: LocalDateTime
    val status: InvoiceStatus
    val remark: String?
    
    @OneToMany(mappedBy = "sourceInvoice")
    val payments: List<PaymentAuto>
}

/**
 * 付款单实体 - 简洁版本
 */
@Entity
@Table(name = "t_payment_auto")
interface PaymentAuto : BusinessEntity {
    
    @Key
    val paymentNo: String
    
    @ManyToOne
    @JoinColumn(name = "source_order_id")
    val sourceOrder: SalesOrderAuto?
    
    @ManyToOne
    @JoinColumn(name = "source_invoice_id")
    val sourceInvoice: InvoiceAuto?
    
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer?
    
    val paymentAmount: BigDecimal
    val paymentDate: LocalDateTime
    val paymentMethod: String
    val status: String
}

/**
 * 明细实体
 */
@Entity
@Table(name = "t_sales_order_item_auto")
interface SalesOrderItemAuto : BusinessEntity {
    @Key val itemId: String
    @ManyToOne @JoinColumn(name = "order_id") val order: SalesOrderAuto
    @ManyToOne @JoinColumn(name = "product_id") val product: Product
    val quantity: BigDecimal
    val unitPrice: BigDecimal
    val totalPrice: BigDecimal
    @OneToMany(mappedBy = "orderItem") val shipmentItems: List<ShipmentItemAuto>
}

@Entity
@Table(name = "t_shipment_item_auto")
interface ShipmentItemAuto : BusinessEntity {
    @Key val itemId: String
    @ManyToOne @JoinColumn(name = "shipment_id") val shipment: ShipmentAuto
    @ManyToOne @JoinColumn(name = "order_item_id") val orderItem: SalesOrderItemAuto?
    @ManyToOne @JoinColumn(name = "product_id") val product: Product
    val quantity: BigDecimal
    val unitPrice: BigDecimal
    val totalPrice: BigDecimal
    val remark: String?
}

/**
 * 销售订单链接定义 - 关键：从这里自动推导关联关系
 */
@Component
class SalesOrderAutoLinkDefinition : AbstractLinkDefinition() {
    override fun defineLink(): Link.() -> Unit = {
        linkName = "销售订单链接"
        description = "销售订单的单据生成关系"
        
        // 销售订单 → 发货单
        rule("生成发货单", SalesOrderAuto::class, ShipmentAuto::class) {
            description = "从销售订单生成发货单"
            
            fromStatus { order -> order.status == SalesOrderStatus.APPROVED }
            condition { order -> 
                order.totalAmount > BigDecimal.ZERO && 
                order.priority in Priority.getHighPriorities()
            }
            
            enabled = true
            manualGenerate()
            
            // 字段映射 - 系统会自动记录这些映射关系
            fieldMapping(SalesOrderAuto::orderNo, ShipmentAuto::shipmentNo) { order ->
                order
            }
            fieldMapping(SalesOrderAuto::id, ShipmentAuto::sourceOrder) { order -> order }
            fieldMapping(SalesOrderAuto::customer, ShipmentAuto::customer)
            fieldMapping(SalesOrderAuto::customerName, ShipmentAuto::customerName)
            fieldMapping(SalesOrderAuto::totalAmount, ShipmentAuto::totalAmount)
            fieldMapping(SalesOrderAuto::deliveryDate, ShipmentAuto::plannedDate)
            
            // 验证规则 - 系统会自动记录这些验证条件
            validation("customer_check") {
                condition({ order -> order.customer != null }, "必须有客户信息")
            }
            validation("order_items_check") {
                condition({ order -> order.orderItems.isNotEmpty() }, "订单必须有明细")
            }
        }
        
        // 销售订单 → 发票
        rule("生成发票", SalesOrderAuto::class, InvoiceAuto::class) {
            description = "从销售订单直接生成发票"
            
            fromStatus { order -> 
                order.status in listOf(SalesOrderStatus.APPROVED, SalesOrderStatus.SHIPPED) &&
                order.invoiceType == InvoiceType.DIRECT
            }
            condition { order -> order.totalAmount > BigDecimal.ZERO }
            
            enabled = true
            manualGenerate()
            
            fieldMapping(SalesOrderAuto::orderNo, InvoiceAuto::invoiceNo) { order ->
                "INV-${order.orderNo}"
            }
            fieldMapping(SalesOrderAuto::id, InvoiceAuto::sourceOrder) { order -> order }
            fieldMapping(SalesOrderAuto::customer, InvoiceAuto::customer)
            fieldMapping(SalesOrderAuto::customerName, InvoiceAuto::customerName)
            fieldMapping(SalesOrderAuto::totalAmount, InvoiceAuto::invoiceAmount)
            fieldMapping(SalesOrderAuto::totalAmount, InvoiceAuto::totalAmount) { order ->
                order.totalAmount * BigDecimal("1.13")
            }
        }
    }
}

/**
 * 发货单链接定义
 */
@Component
class ShipmentAutoLinkDefinition : AbstractLinkDefinition() {
    override fun defineLink(): Link.() -> Unit = {
        linkName = "发货单链接"
        
        // 发货单 → 发票
        rule("生成发票", ShipmentAuto::class, InvoiceAuto::class) {
            description = "从发货单生成发票"
            
            fromStatus { shipment -> 
                shipment.status == ShipmentStatus.SHIPPED &&
                shipment.shipmentType == ShipmentType.SALES
            }
            condition { shipment -> shipment.totalAmount > BigDecimal.ZERO }
            
            enabled = true
            manualGenerate()
            
            fieldMapping(ShipmentAuto::shipmentNo, InvoiceAuto::invoiceNo) { shipment ->
                shipment
            }
            fieldMapping(ShipmentAuto::id, InvoiceAuto::sourceShipment) { shipment -> shipment }
            fieldMapping(ShipmentAuto::sourceOrder, InvoiceAuto::sourceOrder)
            fieldMapping(ShipmentAuto::customer, InvoiceAuto::customer)
            fieldMapping(ShipmentAuto::customerName, InvoiceAuto::customerName)
            fieldMapping(ShipmentAuto::totalAmount, InvoiceAuto::invoiceAmount)
        }
    }
}

/**
 * 发票链接定义（如果发票还能生成其他单据）
 */
@Component
class InvoiceAutoLinkDefinition : AbstractLinkDefinition() {
    override fun defineLink(): Link.() -> Unit = {
        linkName = "发票链接"
        
        // 发票 → 付款单（如果需要的话）
        // 这里可以定义发票生成付款单的规则
    }
}

/**
 * 自动关联关系演示服务
 */
@Component
class AutoRelationDemoService(
    private val documentRelationResolver: DocumentRelationResolver,
    private val universalRelatedDocumentService: UniversalRelatedDocumentService
) {
    
    /**
     * 演示自动关联关系推导
     */
    fun demonstrateAutoRelation() {
        println("=== 自动关联关系推导演示 ===")
        
        // 1. 获取销售订单的关联关系
        val salesOrderRelations = documentRelationResolver.getDocumentRelations(SalesOrderAuto::class)
        println("\n销售订单的关联关系:")
        salesOrderRelations.forEach { relation ->
            println("- ${relation.relationName}: ${relation.sourceDocumentType.simpleName} → ${relation.targetDocumentType.simpleName}")
            println("  方向: ${relation.direction}")
            println("  描述: ${relation.description}")
            println("  字段映射: ${relation.fieldMappings.size} 个")
            println("  验证规则: ${relation.validations.size} 个")
        }
        
        // 2. 获取发货单的关联关系
        val shipmentRelations = documentRelationResolver.getDocumentRelations(ShipmentAuto::class)
        println("\n发货单的关联关系:")
        shipmentRelations.forEach { relation ->
            println("- ${relation.relationName}: ${relation.sourceDocumentType.simpleName} → ${relation.targetDocumentType.simpleName}")
            println("  方向: ${relation.direction}")
            println("  描述: ${relation.description}")
        }
        
        // 3. 获取发票的关联关系
        val invoiceRelations = documentRelationResolver.getDocumentRelations(InvoiceAuto::class)
        println("\n发票的关联关系:")
        invoiceRelations.forEach { relation ->
            println("- ${relation.relationName}: ${relation.sourceDocumentType.simpleName} → ${relation.targetDocumentType.simpleName}")
            println("  方向: ${relation.direction}")
            println("  描述: ${relation.description}")
        }
        
        // 4. 演示查询关联单据
        println("\n=== 查询关联单据演示 ===")
        val relatedDocs = universalRelatedDocumentService.findRelatedDocuments(SalesOrderAuto::class, "SO001")
        println("销售订单 SO001 的关联单据:")
        relatedDocs.forEach { doc ->
            println("- ${doc.documentType}: ${doc.documentNo} (${doc.statusName}) - ${doc.relationName}")
        }
    }
    
    /**
     * 获取单据类型的关联关系摘要
     */
    fun getDocumentRelationSummary(documentType: KClass<*>): Map<String, Any> {
        return universalRelatedDocumentService.getRelationSummary(documentType)
    }
}

// Schema实现类（简化）
@Component class SalesOrderAutoListSchema : AbstractListSchema<SalesOrderAuto>("sales-order-auto-list", "销售订单列表", SalesOrderAuto::class) {
    override fun defineList(): Search.() -> Unit = { standardList("orderNo", "customerName", "totalAmount", "status") }
}
@Component class SalesOrderAutoViewSchema : AbstractViewSchema<SalesOrderAuto>("sales-order-auto-view", "销售订单详情", SalesOrderAuto::class) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = { standardView("orderNo", "customerName", "totalAmount", "status") }
}
@Component class SalesOrderAutoEditSchema : AbstractEditSchema<SalesOrderAuto>("sales-order-auto-edit", "销售订单编辑", SalesOrderAuto::class) {
    override fun defineEdit(): Edit.() -> Unit = { standardEdit("orderNo", "customerName", "totalAmount", "status") }
}
@Component class SalesOrderAutoStateDefinition : AbstractStateDefinition() {
    override fun defineState(): StateFlowDsl.() -> Unit = { standardFlow() }
}

@Component class ShipmentAutoListSchema : AbstractListSchema<ShipmentAuto>("shipment-auto-list", "发货单列表", ShipmentAuto::class) {
    override fun defineList(): Search.() -> Unit = { standardList("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentAutoViewSchema : AbstractViewSchema<ShipmentAuto>("shipment-auto-view", "发货单详情", ShipmentAuto::class) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = { standardView("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentAutoEditSchema : AbstractEditSchema<ShipmentAuto>("shipment-auto-edit", "发货单编辑", ShipmentAuto::class) {
    override fun defineEdit(): Edit.() -> Unit = { standardEdit("shipmentNo", "customerName", "totalAmount", "status") }
}
@Component class ShipmentAutoStateDefinition : AbstractStateDefinition() {
    override fun defineState(): StateFlowDsl.() -> Unit = { standardFlow() }
}

@Component class InvoiceAutoListSchema : AbstractListSchema<InvoiceAuto>("invoice-auto-list", "发票列表", InvoiceAuto::class) {
    override fun defineList(): Search.() -> Unit = { standardList("invoiceNo", "customerName", "totalAmount", "status") }
}
@Component class InvoiceAutoViewSchema : AbstractViewSchema<InvoiceAuto>("invoice-auto-view", "发票详情", InvoiceAuto::class) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = { standardView("invoiceNo", "customerName", "totalAmount", "status") }
}
@Component class InvoiceAutoEditSchema : AbstractEditSchema<InvoiceAuto>("invoice-auto-edit", "发票编辑", InvoiceAuto::class) {
    override fun defineEdit(): Edit.() -> Unit = { standardEdit("invoiceNo", "customerName", "totalAmount", "status") }
}
