package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.annotation.*
import cn.nkpro.elcube.jimmer.model.*
import cn.nkpro.elcube.jimmer.schema.*
import cn.nkpro.elcube.jimmer.enums.*
import cn.nkpro.elcube.jimmer.schema.page.AbstractEditSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractListSchema
import cn.nkpro.elcube.jimmer.schema.page.AbstractViewSchema
import cn.nkpro.elcube.jimmer.schema.page.Edit
import cn.nkpro.elcube.jimmer.schema.page.Model
import cn.nkpro.elcube.jimmer.schema.page.Search
import cn.nkpro.elcube.jimmer.schema.page.ViewSchemaDsl
import cn.nkpro.elcube.jimmer.schema.page.ViewsConfig
import org.springframework.stereotype.Component
import org.babyfish.jimmer.sql.*
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 引用组件使用示例
 * 展示各种引用组件的使用方法，类似 Erupt 的对象组件功能
 */

/**
 * 部门实体 - 树形结构
 */
@Entity
@Table(name = "t_department")
interface Department : BusinessEntity {
    
    @Key
    val deptId: String
    
    val deptName: String
    val deptCode: String
    
    @ManyToOne
    @JoinColumn(name = "parent_id")
    val parent: Department?
    
    @OneToMany(mappedBy = "parent")
    val children: List<Department>
    
    val level: Int
    val sortOrder: Int
    val status: String
    val remark: String?
}

/**
 * 角色实体
 */
@Entity
@Table(name = "t_role")
interface Role : BusinessEntity {
    
    @Key
    val roleId: String
    
    val roleName: String
    val roleCode: String
    val description: String?
    val status: String
}

/**
 * 权限实体
 */
@Entity
@Table(name = "t_permission")
interface Permission : BusinessEntity {
    
    @Key
    val permissionId: String
    
    val permissionName: String
    val permissionCode: String
    val resourceType: String  // MENU, BUTTON, API
    
    @ManyToOne
    @JoinColumn(name = "parent_id")
    val parent: Permission?
    
    @OneToMany(mappedBy = "parent")
    val children: List<Permission>
    
    val level: Int
    val status: String
}

/**
 * 地区实体 - 级联结构（省市区）
 */
@Entity
@Table(name = "t_region")
interface Region : BusinessEntity {
    
    @Key
    val regionId: String
    
    val regionName: String
    val regionCode: String
    
    @ManyToOne
    @JoinColumn(name = "parent_id")
    val parent: Region?
    
    @OneToMany(mappedBy = "parent")
    val children: List<Region>
    
    val level: Int  // 1:省份, 2:城市, 3:区县
    val sortOrder: Int
}

/**
 * 用户实体 - 使用各种引用组件
 */
@Model(
    name = "用户管理",
    views = ViewsConfig(
        list = UserReferenceListSchema::class,
        view = UserReferenceViewSchema::class,
        edit = UserReferenceEditSchema::class
    ),
    schema = UserReferenceModelSchema::class
)
@ReferenceConfig(
    defaultPageSize = 20,
    maxPageSize = 100,
    enableCache = true,
    defaultCacheSeconds = 300
)
@Entity
@Table(name = "t_user_reference")
interface UserReference : BusinessEntity {
    
    @Key
    val userId: String
    
    val username: String
    val realName: String
    val email: String?
    val phone: String?
    
    // 多对一表引用：选择部门
    @ManyToOne
    @JoinColumn(name = "dept_id")
    @ManyToOneReference(
        targetEntity = Department::class,
        displayMode = ReferenceDisplayMode.SELECT,
        displayField = "deptName",
        valueField = "deptId",
        condition = "status = 'ACTIVE'",
        orderBy = "sortOrder ASC",
        searchable = true,
        searchFields = ["deptName", "deptCode"],
        placeholder = "请选择部门",
        allowClear = true
    )
    val department: Department?
    
    // 多对一树引用：选择上级部门
    @ManyToOne
    @JoinColumn(name = "parent_dept_id")
    @ManyToOneTreeReference(
        targetEntity = Department::class,
        displayMode = ReferenceDisplayMode.TREE_SELECT,
        displayField = "deptName",
        valueField = "deptId",
        parentField = "parent",
        childrenField = "children",
        rootCondition = "parent IS NULL",
        condition = "status = 'ACTIVE'",
        showRoot = true,
        leafOnly = false,
        searchable = true,
        expandAll = false,
        defaultExpandLevel = 2
    )
    val parentDepartment: Department?
    
    // 多对多表引用：选择角色
    @ManyToMany
    @JoinTable(
        name = "t_user_role",
        joinColumns = [JoinColumn(name = "user_id")],
        inverseJoinColumns = [JoinColumn(name = "role_id")]
    )
    @ManyToManyReference(
        targetEntity = Role::class,
        displayMode = ReferenceDisplayMode.TRANSFER,
        displayField = "roleName",
        valueField = "roleId",
        joinTable = "t_user_role",
        joinColumn = "user_id",
        inverseJoinColumn = "role_id",
        condition = "status = 'ACTIVE'",
        orderBy = "roleName ASC",
        searchable = true,
        searchFields = ["roleName", "roleCode"],
        maxCount = 10,
        minCount = 1,
        titles = ["可选角色", "已选角色"],
        showSearch = true
    )
    val roles: List<Role>
    
    // 多对多树引用：选择权限
    @ManyToMany
    @JoinTable(
        name = "t_user_permission",
        joinColumns = [JoinColumn(name = "user_id")],
        inverseJoinColumns = [JoinColumn(name = "permission_id")]
    )
    @ManyToManyTreeReference(
        targetEntity = Permission::class,
        displayMode = ReferenceDisplayMode.TREE_SELECT,
        displayField = "permissionName",
        valueField = "permissionId",
        parentField = "parent",
        childrenField = "children",
        rootCondition = "parent IS NULL",
        joinTable = "t_user_permission",
        joinColumn = "user_id",
        inverseJoinColumn = "permission_id",
        condition = "status = 'ACTIVE'",
        showRoot = true,
        leafOnly = true,
        searchable = true,
        expandAll = false,
        defaultExpandLevel = 1,
        maxCount = 50,
        checkStrictly = false
    )
    val permissions: List<Permission>
    
    // 级联引用：选择地区（省市区）
    @CascaderReference(
        targetEntity = Region::class,
        displayField = "regionName",
        valueField = "regionId",
        parentField = "parent",
        levelField = "level",
        rootCondition = "parent IS NULL",
        condition = "",
        leafOnly = true,
        searchable = true,
        placeholder = "请选择省市区",
        allowClear = true,
        separator = " / ",
        showFullPath = true
    )
    val regionPath: String?  // 存储完整路径，如："110000/110100/110101"
    
    // 字典引用：选择用户状态
    @DictReference(
        dictCode = "USER_STATUS",
        displayMode = ReferenceDisplayMode.SELECT,
        allowClear = false,
        placeholder = "请选择状态",
        searchable = false,
        multiple = false
    )
    val status: String
    
    // 字典引用：选择用户类型（多选）
    @DictReference(
        dictCode = "USER_TYPE",
        displayMode = ReferenceDisplayMode.SELECT,
        allowClear = true,
        placeholder = "请选择用户类型",
        searchable = true,
        multiple = true
    )
    val userTypes: String?  // 存储多个值，用逗号分隔
    
    // API引用：选择外部系统用户
    @ApiReference(
        apiUrl = "/api/external/users",
        displayMode = ReferenceDisplayMode.AUTOCOMPLETE,
        displayField = "name",
        valueField = "id",
        method = "GET",
        params = ["status=active"],
        searchable = true,
        searchParam = "keyword",
        lazy = true,
        cacheSeconds = 600
    )
    val externalUserId: String?
    
    val createTime: LocalDateTime
    val updateTime: LocalDateTime?
    val remark: String?
}

/**
 * 订单实体 - 展示依赖字段的使用
 */
@Entity
@Table(name = "t_order_reference")
interface OrderReference : BusinessEntity {
    
    @Key
    val orderId: String
    
    val orderNo: String
    
    // 选择客户
    @ManyToOneReference(
        targetEntity = Customer::class,
        displayField = "customerName",
        valueField = "customerId",
        searchable = true,
        placeholder = "请选择客户"
    )
    val customer: Customer?
    
    // 选择客户联系人（依赖于客户字段）
    @ManyToOneReference(
        targetEntity = CustomerContact::class,
        displayField = "contactName",
        valueField = "contactId",
        condition = "customerId = :customerId",  // 依赖条件
        dependsOn = ["customer"],  // 依赖字段
        searchable = true,
        placeholder = "请选择联系人"
    )
    val customerContact: CustomerContact?
    
    // 选择产品
    @ManyToManyReference(
        targetEntity = Product::class,
        displayMode = ReferenceDisplayMode.TABLE_SELECT,
        displayField = "productName",
        valueField = "productId",
        searchable = true,
        maxCount = 20
    )
    val products: List<Product>
    
    val orderDate: LocalDateTime
    val totalAmount: BigDecimal
    val status: String
}

/**
 * 客户联系人实体
 */
@Entity
@Table(name = "t_customer_contact")
interface CustomerContact : BusinessEntity {
    
    @Key
    val contactId: String
    
    val contactName: String
    val phone: String?
    val email: String?
    
    @ManyToOne
    @JoinColumn(name = "customer_id")
    val customer: Customer
    
    val customerId: String  // 冗余字段，用于查询条件
}

/**
 * 引用组件演示服务
 */
@Component
class ReferenceComponentDemoService {
    
    fun demonstrateReferenceComponents() {
        println("=== 引用组件功能演示 ===")
        
        println("✓ 多对一表引用：")
        println("  - 下拉选择部门")
        println("  - 支持搜索部门名称和编码")
        println("  - 只显示状态为活跃的部门")
        println("  - 按排序字段排序")
        
        println("\n✓ 多对一树引用：")
        println("  - 树形选择上级部门")
        println("  - 支持展开/收起节点")
        println("  - 支持搜索功能")
        println("  - 可配置默认展开层级")
        
        println("\n✓ 多对多表引用：")
        println("  - 穿梭框选择角色")
        println("  - 支持搜索角色名称和编码")
        println("  - 限制最大选择数量")
        println("  - 自定义穿梭框标题")
        
        println("\n✓ 多对多树引用：")
        println("  - 树形多选权限")
        println("  - 只能选择叶子节点")
        println("  - 支持父子关联选择")
        println("  - 限制最大选择数量")
        
        println("\n✓ 级联引用：")
        println("  - 省市区级联选择")
        println("  - 只能选择叶子节点（区县）")
        println("  - 显示完整路径")
        println("  - 支持搜索功能")
        
        println("\n✓ 字典引用：")
        println("  - 下拉选择用户状态")
        println("  - 多选用户类型")
        println("  - 从字典服务获取数据")
        
        println("\n✓ API引用：")
        println("  - 自动完成选择外部用户")
        println("  - 懒加载数据")
        println("  - 支持缓存")
        println("  - 自定义搜索参数")
        
        println("\n✓ 依赖字段：")
        println("  - 客户联系人依赖于客户选择")
        println("  - 当客户变化时自动重新加载联系人")
        println("  - 动态查询条件")
    }
    
    fun demonstrateApiUsage() {
        println("\n=== API使用示例 ===")
        
        println("✓ 查询部门引用数据：")
        println("  GET /api/reference/many-to-one/UserReference/department?keyword=技术")
        
        println("\n✓ 查询树形部门数据：")
        println("  GET /api/reference/many-to-one-tree/UserReference/parentDepartment?parentId=1")
        
        println("\n✓ 查询角色引用数据：")
        println("  GET /api/reference/many-to-many/UserReference/roles?keyword=管理员")
        
        println("\n✓ 查询权限树数据：")
        println("  GET /api/reference/many-to-many-tree/UserReference/permissions")
        
        println("\n✓ 查询级联地区数据：")
        println("  GET /api/reference/cascader/UserReference/regionPath?level=1")
        
        println("\n✓ 查询字典数据：")
        println("  GET /api/reference/dict/UserReference/status")
        
        println("\n✓ 查询API数据：")
        println("  GET /api/reference/api/UserReference/externalUserId?keyword=张三")
        
        println("\n✓ 获取字段配置：")
        println("  GET /api/reference/config/UserReference/department")
        
        println("\n✓ 批量查询：")
        println("  POST /api/reference/batch")
        println("  Body: { entityClass: 'UserReference', fields: [...] }")
    }
}

// Schema实现类（简化）
@Component
class UserReferenceListSchema : AbstractListSchema<UserReference>(
    "user-reference-list", "用户列表", UserReference::class
) {
    override fun defineList(): Search.() -> Unit = {
        standardList("username", "realName", "department", "status")
    }
}

@Component
class UserReferenceViewSchema : AbstractViewSchema<UserReference>(
    "user-reference-view", "用户详情", UserReference::class
) {
    override fun defineView(): ViewSchemaDsl.() -> Unit = {
        standardView("username", "realName", "department", "roles", "status")
    }
}

@Component
class UserReferenceEditSchema : AbstractEditSchema<UserReference>(
    "user-reference-edit", "用户编辑", UserReference::class
) {
    override fun defineEdit(): Edit.() -> Unit = {
        standardEdit("username", "realName", "department", "roles", "permissions", "regionPath", "status")
    }
}

@Component
class UserReferenceModelSchema : AbstractModelSchema<UserReference>(
    "user-reference-model", "用户业务模型", UserReference::class
) {
    override fun defineModel(): ModelSchemaDsl.() -> Unit = {
        modelName = "用户"
        businessIndex("username_idx") { on("username"); unique() }
    }
}
