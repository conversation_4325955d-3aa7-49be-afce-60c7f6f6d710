package cn.nkpro.elcube.jimmer

import cn.nkpro.elcube.jimmer.dsl.*
import cn.nkpro.elcube.jimmer.engine.*
import cn.nkpro.elcube.jimmer.extension.*
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 文档引擎测试
 */
@SpringBootTest
@TestPropertySource(properties = ["spring.profiles.active=test"])
class DocumentEngineTest {
    
    @Test
    fun `测试单据类型DSL定义`() {
        val docType = documentType {
            docType = "SALES_ORDER"
            docName = "销售订单"
            docDesc = "客户销售订单管理"
            docIcon = "shopping-cart"
            enabled = true
            
            state("DRAFT") {
                stateName = "草稿"
                stateDesc = "订单草稿状态"
                stateColor = "#909399"
                isInitial = true
                
                transitionTo("CONFIRMED") {
                    conditionExpr = "doc.docAmount > 0"
                    needApproval = false
                }
                
                transitionTo("CANCELLED") {
                    conditionExpr = "true"
                    needApproval = false
                }
            }
            
            state("CONFIRMED") {
                stateName = "已确认"
                stateDesc = "订单已确认"
                stateColor = "#67C23A"
                
                transitionTo("SHIPPED") {
                    conditionExpr = "doc.inventory.available >= doc.quantity"
                    needApproval = true
                    workflowId = "SHIPPING_APPROVAL"
                }
                
                transitionTo("CANCELLED") {
                    conditionExpr = "doc.docDate.plusDays(7).isAfter(now())"
                    needApproval = true
                }
            }
            
            state("SHIPPED") {
                stateName = "已发货"
                stateDesc = "订单已发货"
                stateColor = "#409EFF"
                
                transitionTo("COMPLETED") {
                    conditionExpr = "doc.payment.status == 'PAID'"
                    needApproval = false
                }
            }
            
            state("COMPLETED") {
                stateName = "已完成"
                stateDesc = "订单已完成"
                stateColor = "#909399"
                isFinal = true
            }
            
            state("CANCELLED") {
                stateName = "已取消"
                stateDesc = "订单已取消"
                stateColor = "#F56C6C"
                isFinal = true
            }
            
            card("HEADER") {
                cardName = "订单抬头"
                cardType = "FORM"
                cardPosition = "HEADER"
                componentName = "HeaderCard"
                enabled = true
                
                field("customerName") {
                    fieldName = "客户名称"
                    fieldType = "STRING"
                    required = true
                }
                
                field("orderDate") {
                    fieldName = "订单日期"
                    fieldType = "DATE"
                    required = true
                    defaultValue = "now()"
                }
                
                field("totalAmount") {
                    fieldName = "订单总额"
                    fieldType = "DECIMAL"
                    readonly = true
                }
            }
            
            card("ITEMS") {
                cardName = "订单明细"
                cardType = "TABLE"
                cardPosition = "BODY"
                componentName = "TableCard"
                enabled = true
                calcExpr = "calculateOrderTotal()"
                
                field("productCode") {
                    fieldName = "产品编码"
                    fieldType = "STRING"
                    required = true
                }
                
                field("productName") {
                    fieldName = "产品名称"
                    fieldType = "STRING"
                    required = true
                }
                
                field("quantity") {
                    fieldName = "数量"
                    fieldType = "INTEGER"
                    required = true
                    defaultValue = "1"
                }
                
                field("unitPrice") {
                    fieldName = "单价"
                    fieldType = "DECIMAL"
                    required = true
                }
                
                field("amount") {
                    fieldName = "金额"
                    fieldType = "DECIMAL"
                    readonly = true
                }
            }
            
            index("CUSTOMER_NAME") {
                indexName = "客户名称"
                indexType = "STRING"
                extractExpr = "doc.header.customerName"
                searchable = true
                sortable = true
            }
            
            index("ORDER_DATE") {
                indexName = "订单日期"
                indexType = "DATE"
                extractExpr = "doc.header.orderDate"
                searchable = true
                sortable = true
            }
            
            index("TOTAL_AMOUNT") {
                indexName = "订单总额"
                indexType = "DECIMAL"
                extractExpr = "doc.header.totalAmount"
                searchable = true
                sortable = true
                aggregatable = true
            }
        }
        
        assertEquals("SALES_ORDER", docType.docType)
        assertEquals("销售订单", docType.docName)
        assertTrue(docType.enabled)
        assertEquals(5, docType.states.size)
        assertEquals(2, docType.cards.size)
        assertEquals(3, docType.indexes.size)
    }
    
    @Test
    fun `测试查询DSL`() = runTest {
        val query = query {
            docTypes = listOf("SALES_ORDER", "PURCHASE_ORDER")
            states = listOf("CONFIRMED", "SHIPPED")
            partnerId = "CUSTOMER_001"
            
            where("totalAmount", QueryOperator.GE, BigDecimal("1000"))
            whereIn("productCategory", listOf("ELECTRONICS", "BOOKS"))
            whereBetween("orderDate", 
                LocalDateTime.now().minusDays(30),
                LocalDateTime.now()
            )
            
            orderBy("orderDate", SortDirection.DESC)
            orderBy("totalAmount", SortDirection.ASC)
            
            limit = 50
            offset = 0
        }
        
        assertNotNull(query.docTypes)
        assertEquals(2, query.docTypes!!.size)
        assertEquals("CUSTOMER_001", query.partnerId)
        assertNotNull(query.conditions)
        assertEquals(3, query.conditions!!.size)
        assertNotNull(query.sortBy)
        assertEquals(2, query.sortBy!!.size)
        assertEquals(50, query.limit)
    }
    
    @Test
    fun `测试文档引擎操作DSL`() = runTest {
        // 模拟文档引擎
        val mockEngine = MockDocumentEngine()
        
        // 创建单据
        val document = mockEngine.create("SALES_ORDER") {
            setFields(
                "customerName" to "测试客户",
                "orderDate" to LocalDateTime.now(),
                "totalAmount" to BigDecimal("2500.00")
            )
            
            setCardData("HEADER", mapOf(
                "customerName" to "测试客户",
                "orderDate" to LocalDateTime.now(),
                "totalAmount" to BigDecimal("2500.00")
            ))
            
            setCardData("ITEMS", listOf(
                mapOf(
                    "productCode" to "P001",
                    "productName" to "笔记本电脑",
                    "quantity" to 2,
                    "unitPrice" to BigDecimal("1000.00"),
                    "amount" to BigDecimal("2000.00")
                ),
                mapOf(
                    "productCode" to "P002", 
                    "productName" to "鼠标",
                    "quantity" to 5,
                    "unitPrice" to BigDecimal("100.00"),
                    "amount" to BigDecimal("500.00")
                )
            ))
        }
        
        assertNotNull(document)
        assertEquals("SALES_ORDER", document.docType.docType)
        
        // 更新单据
        val updatedDocument = mockEngine.update(document.docId) {
            setField("customerName", "更新后的客户")
            calculate("ITEMS")
            state("CONFIRMED", "客户确认订单")
        }
        
        assertNotNull(updatedDocument)
        assertEquals("更新后的客户", updatedDocument.getField<String>("customerName"))
    }
    
    @Test
    fun `测试单据扩展函数`() = runTest {
        val mockEngine = MockDocumentEngine()
        
        val document = mockEngine.create("SALES_ORDER") {
            setField("docDate", LocalDateTime.now().minusDays(5))
            setField("expireDate", LocalDateTime.now().plusDays(10))
            state("CONFIRMED")
        }
        
        // 测试状态检查
        assertTrue(document.isInState("CONFIRMED"))
        assertTrue(document.isInAnyState("DRAFT", "CONFIRMED", "SHIPPED"))
        
        // 测试日期计算
        assertEquals(5, document.getAgeInDays())
        assertEquals(10, document.getRemainingDays())
        assertFalse(document.isExpired())
        
        // 测试单据链
        val nextDocument = mockEngine.create("DELIVERY_ORDER", document.docId) {
            setField("sourceOrderId", document.docId)
        }
        
        val chain = mockEngine.getDocumentChain(nextDocument.docId)
        assertEquals(2, chain.size)
        assertEquals(document.docId, chain[0].docId)
        assertEquals(nextDocument.docId, chain[1].docId)
    }
    
    @Test
    fun `测试Flow扩展函数`() = runTest {
        val mockEngine = MockDocumentEngine()
        
        // 创建测试数据
        val documents = listOf(
            mockEngine.create("SALES_ORDER") {
                setField("partnerId", "CUSTOMER_001")
                setField("docAmount", BigDecimal("1000"))
                state("CONFIRMED")
            },
            mockEngine.create("SALES_ORDER") {
                setField("partnerId", "CUSTOMER_002") 
                setField("docAmount", BigDecimal("2000"))
                state("SHIPPED")
            },
            mockEngine.create("PURCHASE_ORDER") {
                setField("partnerId", "SUPPLIER_001")
                setField("docAmount", BigDecimal("500"))
                state("DRAFT")
            }
        )
        
        val query = query {
            docTypes = listOf("SALES_ORDER", "PURCHASE_ORDER")
        }
        
        val results = mockEngine.find(query)
            .filterByState("CONFIRMED", "SHIPPED")
            .filterByAmountRange(BigDecimal("800"), BigDecimal("2500"))
            .mapToSummary()
            .toList()
        
        assertEquals(2, results.size)
        assertTrue(results.all { it.docType == "销售订单" })
    }
}

/**
 * 模拟文档引擎实现（用于测试）
 */
class MockDocumentEngine : DocumentEngine {
    private val documents = mutableMapOf<String, Document>()
    private var idCounter = 1
    
    override suspend fun getDocument(docId: String): Document? {
        return documents[docId]
    }
    
    override suspend fun getDocument(docType: String, businessKey: String): Document? {
        return documents.values.find { 
            it.docType.docType == docType && it.businessCode == businessKey 
        }
    }
    
    override suspend fun createDocument(
        docType: String,
        preDocId: String?,
        optSource: String?,
        init: suspend DocumentContext.() -> Unit
    ): Document {
        val docId = "DOC_${idCounter++}"
        val mockDocType = createMockDocType(docType)
        
        val document = Document {
            this.docId = docId
            this.docType = mockDocType
            this.preDocId = preDocId
            this.createdTime = LocalDateTime.now()
            this.updatedTime = LocalDateTime.now()
            this.version = 1
            this.deleted = false
        }
        
        val context = MockDocumentContext(document, mockDocType)
        context.init()
        
        documents[docId] = context.document
        return context.document
    }
    
    override suspend fun updateDocument(
        docId: String,
        optSource: String?,
        update: suspend DocumentContext.() -> Unit
    ): Document {
        val document = documents[docId] ?: throw IllegalArgumentException("Document not found: $docId")
        val context = MockDocumentContext(document, document.docType)
        context.update()
        
        documents[docId] = context.document
        return context.document
    }
    
    override suspend fun calculateDocument(
        document: Document,
        fromCard: String?,
        options: Map<String, Any>?
    ): Document {
        return document
    }
    
    override suspend fun deleteDocument(docId: String): Boolean {
        return documents.remove(docId) != null
    }
    
    override fun findDocuments(query: DocumentQuery): kotlinx.coroutines.flow.Flow<Document> {
        return kotlinx.coroutines.flow.flowOf(*documents.values.toTypedArray())
    }
    
    override suspend fun batchProcess(
        docIds: List<String>,
        processor: suspend (Document) -> Document
    ): List<Document> {
        return docIds.mapNotNull { documents[it] }.map { processor(it) }
    }
    
    private fun createMockDocType(docType: String): cn.nkpro.elcube.jimmer.model.DocType {
        return cn.nkpro.elcube.jimmer.model.DocType {
            this.docType = docType
            this.docName = when(docType) {
                "SALES_ORDER" -> "销售订单"
                "PURCHASE_ORDER" -> "采购订单"
                "DELIVERY_ORDER" -> "发货单"
                else -> docType
            }
            this.enabled = true
            this.systemBuiltIn = false
            this.createdTime = LocalDateTime.now()
            this.updatedTime = LocalDateTime.now()
            this.version = 1
            this.deleted = false
        }
    }
}

/**
 * 模拟文档上下文实现（用于测试）
 */
class MockDocumentContext(
    override var document: Document,
    override val docType: cn.nkpro.elcube.jimmer.model.DocType
) : DocumentContext {
    
    private val fieldValues = mutableMapOf<String, Any?>()
    private val cardDataMap = mutableMapOf<String, Any?>()
    
    override fun setField(fieldPath: String, value: Any?) {
        fieldValues[fieldPath] = value
    }
    
    override fun <T> getField(fieldPath: String): T? {
        @Suppress("UNCHECKED_CAST")
        return fieldValues[fieldPath] as? T
    }
    
    override fun setCardData(cardCode: String, data: Any?) {
        cardDataMap[cardCode] = data
    }
    
    override fun <T> getCardData(cardCode: String): T? {
        @Suppress("UNCHECKED_CAST")
        return cardDataMap[cardCode] as? T
    }
    
    override suspend fun <T> evaluateExpression(expression: String): T? {
        // 简单的模拟实现
        return null
    }
    
    override suspend fun triggerCardCalculation(cardCode: String, options: Map<String, Any>?) {
        // 模拟实现
    }
    
    override suspend fun changeState(targetState: String, reason: String?) {
        // 模拟状态变更
    }
    
    override suspend fun addHistory(operationType: String, description: String, data: Any?) {
        // 模拟添加历史记录
    }
}
