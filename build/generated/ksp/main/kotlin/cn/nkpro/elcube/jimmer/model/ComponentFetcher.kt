@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.Component::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = Component::class)
public fun FetcherCreator<Component>.`by`(block: ComponentFetcherDsl.() -> Unit): Fetcher<Component> {
    val dsl = ComponentFetcherDsl(emptyComponentFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = Component::class)
public fun FetcherCreator<Component>.`by`(base: Fetcher<Component>?, block: ComponentFetcherDsl.() -> Unit): Fetcher<Component> {
    val dsl = ComponentFetcherDsl(base ?: emptyComponentFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = Component::class)
public class ComponentFetcherDsl(
    fetcher: Fetcher<Component> = emptyComponentFetcher,
) {
    private var _fetcher: Fetcher<Component> = fetcher

    public fun internallyGetFetcher(): Fetcher<Component> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun componentCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentCode")
        } else {
            _fetcher.remove("componentCode")
        }
    }

    public fun componentName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentName")
        } else {
            _fetcher.remove("componentName")
        }
    }

    public fun componentType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentType")
        } else {
            _fetcher.remove("componentType")
        }
    }

    public fun componentCategory(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentCategory")
        } else {
            _fetcher.remove("componentCategory")
        }
    }

    public fun componentVersion(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentVersion")
        } else {
            _fetcher.remove("componentVersion")
        }
    }

    public fun componentDesc(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentDesc")
        } else {
            _fetcher.remove("componentDesc")
        }
    }

    public fun componentIcon(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentIcon")
        } else {
            _fetcher.remove("componentIcon")
        }
    }

    public fun systemBuiltIn(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("systemBuiltIn")
        } else {
            _fetcher.remove("systemBuiltIn")
        }
    }

    public fun enabled(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("enabled")
        } else {
            _fetcher.remove("enabled")
        }
    }

    public fun groovyScript(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("groovyScript")
        } else {
            _fetcher.remove("groovyScript")
        }
    }

    public fun vueMain(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("vueMain")
        } else {
            _fetcher.remove("vueMain")
        }
    }

    public fun vueDefs(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("vueDefs")
        } else {
            _fetcher.remove("vueDefs")
        }
    }

    public fun configSchema(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("configSchema")
        } else {
            _fetcher.remove("configSchema")
        }
    }

    public fun dependencies(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("dependencies")
        } else {
            _fetcher.remove("dependencies")
        }
    }

    public fun dependencies(childFetcher: Fetcher<ComponentDependency>) {
        _fetcher = _fetcher.add(
            "dependencies",
            childFetcher
        )
    }

    public fun dependencies(childFetcher: Fetcher<ComponentDependency>, cfgBlock: (KListFieldDsl<ComponentDependency>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "dependencies",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun dependencies(childBlock: ComponentDependencyFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "dependencies",
            ComponentDependencyFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun dependencies(cfgBlock: (KListFieldDsl<ComponentDependency>.() -> Unit)?, childBlock: ComponentDependencyFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "dependencies",
            ComponentDependencyFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun dependencies(enabled: Boolean, childFetcher: Fetcher<ComponentDependency>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("dependencies")
        } else {
            dependencies(childFetcher)
        }
    }

    public fun dependencies(
        enabled: Boolean,
        childFetcher: Fetcher<ComponentDependency>,
        cfgBlock: (KListFieldDsl<ComponentDependency>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("dependencies")
        } else {
            dependencies(childFetcher, cfgBlock)
        }
    }

    public fun dependencies(enabled: Boolean, childBlock: ComponentDependencyFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("dependencies")
        } else {
            dependencies(childBlock)
        }
    }

    public fun dependencies(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<ComponentDependency>.() -> Unit)?,
        childBlock: ComponentDependencyFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("dependencies")
        } else {
            dependencies(cfgBlock, childBlock)
        }
    }

    public fun versions(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("versions")
        } else {
            _fetcher.remove("versions")
        }
    }

    public fun versions(childFetcher: Fetcher<ComponentVersion>) {
        _fetcher = _fetcher.add(
            "versions",
            childFetcher
        )
    }

    public fun versions(childFetcher: Fetcher<ComponentVersion>, cfgBlock: (KListFieldDsl<ComponentVersion>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "versions",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun versions(childBlock: ComponentVersionFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "versions",
            ComponentVersionFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun versions(cfgBlock: (KListFieldDsl<ComponentVersion>.() -> Unit)?, childBlock: ComponentVersionFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "versions",
            ComponentVersionFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun versions(enabled: Boolean, childFetcher: Fetcher<ComponentVersion>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("versions")
        } else {
            versions(childFetcher)
        }
    }

    public fun versions(
        enabled: Boolean,
        childFetcher: Fetcher<ComponentVersion>,
        cfgBlock: (KListFieldDsl<ComponentVersion>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("versions")
        } else {
            versions(childFetcher, cfgBlock)
        }
    }

    public fun versions(enabled: Boolean, childBlock: ComponentVersionFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("versions")
        } else {
            versions(childBlock)
        }
    }

    public fun versions(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<ComponentVersion>.() -> Unit)?,
        childBlock: ComponentVersionFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("versions")
        } else {
            versions(cfgBlock, childBlock)
        }
    }
}

private val emptyComponentFetcher: Fetcher<Component> = FetcherImpl(Component::class.java)
