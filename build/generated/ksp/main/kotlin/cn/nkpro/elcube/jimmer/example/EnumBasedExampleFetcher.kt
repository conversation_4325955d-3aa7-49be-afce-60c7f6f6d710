@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderEnum::class)

package cn.nkpro.elcube.jimmer.example

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator

@GeneratedBy(type = SalesOrderEnum::class)
public fun FetcherCreator<SalesOrderEnum>.`by`(block: SalesOrderEnumFetcherDsl.() -> Unit): Fetcher<SalesOrderEnum> {
    val dsl = SalesOrderEnumFetcherDsl(emptySalesOrderEnumFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = SalesOrderEnum::class)
public fun FetcherCreator<SalesOrderEnum>.`by`(base: Fetcher<SalesOrderEnum>?, block: SalesOrderEnumFetcherDsl.() -> Unit): Fetcher<SalesOrderEnum> {
    val dsl = SalesOrderEnumFetcherDsl(base ?: emptySalesOrderEnumFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = SalesOrderEnum::class)
public class SalesOrderEnumFetcherDsl(
    fetcher: Fetcher<SalesOrderEnum> = emptySalesOrderEnumFetcher,
) {
    private var _fetcher: Fetcher<SalesOrderEnum> = fetcher

    public fun internallyGetFetcher(): Fetcher<SalesOrderEnum> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createTime")
        } else {
            _fetcher.remove("createTime")
        }
    }

    public fun updateTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updateTime")
        } else {
            _fetcher.remove("updateTime")
        }
    }

    public fun createBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createBy")
        } else {
            _fetcher.remove("createBy")
        }
    }

    public fun updateBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updateBy")
        } else {
            _fetcher.remove("updateBy")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun orderNo(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderNo")
        } else {
            _fetcher.remove("orderNo")
        }
    }

    public fun customerName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("customerName")
        } else {
            _fetcher.remove("customerName")
        }
    }

    public fun totalAmount(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("totalAmount")
        } else {
            _fetcher.remove("totalAmount")
        }
    }

    public fun orderDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderDate")
        } else {
            _fetcher.remove("orderDate")
        }
    }

    public fun deliveryDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deliveryDate")
        } else {
            _fetcher.remove("deliveryDate")
        }
    }

    public fun status(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("status")
        } else {
            _fetcher.remove("status")
        }
    }

    public fun priority(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("priority")
        } else {
            _fetcher.remove("priority")
        }
    }

    public fun paymentStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("paymentStatus")
        } else {
            _fetcher.remove("paymentStatus")
        }
    }

    public fun invoiceType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("invoiceType")
        } else {
            _fetcher.remove("invoiceType")
        }
    }

    public fun remark(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("remark")
        } else {
            _fetcher.remove("remark")
        }
    }
}

private val emptySalesOrderEnumFetcher: Fetcher<SalesOrderEnum> =
        FetcherImpl(SalesOrderEnum::class.java)
