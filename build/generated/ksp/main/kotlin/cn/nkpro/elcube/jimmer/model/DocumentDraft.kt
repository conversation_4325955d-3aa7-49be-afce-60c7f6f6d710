@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = Document::class)
public interface DocumentDraft : Document, BusinessEntityDraft {
    override var docId: String

    override var docNo: String?

    override var docName: String?

    override var docType: DocType

    @get:JsonIgnore
    public var docTypeId: String

    override var currentState: DocState?

    @get:JsonIgnore
    public var currentStateId: String?

    override var preDocId: String?

    override var preDocument: Document?

    @get:JsonIgnore
    public var preDocumentId: String?

    override var nextDocuments: List<Document>

    override var partnerId: String?

    override var partnerName: String?

    override var docAmount: BigDecimal?

    override var currency: String?

    override var docDate: LocalDateTime?

    override var effectiveDate: LocalDateTime?

    override var expireDate: LocalDateTime?

    override var docData: String?

    override var extProps: String?

    override var cardData: List<DocumentCardData>

    override var histories: List<DocumentHistory>

    override var indexData: List<DocumentIndexData>

    public fun docType(): DocTypeDraft

    public fun docType(block: DocTypeDraft.() -> Unit)

    public fun currentState(): DocStateDraft

    public fun currentState(block: DocStateDraft.() -> Unit)

    public fun preDocument(): DocumentDraft

    public fun preDocument(block: DocumentDraft.() -> Unit)

    public fun nextDocuments(): MutableList<DocumentDraft>

    public fun cardData(): MutableList<DocumentCardDataDraft>

    public fun histories(): MutableList<DocumentHistoryDraft>

    public fun indexData(): MutableList<DocumentIndexDataDraft>

    @GeneratedBy(type = Document::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_DOC_ID: Int = 14

        public const val SLOT_DOC_NO: Int = 15

        public const val SLOT_DOC_NAME: Int = 16

        public const val SLOT_DOC_TYPE: Int = 17

        public const val SLOT_CURRENT_STATE: Int = 18

        public const val SLOT_PRE_DOC_ID: Int = 19

        public const val SLOT_PRE_DOCUMENT: Int = 20

        public const val SLOT_NEXT_DOCUMENTS: Int = 21

        public const val SLOT_PARTNER_ID: Int = 22

        public const val SLOT_PARTNER_NAME: Int = 23

        public const val SLOT_DOC_AMOUNT: Int = 24

        public const val SLOT_CURRENCY: Int = 25

        public const val SLOT_DOC_DATE: Int = 26

        public const val SLOT_EFFECTIVE_DATE: Int = 27

        public const val SLOT_EXPIRE_DATE: Int = 28

        public const val SLOT_DOC_DATA: Int = 29

        public const val SLOT_EXT_PROPS: Int = 30

        public const val SLOT_CARD_DATA: Int = 31

        public const val SLOT_HISTORIES: Int = 32

        public const val SLOT_INDEX_DATA: Int = 33

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                Document::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as Document?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_DOC_ID, "docId", String::class.java, false)
            .add(SLOT_DOC_NO, "docNo", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_NAME, "docName", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_TYPE, "docType", ManyToOne::class.java, DocType::class.java, false)
            .add(SLOT_CURRENT_STATE, "currentState", ManyToOne::class.java, DocState::class.java, true)
            .add(SLOT_PRE_DOC_ID, "preDocId", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_PRE_DOCUMENT, "preDocument", ManyToOne::class.java, Document::class.java, true)
            .add(SLOT_NEXT_DOCUMENTS, "nextDocuments", OneToMany::class.java, Document::class.java, false)
            .add(SLOT_PARTNER_ID, "partnerId", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_PARTNER_NAME, "partnerName", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_AMOUNT, "docAmount", ImmutablePropCategory.SCALAR, BigDecimal::class.java, true)
            .add(SLOT_CURRENCY, "currency", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_DATE, "docDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, true)
            .add(SLOT_EFFECTIVE_DATE, "effectiveDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, true)
            .add(SLOT_EXPIRE_DATE, "expireDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, true)
            .add(SLOT_DOC_DATA, "docData", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_EXT_PROPS, "extProps", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_CARD_DATA, "cardData", OneToMany::class.java, DocumentCardData::class.java, false)
            .add(SLOT_HISTORIES, "histories", OneToMany::class.java, DocumentHistory::class.java, false)
            .add(SLOT_INDEX_DATA, "indexData", OneToMany::class.java, DocumentIndexData::class.java, false)
            .build()

        public fun produce(base: Document? = null, resolveImmediately: Boolean = false): Document {
            val consumer = DraftConsumer<DocumentDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as Document
        }

        public fun produce(
            base: Document? = null,
            resolveImmediately: Boolean = false,
            block: DocumentDraft.() -> Unit,
        ): Document {
            val consumer = DraftConsumer<DocumentDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as Document
        }

        @GeneratedBy(type = Document::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "docId", "docNo", "docName", "docType", "currentState", "preDocId", "preDocument", "nextDocuments", "partnerId", "partnerName", "docAmount", "currency", "docDate", "effectiveDate", "expireDate", "docData", "extProps", "cardData", "histories", "indexData")
        private abstract interface Implementor : Document, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_DOC_ID ->
                	docId
                SLOT_DOC_NO ->
                	docNo
                SLOT_DOC_NAME ->
                	docName
                SLOT_DOC_TYPE ->
                	docType
                SLOT_CURRENT_STATE ->
                	currentState
                SLOT_PRE_DOC_ID ->
                	preDocId
                SLOT_PRE_DOCUMENT ->
                	preDocument
                SLOT_NEXT_DOCUMENTS ->
                	nextDocuments
                SLOT_PARTNER_ID ->
                	partnerId
                SLOT_PARTNER_NAME ->
                	partnerName
                SLOT_DOC_AMOUNT ->
                	docAmount
                SLOT_CURRENCY ->
                	currency
                SLOT_DOC_DATE ->
                	docDate
                SLOT_EFFECTIVE_DATE ->
                	effectiveDate
                SLOT_EXPIRE_DATE ->
                	expireDate
                SLOT_DOC_DATA ->
                	docData
                SLOT_EXT_PROPS ->
                	extProps
                SLOT_CARD_DATA ->
                	cardData
                SLOT_HISTORIES ->
                	histories
                SLOT_INDEX_DATA ->
                	indexData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "docId" ->
                	docId
                "docNo" ->
                	docNo
                "docName" ->
                	docName
                "docType" ->
                	docType
                "currentState" ->
                	currentState
                "preDocId" ->
                	preDocId
                "preDocument" ->
                	preDocument
                "nextDocuments" ->
                	nextDocuments
                "partnerId" ->
                	partnerId
                "partnerName" ->
                	partnerName
                "docAmount" ->
                	docAmount
                "currency" ->
                	currency
                "docDate" ->
                	docDate
                "effectiveDate" ->
                	effectiveDate
                "expireDate" ->
                	expireDate
                "docData" ->
                	docData
                "extProps" ->
                	extProps
                "cardData" ->
                	cardData
                "histories" ->
                	histories
                "indexData" ->
                	indexData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = Document::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docIdValue: String? = null

            @get:JsonIgnore
            internal var __docNoValue: String? = null

            @get:JsonIgnore
            internal var __docNoLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docNameValue: String? = null

            @get:JsonIgnore
            internal var __docNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docTypeValue: DocType? = null

            @get:JsonIgnore
            internal var __currentStateValue: DocState? = null

            @get:JsonIgnore
            internal var __currentStateLoaded: Boolean = false

            @get:JsonIgnore
            internal var __preDocIdValue: String? = null

            @get:JsonIgnore
            internal var __preDocIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __preDocumentValue: Document? = null

            @get:JsonIgnore
            internal var __preDocumentLoaded: Boolean = false

            @get:JsonIgnore
            internal var __nextDocumentsValue: NonSharedList<Document>? = null

            @get:JsonIgnore
            internal var __partnerIdValue: String? = null

            @get:JsonIgnore
            internal var __partnerIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __partnerNameValue: String? = null

            @get:JsonIgnore
            internal var __partnerNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docAmountValue: BigDecimal? = null

            @get:JsonIgnore
            internal var __docAmountLoaded: Boolean = false

            @get:JsonIgnore
            internal var __currencyValue: String? = null

            @get:JsonIgnore
            internal var __currencyLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __docDateLoaded: Boolean = false

            @get:JsonIgnore
            internal var __effectiveDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __effectiveDateLoaded: Boolean = false

            @get:JsonIgnore
            internal var __expireDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __expireDateLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docDataValue: String? = null

            @get:JsonIgnore
            internal var __docDataLoaded: Boolean = false

            @get:JsonIgnore
            internal var __extPropsValue: String? = null

            @get:JsonIgnore
            internal var __extPropsLoaded: Boolean = false

            @get:JsonIgnore
            internal var __cardDataValue: NonSharedList<DocumentCardData>? = null

            @get:JsonIgnore
            internal var __historiesValue: NonSharedList<DocumentHistory>? = null

            @get:JsonIgnore
            internal var __indexDataValue: NonSharedList<DocumentIndexData>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(Document::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(Document::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(Document::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(Document::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(Document::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(Document::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(Document::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(Document::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(Document::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(Document::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(Document::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(Document::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(Document::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(Document::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val docId: String
                get() {
                    val __docIdValue = this.__docIdValue
                    if (__docIdValue === null) {
                        throw UnloadedException(Document::class.java, "docId")
                    }
                    return __docIdValue
                }

            override val docNo: String?
                get() {
                    if (!__docNoLoaded) {
                        throw UnloadedException(Document::class.java, "docNo")
                    }
                    return __docNoValue
                }

            override val docName: String?
                get() {
                    if (!__docNameLoaded) {
                        throw UnloadedException(Document::class.java, "docName")
                    }
                    return __docNameValue
                }

            override val docType: DocType
                get() {
                    val __docTypeValue = this.__docTypeValue
                    if (__docTypeValue === null) {
                        throw UnloadedException(Document::class.java, "docType")
                    }
                    return __docTypeValue
                }

            override val currentState: DocState?
                get() {
                    if (!__currentStateLoaded) {
                        throw UnloadedException(Document::class.java, "currentState")
                    }
                    return __currentStateValue
                }

            override val preDocId: String?
                get() {
                    if (!__preDocIdLoaded) {
                        throw UnloadedException(Document::class.java, "preDocId")
                    }
                    return __preDocIdValue
                }

            override val preDocument: Document?
                get() {
                    if (!__preDocumentLoaded) {
                        throw UnloadedException(Document::class.java, "preDocument")
                    }
                    return __preDocumentValue
                }

            override val nextDocuments: List<Document>
                get() {
                    val __nextDocumentsValue = this.__nextDocumentsValue
                    if (__nextDocumentsValue === null) {
                        throw UnloadedException(Document::class.java, "nextDocuments")
                    }
                    return __nextDocumentsValue
                }

            override val partnerId: String?
                get() {
                    if (!__partnerIdLoaded) {
                        throw UnloadedException(Document::class.java, "partnerId")
                    }
                    return __partnerIdValue
                }

            override val partnerName: String?
                get() {
                    if (!__partnerNameLoaded) {
                        throw UnloadedException(Document::class.java, "partnerName")
                    }
                    return __partnerNameValue
                }

            override val docAmount: BigDecimal?
                get() {
                    if (!__docAmountLoaded) {
                        throw UnloadedException(Document::class.java, "docAmount")
                    }
                    return __docAmountValue
                }

            override val currency: String?
                get() {
                    if (!__currencyLoaded) {
                        throw UnloadedException(Document::class.java, "currency")
                    }
                    return __currencyValue
                }

            override val docDate: LocalDateTime?
                get() {
                    if (!__docDateLoaded) {
                        throw UnloadedException(Document::class.java, "docDate")
                    }
                    return __docDateValue
                }

            override val effectiveDate: LocalDateTime?
                get() {
                    if (!__effectiveDateLoaded) {
                        throw UnloadedException(Document::class.java, "effectiveDate")
                    }
                    return __effectiveDateValue
                }

            override val expireDate: LocalDateTime?
                get() {
                    if (!__expireDateLoaded) {
                        throw UnloadedException(Document::class.java, "expireDate")
                    }
                    return __expireDateValue
                }

            override val docData: String?
                get() {
                    if (!__docDataLoaded) {
                        throw UnloadedException(Document::class.java, "docData")
                    }
                    return __docDataValue
                }

            override val extProps: String?
                get() {
                    if (!__extPropsLoaded) {
                        throw UnloadedException(Document::class.java, "extProps")
                    }
                    return __extPropsValue
                }

            override val cardData: List<DocumentCardData>
                get() {
                    val __cardDataValue = this.__cardDataValue
                    if (__cardDataValue === null) {
                        throw UnloadedException(Document::class.java, "cardData")
                    }
                    return __cardDataValue
                }

            override val histories: List<DocumentHistory>
                get() {
                    val __historiesValue = this.__historiesValue
                    if (__historiesValue === null) {
                        throw UnloadedException(Document::class.java, "histories")
                    }
                    return __historiesValue
                }

            override val indexData: List<DocumentIndexData>
                get() {
                    val __indexDataValue = this.__indexDataValue
                    if (__indexDataValue === null) {
                        throw UnloadedException(Document::class.java, "indexData")
                    }
                    return __indexDataValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_DOC_ID ->
                	__docIdValue !== null
                SLOT_DOC_NO ->
                	__docNoLoaded
                SLOT_DOC_NAME ->
                	__docNameLoaded
                SLOT_DOC_TYPE ->
                	__docTypeValue !== null
                SLOT_CURRENT_STATE ->
                	__currentStateLoaded
                SLOT_PRE_DOC_ID ->
                	__preDocIdLoaded
                SLOT_PRE_DOCUMENT ->
                	__preDocumentLoaded
                SLOT_NEXT_DOCUMENTS ->
                	__nextDocumentsValue !== null
                SLOT_PARTNER_ID ->
                	__partnerIdLoaded
                SLOT_PARTNER_NAME ->
                	__partnerNameLoaded
                SLOT_DOC_AMOUNT ->
                	__docAmountLoaded
                SLOT_CURRENCY ->
                	__currencyLoaded
                SLOT_DOC_DATE ->
                	__docDateLoaded
                SLOT_EFFECTIVE_DATE ->
                	__effectiveDateLoaded
                SLOT_EXPIRE_DATE ->
                	__expireDateLoaded
                SLOT_DOC_DATA ->
                	__docDataLoaded
                SLOT_EXT_PROPS ->
                	__extPropsLoaded
                SLOT_CARD_DATA ->
                	__cardDataValue !== null
                SLOT_HISTORIES ->
                	__historiesValue !== null
                SLOT_INDEX_DATA ->
                	__indexDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "docId" ->
                	__docIdValue !== null
                "docNo" ->
                	__docNoLoaded
                "docName" ->
                	__docNameLoaded
                "docType" ->
                	__docTypeValue !== null
                "currentState" ->
                	__currentStateLoaded
                "preDocId" ->
                	__preDocIdLoaded
                "preDocument" ->
                	__preDocumentLoaded
                "nextDocuments" ->
                	__nextDocumentsValue !== null
                "partnerId" ->
                	__partnerIdLoaded
                "partnerName" ->
                	__partnerNameLoaded
                "docAmount" ->
                	__docAmountLoaded
                "currency" ->
                	__currencyLoaded
                "docDate" ->
                	__docDateLoaded
                "effectiveDate" ->
                	__effectiveDateLoaded
                "expireDate" ->
                	__expireDateLoaded
                "docData" ->
                	__docDataLoaded
                "extProps" ->
                	__extPropsLoaded
                "cardData" ->
                	__cardDataValue !== null
                "histories" ->
                	__historiesValue !== null
                "indexData" ->
                	__indexDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_DOC_ID ->
                    	__visibility.visible(SLOT_DOC_ID)
                    SLOT_DOC_NO ->
                    	__visibility.visible(SLOT_DOC_NO)
                    SLOT_DOC_NAME ->
                    	__visibility.visible(SLOT_DOC_NAME)
                    SLOT_DOC_TYPE ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    SLOT_CURRENT_STATE ->
                    	__visibility.visible(SLOT_CURRENT_STATE)
                    SLOT_PRE_DOC_ID ->
                    	__visibility.visible(SLOT_PRE_DOC_ID)
                    SLOT_PRE_DOCUMENT ->
                    	__visibility.visible(SLOT_PRE_DOCUMENT)
                    SLOT_NEXT_DOCUMENTS ->
                    	__visibility.visible(SLOT_NEXT_DOCUMENTS)
                    SLOT_PARTNER_ID ->
                    	__visibility.visible(SLOT_PARTNER_ID)
                    SLOT_PARTNER_NAME ->
                    	__visibility.visible(SLOT_PARTNER_NAME)
                    SLOT_DOC_AMOUNT ->
                    	__visibility.visible(SLOT_DOC_AMOUNT)
                    SLOT_CURRENCY ->
                    	__visibility.visible(SLOT_CURRENCY)
                    SLOT_DOC_DATE ->
                    	__visibility.visible(SLOT_DOC_DATE)
                    SLOT_EFFECTIVE_DATE ->
                    	__visibility.visible(SLOT_EFFECTIVE_DATE)
                    SLOT_EXPIRE_DATE ->
                    	__visibility.visible(SLOT_EXPIRE_DATE)
                    SLOT_DOC_DATA ->
                    	__visibility.visible(SLOT_DOC_DATA)
                    SLOT_EXT_PROPS ->
                    	__visibility.visible(SLOT_EXT_PROPS)
                    SLOT_CARD_DATA ->
                    	__visibility.visible(SLOT_CARD_DATA)
                    SLOT_HISTORIES ->
                    	__visibility.visible(SLOT_HISTORIES)
                    SLOT_INDEX_DATA ->
                    	__visibility.visible(SLOT_INDEX_DATA)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "docId" ->
                    	__visibility.visible(SLOT_DOC_ID)
                    "docNo" ->
                    	__visibility.visible(SLOT_DOC_NO)
                    "docName" ->
                    	__visibility.visible(SLOT_DOC_NAME)
                    "docType" ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    "currentState" ->
                    	__visibility.visible(SLOT_CURRENT_STATE)
                    "preDocId" ->
                    	__visibility.visible(SLOT_PRE_DOC_ID)
                    "preDocument" ->
                    	__visibility.visible(SLOT_PRE_DOCUMENT)
                    "nextDocuments" ->
                    	__visibility.visible(SLOT_NEXT_DOCUMENTS)
                    "partnerId" ->
                    	__visibility.visible(SLOT_PARTNER_ID)
                    "partnerName" ->
                    	__visibility.visible(SLOT_PARTNER_NAME)
                    "docAmount" ->
                    	__visibility.visible(SLOT_DOC_AMOUNT)
                    "currency" ->
                    	__visibility.visible(SLOT_CURRENCY)
                    "docDate" ->
                    	__visibility.visible(SLOT_DOC_DATE)
                    "effectiveDate" ->
                    	__visibility.visible(SLOT_EFFECTIVE_DATE)
                    "expireDate" ->
                    	__visibility.visible(SLOT_EXPIRE_DATE)
                    "docData" ->
                    	__visibility.visible(SLOT_DOC_DATA)
                    "extProps" ->
                    	__visibility.visible(SLOT_EXT_PROPS)
                    "cardData" ->
                    	__visibility.visible(SLOT_CARD_DATA)
                    "histories" ->
                    	__visibility.visible(SLOT_HISTORIES)
                    "indexData" ->
                    	__visibility.visible(SLOT_INDEX_DATA)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__docIdValue !== null) {
                    hash = 31 * hash + __docIdValue.hashCode()
                }
                if (__docNoLoaded) {
                    hash = 31 * hash + (__docNoValue?.hashCode() ?: 0)
                }
                if (__docNameLoaded) {
                    hash = 31 * hash + (__docNameValue?.hashCode() ?: 0)
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__docTypeValue)
                }
                if (__currentStateLoaded) {
                    hash = 31 * hash + System.identityHashCode(__currentStateValue)
                }
                if (__preDocIdLoaded) {
                    hash = 31 * hash + (__preDocIdValue?.hashCode() ?: 0)
                }
                if (__preDocumentLoaded) {
                    hash = 31 * hash + System.identityHashCode(__preDocumentValue)
                }
                if (__nextDocumentsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__nextDocumentsValue)
                }
                if (__partnerIdLoaded) {
                    hash = 31 * hash + (__partnerIdValue?.hashCode() ?: 0)
                }
                if (__partnerNameLoaded) {
                    hash = 31 * hash + (__partnerNameValue?.hashCode() ?: 0)
                }
                if (__docAmountLoaded) {
                    hash = 31 * hash + (__docAmountValue?.hashCode() ?: 0)
                }
                if (__currencyLoaded) {
                    hash = 31 * hash + (__currencyValue?.hashCode() ?: 0)
                }
                if (__docDateLoaded) {
                    hash = 31 * hash + (__docDateValue?.hashCode() ?: 0)
                }
                if (__effectiveDateLoaded) {
                    hash = 31 * hash + (__effectiveDateValue?.hashCode() ?: 0)
                }
                if (__expireDateLoaded) {
                    hash = 31 * hash + (__expireDateValue?.hashCode() ?: 0)
                }
                if (__docDataLoaded) {
                    hash = 31 * hash + (__docDataValue?.hashCode() ?: 0)
                }
                if (__extPropsLoaded) {
                    hash = 31 * hash + (__extPropsValue?.hashCode() ?: 0)
                }
                if (__cardDataValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__cardDataValue)
                }
                if (__historiesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__historiesValue)
                }
                if (__indexDataValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__indexDataValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__docIdValue !== null) {
                    hash = 31 * hash + __docIdValue.hashCode()
                }
                if (__docNoLoaded) {
                    hash = 31 * hash + (__docNoValue?.hashCode() ?: 0)
                }
                if (__docNameLoaded) {
                    hash = 31 * hash + (__docNameValue?.hashCode() ?: 0)
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + __docTypeValue.hashCode()
                }
                if (__currentStateLoaded) {
                    hash = 31 * hash + (__currentStateValue?.hashCode() ?: 0)
                }
                if (__preDocIdLoaded) {
                    hash = 31 * hash + (__preDocIdValue?.hashCode() ?: 0)
                }
                if (__preDocumentLoaded) {
                    hash = 31 * hash + (__preDocumentValue?.hashCode() ?: 0)
                }
                if (__nextDocumentsValue !== null) {
                    hash = 31 * hash + __nextDocumentsValue.hashCode()
                }
                if (__partnerIdLoaded) {
                    hash = 31 * hash + (__partnerIdValue?.hashCode() ?: 0)
                }
                if (__partnerNameLoaded) {
                    hash = 31 * hash + (__partnerNameValue?.hashCode() ?: 0)
                }
                if (__docAmountLoaded) {
                    hash = 31 * hash + (__docAmountValue?.hashCode() ?: 0)
                }
                if (__currencyLoaded) {
                    hash = 31 * hash + (__currencyValue?.hashCode() ?: 0)
                }
                if (__docDateLoaded) {
                    hash = 31 * hash + (__docDateValue?.hashCode() ?: 0)
                }
                if (__effectiveDateLoaded) {
                    hash = 31 * hash + (__effectiveDateValue?.hashCode() ?: 0)
                }
                if (__expireDateLoaded) {
                    hash = 31 * hash + (__expireDateValue?.hashCode() ?: 0)
                }
                if (__docDataLoaded) {
                    hash = 31 * hash + (__docDataValue?.hashCode() ?: 0)
                }
                if (__extPropsLoaded) {
                    hash = 31 * hash + (__extPropsValue?.hashCode() ?: 0)
                }
                if (__cardDataValue !== null) {
                    hash = 31 * hash + __cardDataValue.hashCode()
                }
                if (__historiesValue !== null) {
                    hash = 31 * hash + __historiesValue.hashCode()
                }
                if (__indexDataValue !== null) {
                    hash = 31 * hash + __indexDataValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_ID))) {
                    return false
                }
                val __docIdLoaded = 
                    this.__docIdValue !== null
                if (__docIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_ID)))) {
                    return false
                }
                if (__docIdLoaded && this.__docIdValue != __other.docId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NO)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NO))) {
                    return false
                }
                val __docNoLoaded = 
                    this.__docNoLoaded
                if (__docNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NO)))) {
                    return false
                }
                if (__docNoLoaded && this.__docNoValue != __other.docNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NAME))) {
                    return false
                }
                val __docNameLoaded = 
                    this.__docNameLoaded
                if (__docNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NAME)))) {
                    return false
                }
                if (__docNameLoaded && this.__docNameValue != __other.docName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue !== __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CURRENT_STATE)) != __other.__isVisible(PropId.byIndex(SLOT_CURRENT_STATE))) {
                    return false
                }
                val __currentStateLoaded = 
                    this.__currentStateLoaded
                if (__currentStateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CURRENT_STATE)))) {
                    return false
                }
                if (__currentStateLoaded && this.__currentStateValue !== __other.currentState) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRE_DOC_ID)) != __other.__isVisible(PropId.byIndex(SLOT_PRE_DOC_ID))) {
                    return false
                }
                val __preDocIdLoaded = 
                    this.__preDocIdLoaded
                if (__preDocIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRE_DOC_ID)))) {
                    return false
                }
                if (__preDocIdLoaded && this.__preDocIdValue != __other.preDocId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRE_DOCUMENT)) != __other.__isVisible(PropId.byIndex(SLOT_PRE_DOCUMENT))) {
                    return false
                }
                val __preDocumentLoaded = 
                    this.__preDocumentLoaded
                if (__preDocumentLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRE_DOCUMENT)))) {
                    return false
                }
                if (__preDocumentLoaded && this.__preDocumentValue !== __other.preDocument) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_NEXT_DOCUMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_NEXT_DOCUMENTS))) {
                    return false
                }
                val __nextDocumentsLoaded = 
                    this.__nextDocumentsValue !== null
                if (__nextDocumentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_NEXT_DOCUMENTS)))) {
                    return false
                }
                if (__nextDocumentsLoaded && this.__nextDocumentsValue !== __other.nextDocuments) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PARTNER_ID)) != __other.__isVisible(PropId.byIndex(SLOT_PARTNER_ID))) {
                    return false
                }
                val __partnerIdLoaded = 
                    this.__partnerIdLoaded
                if (__partnerIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PARTNER_ID)))) {
                    return false
                }
                if (__partnerIdLoaded && this.__partnerIdValue != __other.partnerId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PARTNER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_PARTNER_NAME))) {
                    return false
                }
                val __partnerNameLoaded = 
                    this.__partnerNameLoaded
                if (__partnerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PARTNER_NAME)))) {
                    return false
                }
                if (__partnerNameLoaded && this.__partnerNameValue != __other.partnerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_AMOUNT))) {
                    return false
                }
                val __docAmountLoaded = 
                    this.__docAmountLoaded
                if (__docAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_AMOUNT)))) {
                    return false
                }
                if (__docAmountLoaded && this.__docAmountValue != __other.docAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CURRENCY)) != __other.__isVisible(PropId.byIndex(SLOT_CURRENCY))) {
                    return false
                }
                val __currencyLoaded = 
                    this.__currencyLoaded
                if (__currencyLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CURRENCY)))) {
                    return false
                }
                if (__currencyLoaded && this.__currencyValue != __other.currency) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DATE))) {
                    return false
                }
                val __docDateLoaded = 
                    this.__docDateLoaded
                if (__docDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DATE)))) {
                    return false
                }
                if (__docDateLoaded && this.__docDateValue != __other.docDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EFFECTIVE_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_EFFECTIVE_DATE))) {
                    return false
                }
                val __effectiveDateLoaded = 
                    this.__effectiveDateLoaded
                if (__effectiveDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EFFECTIVE_DATE)))) {
                    return false
                }
                if (__effectiveDateLoaded && this.__effectiveDateValue != __other.effectiveDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXPIRE_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_EXPIRE_DATE))) {
                    return false
                }
                val __expireDateLoaded = 
                    this.__expireDateLoaded
                if (__expireDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXPIRE_DATE)))) {
                    return false
                }
                if (__expireDateLoaded && this.__expireDateValue != __other.expireDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DATA))) {
                    return false
                }
                val __docDataLoaded = 
                    this.__docDataLoaded
                if (__docDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DATA)))) {
                    return false
                }
                if (__docDataLoaded && this.__docDataValue != __other.docData) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXT_PROPS)) != __other.__isVisible(PropId.byIndex(SLOT_EXT_PROPS))) {
                    return false
                }
                val __extPropsLoaded = 
                    this.__extPropsLoaded
                if (__extPropsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXT_PROPS)))) {
                    return false
                }
                if (__extPropsLoaded && this.__extPropsValue != __other.extProps) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DATA))) {
                    return false
                }
                val __cardDataLoaded = 
                    this.__cardDataValue !== null
                if (__cardDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DATA)))) {
                    return false
                }
                if (__cardDataLoaded && this.__cardDataValue !== __other.cardData) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_HISTORIES)) != __other.__isVisible(PropId.byIndex(SLOT_HISTORIES))) {
                    return false
                }
                val __historiesLoaded = 
                    this.__historiesValue !== null
                if (__historiesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_HISTORIES)))) {
                    return false
                }
                if (__historiesLoaded && this.__historiesValue !== __other.histories) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_DATA))) {
                    return false
                }
                val __indexDataLoaded = 
                    this.__indexDataValue !== null
                if (__indexDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_DATA)))) {
                    return false
                }
                if (__indexDataLoaded && this.__indexDataValue !== __other.indexData) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_ID))) {
                    return false
                }
                val __docIdLoaded = 
                    this.__docIdValue !== null
                if (__docIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_ID)))) {
                    return false
                }
                if (__docIdLoaded && this.__docIdValue != __other.docId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NO)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NO))) {
                    return false
                }
                val __docNoLoaded = 
                    this.__docNoLoaded
                if (__docNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NO)))) {
                    return false
                }
                if (__docNoLoaded && this.__docNoValue != __other.docNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NAME))) {
                    return false
                }
                val __docNameLoaded = 
                    this.__docNameLoaded
                if (__docNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NAME)))) {
                    return false
                }
                if (__docNameLoaded && this.__docNameValue != __other.docName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue != __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CURRENT_STATE)) != __other.__isVisible(PropId.byIndex(SLOT_CURRENT_STATE))) {
                    return false
                }
                val __currentStateLoaded = 
                    this.__currentStateLoaded
                if (__currentStateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CURRENT_STATE)))) {
                    return false
                }
                if (__currentStateLoaded && this.__currentStateValue != __other.currentState) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRE_DOC_ID)) != __other.__isVisible(PropId.byIndex(SLOT_PRE_DOC_ID))) {
                    return false
                }
                val __preDocIdLoaded = 
                    this.__preDocIdLoaded
                if (__preDocIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRE_DOC_ID)))) {
                    return false
                }
                if (__preDocIdLoaded && this.__preDocIdValue != __other.preDocId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRE_DOCUMENT)) != __other.__isVisible(PropId.byIndex(SLOT_PRE_DOCUMENT))) {
                    return false
                }
                val __preDocumentLoaded = 
                    this.__preDocumentLoaded
                if (__preDocumentLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRE_DOCUMENT)))) {
                    return false
                }
                if (__preDocumentLoaded && this.__preDocumentValue != __other.preDocument) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_NEXT_DOCUMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_NEXT_DOCUMENTS))) {
                    return false
                }
                val __nextDocumentsLoaded = 
                    this.__nextDocumentsValue !== null
                if (__nextDocumentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_NEXT_DOCUMENTS)))) {
                    return false
                }
                if (__nextDocumentsLoaded && this.__nextDocumentsValue != __other.nextDocuments) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PARTNER_ID)) != __other.__isVisible(PropId.byIndex(SLOT_PARTNER_ID))) {
                    return false
                }
                val __partnerIdLoaded = 
                    this.__partnerIdLoaded
                if (__partnerIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PARTNER_ID)))) {
                    return false
                }
                if (__partnerIdLoaded && this.__partnerIdValue != __other.partnerId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PARTNER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_PARTNER_NAME))) {
                    return false
                }
                val __partnerNameLoaded = 
                    this.__partnerNameLoaded
                if (__partnerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PARTNER_NAME)))) {
                    return false
                }
                if (__partnerNameLoaded && this.__partnerNameValue != __other.partnerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_AMOUNT))) {
                    return false
                }
                val __docAmountLoaded = 
                    this.__docAmountLoaded
                if (__docAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_AMOUNT)))) {
                    return false
                }
                if (__docAmountLoaded && this.__docAmountValue != __other.docAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CURRENCY)) != __other.__isVisible(PropId.byIndex(SLOT_CURRENCY))) {
                    return false
                }
                val __currencyLoaded = 
                    this.__currencyLoaded
                if (__currencyLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CURRENCY)))) {
                    return false
                }
                if (__currencyLoaded && this.__currencyValue != __other.currency) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DATE))) {
                    return false
                }
                val __docDateLoaded = 
                    this.__docDateLoaded
                if (__docDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DATE)))) {
                    return false
                }
                if (__docDateLoaded && this.__docDateValue != __other.docDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EFFECTIVE_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_EFFECTIVE_DATE))) {
                    return false
                }
                val __effectiveDateLoaded = 
                    this.__effectiveDateLoaded
                if (__effectiveDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EFFECTIVE_DATE)))) {
                    return false
                }
                if (__effectiveDateLoaded && this.__effectiveDateValue != __other.effectiveDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXPIRE_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_EXPIRE_DATE))) {
                    return false
                }
                val __expireDateLoaded = 
                    this.__expireDateLoaded
                if (__expireDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXPIRE_DATE)))) {
                    return false
                }
                if (__expireDateLoaded && this.__expireDateValue != __other.expireDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DATA))) {
                    return false
                }
                val __docDataLoaded = 
                    this.__docDataLoaded
                if (__docDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DATA)))) {
                    return false
                }
                if (__docDataLoaded && this.__docDataValue != __other.docData) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXT_PROPS)) != __other.__isVisible(PropId.byIndex(SLOT_EXT_PROPS))) {
                    return false
                }
                val __extPropsLoaded = 
                    this.__extPropsLoaded
                if (__extPropsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXT_PROPS)))) {
                    return false
                }
                if (__extPropsLoaded && this.__extPropsValue != __other.extProps) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DATA))) {
                    return false
                }
                val __cardDataLoaded = 
                    this.__cardDataValue !== null
                if (__cardDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DATA)))) {
                    return false
                }
                if (__cardDataLoaded && this.__cardDataValue != __other.cardData) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_HISTORIES)) != __other.__isVisible(PropId.byIndex(SLOT_HISTORIES))) {
                    return false
                }
                val __historiesLoaded = 
                    this.__historiesValue !== null
                if (__historiesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_HISTORIES)))) {
                    return false
                }
                if (__historiesLoaded && this.__historiesValue != __other.histories) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_DATA))) {
                    return false
                }
                val __indexDataLoaded = 
                    this.__indexDataValue !== null
                if (__indexDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_DATA)))) {
                    return false
                }
                if (__indexDataLoaded && this.__indexDataValue != __other.indexData) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = Document::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: Document?,
        ) : Implementor,
            DocumentDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: Document? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var docId: String
                get() = (__modified ?: __base!!).docId
                set(docId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docIdValue = docId
                }

            override var docNo: String?
                get() = (__modified ?: __base!!).docNo
                set(docNo) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docNoValue = docNo
                    __tmpModified.__docNoLoaded = true
                }

            override var docName: String?
                get() = (__modified ?: __base!!).docName
                set(docName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docNameValue = docName
                    __tmpModified.__docNameLoaded = true
                }

            override var docType: DocType
                get() = __ctx().toDraftObject((__modified ?: __base!!).docType)
                set(docType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docTypeValue = docType
                }

            @get:JsonIgnore
            public override var docTypeId: String
                get() = docType.id
                set(docTypeId) {
                    docType().id = docTypeId
                }

            override var currentState: DocState?
                get() = __ctx().toDraftObject((__modified ?: __base!!).currentState)
                set(currentState) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__currentStateValue = currentState
                    __tmpModified.__currentStateLoaded = true
                }

            @get:JsonIgnore
            public override var currentStateId: String?
                get() = currentState?.id
                set(currentStateId) {
                    if (currentStateId === null) {
                        this.currentState = null
                        return
                    }
                    currentState().id = currentStateId
                }

            override var preDocId: String?
                get() = (__modified ?: __base!!).preDocId
                set(preDocId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__preDocIdValue = preDocId
                    __tmpModified.__preDocIdLoaded = true
                }

            override var preDocument: Document?
                get() = __ctx().toDraftObject((__modified ?: __base!!).preDocument)
                set(preDocument) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__preDocumentValue = preDocument
                    __tmpModified.__preDocumentLoaded = true
                }

            @get:JsonIgnore
            public override var preDocumentId: String?
                get() = preDocument?.id
                set(preDocumentId) {
                    if (preDocumentId === null) {
                        this.preDocument = null
                        return
                    }
                    preDocument().id = preDocumentId
                }

            override var nextDocuments: List<Document>
                get() = __ctx().toDraftList((__modified ?: __base!!).nextDocuments, Document::class.java, true)
                set(nextDocuments) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__nextDocumentsValue = NonSharedList.of(__tmpModified.__nextDocumentsValue, nextDocuments)
                }

            override var partnerId: String?
                get() = (__modified ?: __base!!).partnerId
                set(partnerId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__partnerIdValue = partnerId
                    __tmpModified.__partnerIdLoaded = true
                }

            override var partnerName: String?
                get() = (__modified ?: __base!!).partnerName
                set(partnerName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__partnerNameValue = partnerName
                    __tmpModified.__partnerNameLoaded = true
                }

            override var docAmount: BigDecimal?
                get() = (__modified ?: __base!!).docAmount
                set(docAmount) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docAmountValue = docAmount
                    __tmpModified.__docAmountLoaded = true
                }

            override var currency: String?
                get() = (__modified ?: __base!!).currency
                set(currency) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__currencyValue = currency
                    __tmpModified.__currencyLoaded = true
                }

            override var docDate: LocalDateTime?
                get() = (__modified ?: __base!!).docDate
                set(docDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docDateValue = docDate
                    __tmpModified.__docDateLoaded = true
                }

            override var effectiveDate: LocalDateTime?
                get() = (__modified ?: __base!!).effectiveDate
                set(effectiveDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__effectiveDateValue = effectiveDate
                    __tmpModified.__effectiveDateLoaded = true
                }

            override var expireDate: LocalDateTime?
                get() = (__modified ?: __base!!).expireDate
                set(expireDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__expireDateValue = expireDate
                    __tmpModified.__expireDateLoaded = true
                }

            override var docData: String?
                get() = (__modified ?: __base!!).docData
                set(docData) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docDataValue = docData
                    __tmpModified.__docDataLoaded = true
                }

            override var extProps: String?
                get() = (__modified ?: __base!!).extProps
                set(extProps) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__extPropsValue = extProps
                    __tmpModified.__extPropsLoaded = true
                }

            override var cardData: List<DocumentCardData>
                get() = __ctx().toDraftList((__modified ?: __base!!).cardData, DocumentCardData::class.java, true)
                set(cardData) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardDataValue = NonSharedList.of(__tmpModified.__cardDataValue, cardData)
                }

            override var histories: List<DocumentHistory>
                get() = __ctx().toDraftList((__modified ?: __base!!).histories, DocumentHistory::class.java, true)
                set(histories) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__historiesValue = NonSharedList.of(__tmpModified.__historiesValue, histories)
                }

            override var indexData: List<DocumentIndexData>
                get() = __ctx().toDraftList((__modified ?: __base!!).indexData, DocumentIndexData::class.java, true)
                set(indexData) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexDataValue = NonSharedList.of(__tmpModified.__indexDataValue, indexData)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun docType(): DocTypeDraft {
                if (!__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                    docType = DocTypeDraft.`$`.produce()
                }
                return docType as DocTypeDraft
            }

            override fun docType(block: DocTypeDraft.() -> Unit) {
                docType().apply(block)
            }

            override fun currentState(): DocStateDraft {
                if (!__isLoaded(PropId.byIndex(SLOT_CURRENT_STATE)) || currentState === null) {
                    currentState = DocStateDraft.`$`.produce()
                }
                return currentState as DocStateDraft
            }

            override fun currentState(block: DocStateDraft.() -> Unit) {
                currentState().apply(block)
            }

            override fun preDocument(): DocumentDraft {
                if (!__isLoaded(PropId.byIndex(SLOT_PRE_DOCUMENT)) || preDocument === null) {
                    preDocument = `$`.produce()
                }
                return preDocument as DocumentDraft
            }

            override fun preDocument(block: DocumentDraft.() -> Unit) {
                preDocument().apply(block)
            }

            override fun nextDocuments(): MutableList<DocumentDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_NEXT_DOCUMENTS))) {
                    nextDocuments = emptyList()
                }
                return nextDocuments as MutableList<DocumentDraft>
            }

            override fun cardData(): MutableList<DocumentCardDataDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_CARD_DATA))) {
                    cardData = emptyList()
                }
                return cardData as MutableList<DocumentCardDataDraft>
            }

            override fun histories(): MutableList<DocumentHistoryDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_HISTORIES))) {
                    histories = emptyList()
                }
                return histories as MutableList<DocumentHistoryDraft>
            }

            override fun indexData(): MutableList<DocumentIndexDataDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_INDEX_DATA))) {
                    indexData = emptyList()
                }
                return indexData as MutableList<DocumentIndexDataDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_DOC_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docIdValue = null
                    SLOT_DOC_NO ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNoValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNoLoaded = false
                        }
                    SLOT_DOC_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNameLoaded = false
                        }
                    SLOT_DOC_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    SLOT_CURRENT_STATE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currentStateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currentStateLoaded = false
                        }
                    SLOT_PRE_DOC_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocIdLoaded = false
                        }
                    SLOT_PRE_DOCUMENT ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocumentValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocumentLoaded = false
                        }
                    SLOT_NEXT_DOCUMENTS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__nextDocumentsValue = null
                    SLOT_PARTNER_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerIdLoaded = false
                        }
                    SLOT_PARTNER_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerNameLoaded = false
                        }
                    SLOT_DOC_AMOUNT ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docAmountValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docAmountLoaded = false
                        }
                    SLOT_CURRENCY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currencyValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currencyLoaded = false
                        }
                    SLOT_DOC_DATE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDateLoaded = false
                        }
                    SLOT_EFFECTIVE_DATE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__effectiveDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__effectiveDateLoaded = false
                        }
                    SLOT_EXPIRE_DATE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__expireDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__expireDateLoaded = false
                        }
                    SLOT_DOC_DATA ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDataValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDataLoaded = false
                        }
                    SLOT_EXT_PROPS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__extPropsValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__extPropsLoaded = false
                        }
                    SLOT_CARD_DATA ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardDataValue = null
                    SLOT_HISTORIES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__historiesValue = null
                    SLOT_INDEX_DATA ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "docId" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docIdValue = null
                    "docNo" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNoValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNoLoaded = false
                        }
                    "docName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docNameLoaded = false
                        }
                    "docType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    "currentState" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currentStateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currentStateLoaded = false
                        }
                    "preDocId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocIdLoaded = false
                        }
                    "preDocument" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocumentValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__preDocumentLoaded = false
                        }
                    "nextDocuments" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__nextDocumentsValue = null
                    "partnerId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerIdLoaded = false
                        }
                    "partnerName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__partnerNameLoaded = false
                        }
                    "docAmount" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docAmountValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docAmountLoaded = false
                        }
                    "currency" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currencyValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__currencyLoaded = false
                        }
                    "docDate" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDateLoaded = false
                        }
                    "effectiveDate" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__effectiveDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__effectiveDateLoaded = false
                        }
                    "expireDate" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__expireDateValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__expireDateLoaded = false
                        }
                    "docData" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDataValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDataLoaded = false
                        }
                    "extProps" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__extPropsValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__extPropsLoaded = false
                        }
                    "cardData" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardDataValue = null
                    "histories" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__historiesValue = null
                    "indexData" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_DOC_ID ->
                    	this.docId = value as String?
                    	?: throw IllegalArgumentException("'docId cannot be null")
                    SLOT_DOC_NO ->
                    	this.docNo = value as String?
                    SLOT_DOC_NAME ->
                    	this.docName = value as String?
                    SLOT_DOC_TYPE ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    SLOT_CURRENT_STATE ->
                    	this.currentState = value as DocState?
                    SLOT_PRE_DOC_ID ->
                    	this.preDocId = value as String?
                    SLOT_PRE_DOCUMENT ->
                    	this.preDocument = value as Document?
                    SLOT_NEXT_DOCUMENTS ->
                    	this.nextDocuments = value as List<Document>?
                    	?: throw IllegalArgumentException("'nextDocuments cannot be null")
                    SLOT_PARTNER_ID ->
                    	this.partnerId = value as String?
                    SLOT_PARTNER_NAME ->
                    	this.partnerName = value as String?
                    SLOT_DOC_AMOUNT ->
                    	this.docAmount = value as BigDecimal?
                    SLOT_CURRENCY ->
                    	this.currency = value as String?
                    SLOT_DOC_DATE ->
                    	this.docDate = value as LocalDateTime?
                    SLOT_EFFECTIVE_DATE ->
                    	this.effectiveDate = value as LocalDateTime?
                    SLOT_EXPIRE_DATE ->
                    	this.expireDate = value as LocalDateTime?
                    SLOT_DOC_DATA ->
                    	this.docData = value as String?
                    SLOT_EXT_PROPS ->
                    	this.extProps = value as String?
                    SLOT_CARD_DATA ->
                    	this.cardData = value as List<DocumentCardData>?
                    	?: throw IllegalArgumentException("'cardData cannot be null")
                    SLOT_HISTORIES ->
                    	this.histories = value as List<DocumentHistory>?
                    	?: throw IllegalArgumentException("'histories cannot be null")
                    SLOT_INDEX_DATA ->
                    	this.indexData = value as List<DocumentIndexData>?
                    	?: throw IllegalArgumentException("'indexData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "docId" ->
                    	this.docId = value as String?
                    	?: throw IllegalArgumentException("'docId cannot be null")
                    "docNo" ->
                    	this.docNo = value as String?
                    "docName" ->
                    	this.docName = value as String?
                    "docType" ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    "currentState" ->
                    	this.currentState = value as DocState?
                    "preDocId" ->
                    	this.preDocId = value as String?
                    "preDocument" ->
                    	this.preDocument = value as Document?
                    "nextDocuments" ->
                    	this.nextDocuments = value as List<Document>?
                    	?: throw IllegalArgumentException("'nextDocuments cannot be null")
                    "partnerId" ->
                    	this.partnerId = value as String?
                    "partnerName" ->
                    	this.partnerName = value as String?
                    "docAmount" ->
                    	this.docAmount = value as BigDecimal?
                    "currency" ->
                    	this.currency = value as String?
                    "docDate" ->
                    	this.docDate = value as LocalDateTime?
                    "effectiveDate" ->
                    	this.effectiveDate = value as LocalDateTime?
                    "expireDate" ->
                    	this.expireDate = value as LocalDateTime?
                    "docData" ->
                    	this.docData = value as String?
                    "extProps" ->
                    	this.extProps = value as String?
                    "cardData" ->
                    	this.cardData = value as List<DocumentCardData>?
                    	?: throw IllegalArgumentException("'cardData cannot be null")
                    "histories" ->
                    	this.histories = value as List<DocumentHistory>?
                    	?: throw IllegalArgumentException("'histories cannot be null")
                    "indexData" ->
                    	this.indexData = value as List<DocumentIndexData>?
                    	?: throw IllegalArgumentException("'indexData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Document\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(34).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_DOC_ID ->
                    	__visibility.show(SLOT_DOC_ID, visible)
                    SLOT_DOC_NO ->
                    	__visibility.show(SLOT_DOC_NO, visible)
                    SLOT_DOC_NAME ->
                    	__visibility.show(SLOT_DOC_NAME, visible)
                    SLOT_DOC_TYPE ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    SLOT_CURRENT_STATE ->
                    	__visibility.show(SLOT_CURRENT_STATE, visible)
                    SLOT_PRE_DOC_ID ->
                    	__visibility.show(SLOT_PRE_DOC_ID, visible)
                    SLOT_PRE_DOCUMENT ->
                    	__visibility.show(SLOT_PRE_DOCUMENT, visible)
                    SLOT_NEXT_DOCUMENTS ->
                    	__visibility.show(SLOT_NEXT_DOCUMENTS, visible)
                    SLOT_PARTNER_ID ->
                    	__visibility.show(SLOT_PARTNER_ID, visible)
                    SLOT_PARTNER_NAME ->
                    	__visibility.show(SLOT_PARTNER_NAME, visible)
                    SLOT_DOC_AMOUNT ->
                    	__visibility.show(SLOT_DOC_AMOUNT, visible)
                    SLOT_CURRENCY ->
                    	__visibility.show(SLOT_CURRENCY, visible)
                    SLOT_DOC_DATE ->
                    	__visibility.show(SLOT_DOC_DATE, visible)
                    SLOT_EFFECTIVE_DATE ->
                    	__visibility.show(SLOT_EFFECTIVE_DATE, visible)
                    SLOT_EXPIRE_DATE ->
                    	__visibility.show(SLOT_EXPIRE_DATE, visible)
                    SLOT_DOC_DATA ->
                    	__visibility.show(SLOT_DOC_DATA, visible)
                    SLOT_EXT_PROPS ->
                    	__visibility.show(SLOT_EXT_PROPS, visible)
                    SLOT_CARD_DATA ->
                    	__visibility.show(SLOT_CARD_DATA, visible)
                    SLOT_HISTORIES ->
                    	__visibility.show(SLOT_HISTORIES, visible)
                    SLOT_INDEX_DATA ->
                    	__visibility.show(SLOT_INDEX_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(34).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "docId" ->
                    	__visibility.show(SLOT_DOC_ID, visible)
                    "docNo" ->
                    	__visibility.show(SLOT_DOC_NO, visible)
                    "docName" ->
                    	__visibility.show(SLOT_DOC_NAME, visible)
                    "docType" ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    "currentState" ->
                    	__visibility.show(SLOT_CURRENT_STATE, visible)
                    "preDocId" ->
                    	__visibility.show(SLOT_PRE_DOC_ID, visible)
                    "preDocument" ->
                    	__visibility.show(SLOT_PRE_DOCUMENT, visible)
                    "nextDocuments" ->
                    	__visibility.show(SLOT_NEXT_DOCUMENTS, visible)
                    "partnerId" ->
                    	__visibility.show(SLOT_PARTNER_ID, visible)
                    "partnerName" ->
                    	__visibility.show(SLOT_PARTNER_NAME, visible)
                    "docAmount" ->
                    	__visibility.show(SLOT_DOC_AMOUNT, visible)
                    "currency" ->
                    	__visibility.show(SLOT_CURRENCY, visible)
                    "docDate" ->
                    	__visibility.show(SLOT_DOC_DATE, visible)
                    "effectiveDate" ->
                    	__visibility.show(SLOT_EFFECTIVE_DATE, visible)
                    "expireDate" ->
                    	__visibility.show(SLOT_EXPIRE_DATE, visible)
                    "docData" ->
                    	__visibility.show(SLOT_DOC_DATA, visible)
                    "extProps" ->
                    	__visibility.show(SLOT_EXT_PROPS, visible)
                    "cardData" ->
                    	__visibility.show(SLOT_CARD_DATA, visible)
                    "histories" ->
                    	__visibility.show(SLOT_HISTORIES, visible)
                    "indexData" ->
                    	__visibility.show(SLOT_INDEX_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                            val oldValue = base!!.docType
                            val newValue = __ctx.resolveObject(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_CURRENT_STATE))) {
                            val oldValue = base!!.currentState
                            val newValue = __ctx.resolveObject(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_PRE_DOCUMENT))) {
                            val oldValue = base!!.preDocument
                            val newValue = __ctx.resolveObject(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_NEXT_DOCUMENTS))) {
                            val oldValue = base!!.nextDocuments
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_CARD_DATA))) {
                            val oldValue = base!!.cardData
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_HISTORIES))) {
                            val oldValue = base!!.histories
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_INDEX_DATA))) {
                            val oldValue = base!!.indexData
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__docTypeValue = __ctx.resolveObject(__tmpModified.__docTypeValue)
                        __tmpModified.__currentStateValue = __ctx.resolveObject(__tmpModified.__currentStateValue)
                        __tmpModified.__preDocumentValue = __ctx.resolveObject(__tmpModified.__preDocumentValue)
                        __tmpModified.__nextDocumentsValue = NonSharedList.of(__tmpModified.__nextDocumentsValue, __ctx.resolveList(__tmpModified.__nextDocumentsValue))
                        __tmpModified.__cardDataValue = NonSharedList.of(__tmpModified.__cardDataValue, __ctx.resolveList(__tmpModified.__cardDataValue))
                        __tmpModified.__historiesValue = NonSharedList.of(__tmpModified.__historiesValue, __ctx.resolveList(__tmpModified.__historiesValue))
                        __tmpModified.__indexDataValue = NonSharedList.of(__tmpModified.__indexDataValue, __ctx.resolveList(__tmpModified.__indexDataValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = Document::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: Document?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun docId(docId: String?): Builder {
            if (docId !== null) {
                __draft.docId = docId
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_ID), true)
            }
            return this
        }

        public fun docNo(docNo: String?): Builder {
            __draft.docNo = docNo
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_NO), true)
            return this
        }

        public fun docName(docName: String?): Builder {
            __draft.docName = docName
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_NAME), true)
            return this
        }

        public fun docType(docType: DocType?): Builder {
            if (docType !== null) {
                __draft.docType = docType
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_TYPE), true)
            }
            return this
        }

        public fun currentState(currentState: DocState?): Builder {
            __draft.currentState = currentState
            __draft.__show(PropId.byIndex(`$`.SLOT_CURRENT_STATE), true)
            return this
        }

        public fun preDocId(preDocId: String?): Builder {
            __draft.preDocId = preDocId
            __draft.__show(PropId.byIndex(`$`.SLOT_PRE_DOC_ID), true)
            return this
        }

        public fun preDocument(preDocument: Document?): Builder {
            __draft.preDocument = preDocument
            __draft.__show(PropId.byIndex(`$`.SLOT_PRE_DOCUMENT), true)
            return this
        }

        public fun nextDocuments(nextDocuments: List<Document>?): Builder {
            if (nextDocuments !== null) {
                __draft.nextDocuments = nextDocuments
                __draft.__show(PropId.byIndex(`$`.SLOT_NEXT_DOCUMENTS), true)
            }
            return this
        }

        public fun partnerId(partnerId: String?): Builder {
            __draft.partnerId = partnerId
            __draft.__show(PropId.byIndex(`$`.SLOT_PARTNER_ID), true)
            return this
        }

        public fun partnerName(partnerName: String?): Builder {
            __draft.partnerName = partnerName
            __draft.__show(PropId.byIndex(`$`.SLOT_PARTNER_NAME), true)
            return this
        }

        public fun docAmount(docAmount: BigDecimal?): Builder {
            __draft.docAmount = docAmount
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_AMOUNT), true)
            return this
        }

        public fun currency(currency: String?): Builder {
            __draft.currency = currency
            __draft.__show(PropId.byIndex(`$`.SLOT_CURRENCY), true)
            return this
        }

        public fun docDate(docDate: LocalDateTime?): Builder {
            __draft.docDate = docDate
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_DATE), true)
            return this
        }

        public fun effectiveDate(effectiveDate: LocalDateTime?): Builder {
            __draft.effectiveDate = effectiveDate
            __draft.__show(PropId.byIndex(`$`.SLOT_EFFECTIVE_DATE), true)
            return this
        }

        public fun expireDate(expireDate: LocalDateTime?): Builder {
            __draft.expireDate = expireDate
            __draft.__show(PropId.byIndex(`$`.SLOT_EXPIRE_DATE), true)
            return this
        }

        public fun docData(docData: String?): Builder {
            __draft.docData = docData
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_DATA), true)
            return this
        }

        public fun extProps(extProps: String?): Builder {
            __draft.extProps = extProps
            __draft.__show(PropId.byIndex(`$`.SLOT_EXT_PROPS), true)
            return this
        }

        public fun cardData(cardData: List<DocumentCardData>?): Builder {
            if (cardData !== null) {
                __draft.cardData = cardData
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_DATA), true)
            }
            return this
        }

        public fun histories(histories: List<DocumentHistory>?): Builder {
            if (histories !== null) {
                __draft.histories = histories
                __draft.__show(PropId.byIndex(`$`.SLOT_HISTORIES), true)
            }
            return this
        }

        public fun indexData(indexData: List<DocumentIndexData>?): Builder {
            if (indexData !== null) {
                __draft.indexData = indexData
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEX_DATA), true)
            }
            return this
        }

        public fun build(): Document = __draft.__unwrap() as Document
    }
}

@GeneratedBy(type = Document::class)
public fun ImmutableCreator<Document>.`by`(resolveImmediately: Boolean = false, block: DocumentDraft.() -> Unit): Document = DocumentDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = Document::class)
public fun ImmutableCreator<Document>.`by`(base: Document?, resolveImmediately: Boolean = false): Document = DocumentDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = Document::class)
public fun ImmutableCreator<Document>.`by`(
    base: Document?,
    resolveImmediately: Boolean = false,
    block: DocumentDraft.() -> Unit,
): Document = DocumentDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = Document::class)
public fun Document(resolveImmediately: Boolean = false, block: DocumentDraft.() -> Unit): Document = DocumentDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = Document::class)
public fun Document(
    base: Document?,
    resolveImmediately: Boolean = false,
    block: DocumentDraft.() -> Unit,
): Document = DocumentDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = Document::class)
public fun MutableList<DocumentDraft>.addBy(resolveImmediately: Boolean = false, block: DocumentDraft.() -> Unit): MutableList<DocumentDraft> {
    add(DocumentDraft.`$`.produce(null, resolveImmediately, block) as DocumentDraft)
    return this
}

@GeneratedBy(type = Document::class)
public fun MutableList<DocumentDraft>.addBy(base: Document?, resolveImmediately: Boolean = false): MutableList<DocumentDraft> {
    add(DocumentDraft.`$`.produce(base, resolveImmediately) as DocumentDraft)
    return this
}

@GeneratedBy(type = Document::class)
public fun MutableList<DocumentDraft>.addBy(
    base: Document?,
    resolveImmediately: Boolean = false,
    block: DocumentDraft.() -> Unit,
): MutableList<DocumentDraft> {
    add(DocumentDraft.`$`.produce(base, resolveImmediately, block) as DocumentDraft)
    return this
}

@GeneratedBy(type = Document::class)
public fun Document.copy(resolveImmediately: Boolean = false, block: DocumentDraft.() -> Unit): Document = DocumentDraft.`$`.produce(this, resolveImmediately, block)
