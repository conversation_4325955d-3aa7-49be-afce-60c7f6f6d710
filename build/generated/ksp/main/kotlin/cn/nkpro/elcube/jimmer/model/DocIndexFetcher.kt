@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocIndex::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType
import org.babyfish.jimmer.sql.fetcher.ReferenceFetchType
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.KReferenceFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = DocIndex::class)
public fun FetcherCreator<DocIndex>.`by`(block: DocIndexFetcherDsl.() -> Unit): Fetcher<DocIndex> {
    val dsl = DocIndexFetcherDsl(emptyDocIndexFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = DocIndex::class)
public fun FetcherCreator<DocIndex>.`by`(base: Fetcher<DocIndex>?, block: DocIndexFetcherDsl.() -> Unit): Fetcher<DocIndex> {
    val dsl = DocIndexFetcherDsl(base ?: emptyDocIndexFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = DocIndex::class)
public class DocIndexFetcherDsl(
    fetcher: Fetcher<DocIndex> = emptyDocIndexFetcher,
) {
    private var _fetcher: Fetcher<DocIndex> = fetcher

    public fun internallyGetFetcher(): Fetcher<DocIndex> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun indexCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexCode")
        } else {
            _fetcher.remove("indexCode")
        }
    }

    public fun indexName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexName")
        } else {
            _fetcher.remove("indexName")
        }
    }

    public fun indexType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexType")
        } else {
            _fetcher.remove("indexType")
        }
    }

    public fun docType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docType")
        } else {
            _fetcher.remove("docType")
        }
    }

    public fun docType(idOnlyFetchType: IdOnlyFetchType) {
        _fetcher = _fetcher.add("docType", idOnlyFetchType)
    }

    public fun docType(childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher
        )
    }

    public fun docType(childFetcher: Fetcher<DocType>, cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun docType(cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(enabled: Boolean, childFetcher: Fetcher<DocType>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher)
        }
    }

    public fun docType(
        enabled: Boolean,
        childFetcher: Fetcher<DocType>,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher, cfgBlock)
        }
    }

    public fun docType(enabled: Boolean, childBlock: DocTypeFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childBlock)
        }
    }

    public fun docType(
        enabled: Boolean,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
        childBlock: DocTypeFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(cfgBlock, childBlock)
        }
    }

    public fun docType(fetchType: ReferenceFetchType, childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun docType(fetchType: ReferenceFetchType, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun extractExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("extractExpr")
        } else {
            _fetcher.remove("extractExpr")
        }
    }

    public fun searchable(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("searchable")
        } else {
            _fetcher.remove("searchable")
        }
    }

    public fun sortable(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortable")
        } else {
            _fetcher.remove("sortable")
        }
    }

    public fun aggregatable(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("aggregatable")
        } else {
            _fetcher.remove("aggregatable")
        }
    }

    public fun indexData(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexData")
        } else {
            _fetcher.remove("indexData")
        }
    }

    public fun indexData(childFetcher: Fetcher<DocumentIndexData>) {
        _fetcher = _fetcher.add(
            "indexData",
            childFetcher
        )
    }

    public fun indexData(childFetcher: Fetcher<DocumentIndexData>, cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "indexData",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexData(childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexData",
            DocumentIndexDataFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun indexData(cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?, childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexData",
            DocumentIndexDataFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexData(enabled: Boolean, childFetcher: Fetcher<DocumentIndexData>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childFetcher)
        }
    }

    public fun indexData(
        enabled: Boolean,
        childFetcher: Fetcher<DocumentIndexData>,
        cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childFetcher, cfgBlock)
        }
    }

    public fun indexData(enabled: Boolean, childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childBlock)
        }
    }

    public fun indexData(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?,
        childBlock: DocumentIndexDataFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(cfgBlock, childBlock)
        }
    }
}

private val emptyDocIndexFetcher: Fetcher<DocIndex> = FetcherImpl(DocIndex::class.java)
