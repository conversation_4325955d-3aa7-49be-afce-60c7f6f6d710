@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocCard::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType
import org.babyfish.jimmer.sql.fetcher.ReferenceFetchType
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.KReferenceFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = DocCard::class)
public fun FetcherCreator<DocCard>.`by`(block: DocCardFetcherDsl.() -> Unit): Fetcher<DocCard> {
    val dsl = DocCardFetcherDsl(emptyDocCardFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = DocCard::class)
public fun FetcherCreator<DocCard>.`by`(base: Fetcher<DocCard>?, block: DocCardFetcherDsl.() -> Unit): Fetcher<DocCard> {
    val dsl = DocCardFetcherDsl(base ?: emptyDocCardFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = DocCard::class)
public class DocCardFetcherDsl(
    fetcher: Fetcher<DocCard> = emptyDocCardFetcher,
) {
    private var _fetcher: Fetcher<DocCard> = fetcher

    public fun internallyGetFetcher(): Fetcher<DocCard> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun cardCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardCode")
        } else {
            _fetcher.remove("cardCode")
        }
    }

    public fun cardName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardName")
        } else {
            _fetcher.remove("cardName")
        }
    }

    public fun cardDesc(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardDesc")
        } else {
            _fetcher.remove("cardDesc")
        }
    }

    public fun cardType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardType")
        } else {
            _fetcher.remove("cardType")
        }
    }

    public fun cardPosition(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardPosition")
        } else {
            _fetcher.remove("cardPosition")
        }
    }

    public fun docType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docType")
        } else {
            _fetcher.remove("docType")
        }
    }

    public fun docType(idOnlyFetchType: IdOnlyFetchType) {
        _fetcher = _fetcher.add("docType", idOnlyFetchType)
    }

    public fun docType(childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher
        )
    }

    public fun docType(childFetcher: Fetcher<DocType>, cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun docType(cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(enabled: Boolean, childFetcher: Fetcher<DocType>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher)
        }
    }

    public fun docType(
        enabled: Boolean,
        childFetcher: Fetcher<DocType>,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher, cfgBlock)
        }
    }

    public fun docType(enabled: Boolean, childBlock: DocTypeFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childBlock)
        }
    }

    public fun docType(
        enabled: Boolean,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
        childBlock: DocTypeFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(cfgBlock, childBlock)
        }
    }

    public fun docType(fetchType: ReferenceFetchType, childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun docType(fetchType: ReferenceFetchType, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun componentName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("componentName")
        } else {
            _fetcher.remove("componentName")
        }
    }

    public fun cardConfig(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardConfig")
        } else {
            _fetcher.remove("cardConfig")
        }
    }

    public fun enabled(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("enabled")
        } else {
            _fetcher.remove("enabled")
        }
    }

    public fun required(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("required")
        } else {
            _fetcher.remove("required")
        }
    }

    public fun readonly(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("readonly")
        } else {
            _fetcher.remove("readonly")
        }
    }

    public fun visible(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("visible")
        } else {
            _fetcher.remove("visible")
        }
    }

    public fun visibleExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("visibleExpr")
        } else {
            _fetcher.remove("visibleExpr")
        }
    }

    public fun readonlyExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("readonlyExpr")
        } else {
            _fetcher.remove("readonlyExpr")
        }
    }

    public fun requiredExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("requiredExpr")
        } else {
            _fetcher.remove("requiredExpr")
        }
    }

    public fun calcExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("calcExpr")
        } else {
            _fetcher.remove("calcExpr")
        }
    }

    public fun validateExpr(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("validateExpr")
        } else {
            _fetcher.remove("validateExpr")
        }
    }

    public fun fields(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("fields")
        } else {
            _fetcher.remove("fields")
        }
    }

    public fun fields(childFetcher: Fetcher<DocCardField>) {
        _fetcher = _fetcher.add(
            "fields",
            childFetcher
        )
    }

    public fun fields(childFetcher: Fetcher<DocCardField>, cfgBlock: (KListFieldDsl<DocCardField>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "fields",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun fields(childBlock: DocCardFieldFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "fields",
            DocCardFieldFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun fields(cfgBlock: (KListFieldDsl<DocCardField>.() -> Unit)?, childBlock: DocCardFieldFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "fields",
            DocCardFieldFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun fields(enabled: Boolean, childFetcher: Fetcher<DocCardField>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("fields")
        } else {
            fields(childFetcher)
        }
    }

    public fun fields(
        enabled: Boolean,
        childFetcher: Fetcher<DocCardField>,
        cfgBlock: (KListFieldDsl<DocCardField>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("fields")
        } else {
            fields(childFetcher, cfgBlock)
        }
    }

    public fun fields(enabled: Boolean, childBlock: DocCardFieldFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("fields")
        } else {
            fields(childBlock)
        }
    }

    public fun fields(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocCardField>.() -> Unit)?,
        childBlock: DocCardFieldFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("fields")
        } else {
            fields(cfgBlock, childBlock)
        }
    }

    public fun cardData(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardData")
        } else {
            _fetcher.remove("cardData")
        }
    }

    public fun cardData(childFetcher: Fetcher<DocumentCardData>) {
        _fetcher = _fetcher.add(
            "cardData",
            childFetcher
        )
    }

    public fun cardData(childFetcher: Fetcher<DocumentCardData>, cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "cardData",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cardData(childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cardData",
            DocumentCardDataFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun cardData(cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?, childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cardData",
            DocumentCardDataFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cardData(enabled: Boolean, childFetcher: Fetcher<DocumentCardData>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childFetcher)
        }
    }

    public fun cardData(
        enabled: Boolean,
        childFetcher: Fetcher<DocumentCardData>,
        cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childFetcher, cfgBlock)
        }
    }

    public fun cardData(enabled: Boolean, childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childBlock)
        }
    }

    public fun cardData(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?,
        childBlock: DocumentCardDataFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(cfgBlock, childBlock)
        }
    }
}

private val emptyDocCardFetcher: Fetcher<DocCard> = FetcherImpl(DocCard::class.java)
