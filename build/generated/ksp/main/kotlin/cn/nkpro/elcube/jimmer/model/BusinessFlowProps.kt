@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.BusinessFlow::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<BusinessFlow>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<BusinessFlow>.id: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BusinessFlow>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<LocalDateTime>(BusinessFlowProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<BusinessFlow>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<LocalDateTime>(BusinessFlowProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<BusinessFlow>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<LocalDateTime>(BusinessFlowProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<BusinessFlow>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<LocalDateTime>(BusinessFlowProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<BusinessFlow>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BusinessFlow>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Int>(BusinessFlowProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<BusinessFlow>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Int>(BusinessFlowProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<BusinessFlow>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<BusinessFlow>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<BusinessFlow>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Int>(BusinessFlowProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<BusinessFlow>.flowCode: KNonNullPropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_CODE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<BusinessFlow>.flowCode: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_CODE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BusinessFlow>.flowName: KNonNullPropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<BusinessFlow>.flowName: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<BusinessFlow>.flowDesc: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_DESC.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BusinessFlow>.flowType: KNonNullPropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<BusinessFlow>.flowType: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_TYPE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BusinessFlow>.enabled: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.ENABLED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<BusinessFlow>.enabled: KNullablePropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.ENABLED.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<BusinessFlow>.systemBuiltIn: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.SYSTEM_BUILT_IN.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<BusinessFlow>.systemBuiltIn: KNullablePropExpression<Boolean>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<Boolean>(BusinessFlowProps.SYSTEM_BUILT_IN.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<BusinessFlow>.flowConfig: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = get<String>(BusinessFlowProps.FLOW_CONFIG.unwrap()) as KNullablePropExpression<String>

public fun KProps<BusinessFlow>.nodes(block: KImplicitSubQueryTable<BusinessFlowNode>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(BusinessFlowProps.NODES.unwrap(), block)

public val KTableEx<BusinessFlow>.nodes: KNonNullTableEx<BusinessFlowNode>
    @GeneratedBy(type = BusinessFlow::class)
    get() = join(BusinessFlowProps.NODES.unwrap())

public val KTableEx<BusinessFlow>.`nodes?`: KNullableTableEx<BusinessFlowNode>
    @GeneratedBy(type = BusinessFlow::class)
    get() = outerJoin(BusinessFlowProps.NODES.unwrap())

public fun KProps<BusinessFlow>.connections(block: KImplicitSubQueryTable<BusinessFlowConnection>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(BusinessFlowProps.CONNECTIONS.unwrap(), block)

public val KTableEx<BusinessFlow>.connections: KNonNullTableEx<BusinessFlowConnection>
    @GeneratedBy(type = BusinessFlow::class)
    get() = join(BusinessFlowProps.CONNECTIONS.unwrap())

public val KTableEx<BusinessFlow>.`connections?`: KNullableTableEx<BusinessFlowConnection>
    @GeneratedBy(type = BusinessFlow::class)
    get() = outerJoin(BusinessFlowProps.CONNECTIONS.unwrap())

public fun KProps<BusinessFlow>.instances(block: KImplicitSubQueryTable<BusinessFlowInstance>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(BusinessFlowProps.INSTANCES.unwrap(), block)

public val KTableEx<BusinessFlow>.instances: KNonNullTableEx<BusinessFlowInstance>
    @GeneratedBy(type = BusinessFlow::class)
    get() = join(BusinessFlowProps.INSTANCES.unwrap())

public val KTableEx<BusinessFlow>.`instances?`: KNullableTableEx<BusinessFlowInstance>
    @GeneratedBy(type = BusinessFlow::class)
    get() = outerJoin(BusinessFlowProps.INSTANCES.unwrap())

public val KRemoteRef.NonNull<BusinessFlow>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<BusinessFlow>.id: KNullablePropExpression<String>
    @GeneratedBy(type = BusinessFlow::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = BusinessFlow::class)
public fun KNonNullTable<BusinessFlow>.fetchBy(block: BusinessFlowFetcherDsl.() -> Unit): Selection<BusinessFlow> = fetch(newFetcher(BusinessFlow::class).`by`(block))

@GeneratedBy(type = BusinessFlow::class)
public fun KNullableTable<BusinessFlow>.fetchBy(block: BusinessFlowFetcherDsl.() -> Unit): Selection<BusinessFlow?> = fetch(newFetcher(BusinessFlow::class).`by`(block))

@GeneratedBy(type = BusinessFlow::class)
public object BusinessFlowProps {
    public val ID: TypedProp.Scalar<BusinessFlow, String> =
            TypedProp.scalar(BusinessFlow::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<BusinessFlow, LocalDateTime> =
            TypedProp.scalar(BusinessFlow::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<BusinessFlow, LocalDateTime> =
            TypedProp.scalar(BusinessFlow::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<BusinessFlow, Int> =
            TypedProp.scalar(BusinessFlow::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<BusinessFlow, Boolean> =
            TypedProp.scalar(BusinessFlow::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<BusinessFlow, Int?> =
            TypedProp.scalar(BusinessFlow::sortOrder.toImmutableProp())

    public val FLOW_CODE: TypedProp.Scalar<BusinessFlow, String> =
            TypedProp.scalar(BusinessFlow::flowCode.toImmutableProp())

    public val FLOW_NAME: TypedProp.Scalar<BusinessFlow, String> =
            TypedProp.scalar(BusinessFlow::flowName.toImmutableProp())

    public val FLOW_DESC: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::flowDesc.toImmutableProp())

    public val FLOW_TYPE: TypedProp.Scalar<BusinessFlow, String> =
            TypedProp.scalar(BusinessFlow::flowType.toImmutableProp())

    public val ENABLED: TypedProp.Scalar<BusinessFlow, Boolean> =
            TypedProp.scalar(BusinessFlow::enabled.toImmutableProp())

    public val SYSTEM_BUILT_IN: TypedProp.Scalar<BusinessFlow, Boolean> =
            TypedProp.scalar(BusinessFlow::systemBuiltIn.toImmutableProp())

    public val FLOW_CONFIG: TypedProp.Scalar<BusinessFlow, String?> =
            TypedProp.scalar(BusinessFlow::flowConfig.toImmutableProp())

    public val NODES: TypedProp.ReferenceList<BusinessFlow, BusinessFlowNode> =
            TypedProp.referenceList(BusinessFlow::nodes.toImmutableProp())

    public val CONNECTIONS: TypedProp.ReferenceList<BusinessFlow, BusinessFlowConnection> =
            TypedProp.referenceList(BusinessFlow::connections.toImmutableProp())

    public val INSTANCES: TypedProp.ReferenceList<BusinessFlow, BusinessFlowInstance> =
            TypedProp.referenceList(BusinessFlow::instances.toImmutableProp())
}
