@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.Component::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<Component>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Component>.id: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = Component::class)
    get() = get<LocalDateTime>(ComponentProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<Component>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Component::class)
    get() = get<LocalDateTime>(ComponentProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<Component>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = Component::class)
    get() = get<LocalDateTime>(ComponentProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<Component>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Component::class)
    get() = get<LocalDateTime>(ComponentProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<Component>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = Component::class)
    get() = get<Int>(ComponentProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<Component>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = Component::class)
    get() = get<Int>(ComponentProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<Component>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<Component>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<Component>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = Component::class)
    get() = get<Int>(ComponentProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<Component>.componentCode: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_CODE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Component>.componentCode: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_CODE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.componentName: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Component>.componentName: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_NAME.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.componentType: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Component>.componentType: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_TYPE.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.componentCategory: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_CATEGORY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.componentVersion: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_VERSION.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Component>.componentVersion: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_VERSION.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.componentDesc: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_DESC.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.componentIcon: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.COMPONENT_ICON.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Component>.systemBuiltIn: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.SYSTEM_BUILT_IN.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<Component>.systemBuiltIn: KNullablePropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.SYSTEM_BUILT_IN.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<Component>.enabled: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.ENABLED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<Component>.enabled: KNullablePropExpression<Boolean>
    @GeneratedBy(type = Component::class)
    get() = get<Boolean>(ComponentProps.ENABLED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<Component>.groovyScript: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.GROOVY_SCRIPT.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.vueMain: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.VUE_MAIN.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.vueDefs: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.VUE_DEFS.unwrap()) as KNullablePropExpression<String>

public val KProps<Component>.configSchema: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = get<String>(ComponentProps.CONFIG_SCHEMA.unwrap()) as KNullablePropExpression<String>

public fun KProps<Component>.dependencies(block: KImplicitSubQueryTable<ComponentDependency>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(ComponentProps.DEPENDENCIES.unwrap(), block)

public val KTableEx<Component>.dependencies: KNonNullTableEx<ComponentDependency>
    @GeneratedBy(type = Component::class)
    get() = join(ComponentProps.DEPENDENCIES.unwrap())

public val KTableEx<Component>.`dependencies?`: KNullableTableEx<ComponentDependency>
    @GeneratedBy(type = Component::class)
    get() = outerJoin(ComponentProps.DEPENDENCIES.unwrap())

public fun KProps<Component>.versions(block: KImplicitSubQueryTable<ComponentVersion>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(ComponentProps.VERSIONS.unwrap(), block)

public val KTableEx<Component>.versions: KNonNullTableEx<ComponentVersion>
    @GeneratedBy(type = Component::class)
    get() = join(ComponentProps.VERSIONS.unwrap())

public val KTableEx<Component>.`versions?`: KNullableTableEx<ComponentVersion>
    @GeneratedBy(type = Component::class)
    get() = outerJoin(ComponentProps.VERSIONS.unwrap())

public val KRemoteRef.NonNull<Component>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<Component>.id: KNullablePropExpression<String>
    @GeneratedBy(type = Component::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = Component::class)
public fun KNonNullTable<Component>.fetchBy(block: ComponentFetcherDsl.() -> Unit): Selection<Component> = fetch(newFetcher(Component::class).`by`(block))

@GeneratedBy(type = Component::class)
public fun KNullableTable<Component>.fetchBy(block: ComponentFetcherDsl.() -> Unit): Selection<Component?> = fetch(newFetcher(Component::class).`by`(block))

@GeneratedBy(type = Component::class)
public object ComponentProps {
    public val ID: TypedProp.Scalar<Component, String> =
            TypedProp.scalar(Component::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<Component, LocalDateTime> =
            TypedProp.scalar(Component::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<Component, LocalDateTime> =
            TypedProp.scalar(Component::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<Component, Int> =
            TypedProp.scalar(Component::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<Component, Boolean> =
            TypedProp.scalar(Component::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<Component, Int?> =
            TypedProp.scalar(Component::sortOrder.toImmutableProp())

    public val COMPONENT_CODE: TypedProp.Scalar<Component, String> =
            TypedProp.scalar(Component::componentCode.toImmutableProp())

    public val COMPONENT_NAME: TypedProp.Scalar<Component, String> =
            TypedProp.scalar(Component::componentName.toImmutableProp())

    public val COMPONENT_TYPE: TypedProp.Scalar<Component, String> =
            TypedProp.scalar(Component::componentType.toImmutableProp())

    public val COMPONENT_CATEGORY: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::componentCategory.toImmutableProp())

    public val COMPONENT_VERSION: TypedProp.Scalar<Component, String> =
            TypedProp.scalar(Component::componentVersion.toImmutableProp())

    public val COMPONENT_DESC: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::componentDesc.toImmutableProp())

    public val COMPONENT_ICON: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::componentIcon.toImmutableProp())

    public val SYSTEM_BUILT_IN: TypedProp.Scalar<Component, Boolean> =
            TypedProp.scalar(Component::systemBuiltIn.toImmutableProp())

    public val ENABLED: TypedProp.Scalar<Component, Boolean> =
            TypedProp.scalar(Component::enabled.toImmutableProp())

    public val GROOVY_SCRIPT: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::groovyScript.toImmutableProp())

    public val VUE_MAIN: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::vueMain.toImmutableProp())

    public val VUE_DEFS: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::vueDefs.toImmutableProp())

    public val CONFIG_SCHEMA: TypedProp.Scalar<Component, String?> =
            TypedProp.scalar(Component::configSchema.toImmutableProp())

    public val DEPENDENCIES: TypedProp.ReferenceList<Component, ComponentDependency> =
            TypedProp.referenceList(Component::dependencies.toImmutableProp())

    public val VERSIONS: TypedProp.ReferenceList<Component, ComponentVersion> =
            TypedProp.referenceList(Component::versions.toImmutableProp())
}
