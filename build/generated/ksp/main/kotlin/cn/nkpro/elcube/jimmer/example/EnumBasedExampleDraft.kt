@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.enums.InvoiceType
import cn.nkpro.elcube.jimmer.enums.PaymentStatus
import cn.nkpro.elcube.jimmer.enums.Priority
import cn.nkpro.elcube.jimmer.enums.SalesOrderStatus
import cn.nkpro.elcube.jimmer.model.base.BusinessEntityDraft
import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.Visibility

@DslScope
@GeneratedBy(type = SalesOrderEnum::class)
public interface SalesOrderEnumDraft : SalesOrderEnum, BusinessEntityDraft {
    override var orderNo: String

    override var customerName: String

    override var totalAmount: BigDecimal

    override var orderDate: LocalDateTime

    override var deliveryDate: LocalDateTime

    override var status: SalesOrderStatus

    override var priority: Priority

    override var paymentStatus: PaymentStatus

    override var invoiceType: InvoiceType?

    override var remark: String?

    @GeneratedBy(type = SalesOrderEnum::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATE_TIME: Int = 1

        public const val SLOT_UPDATE_TIME: Int = 2

        public const val SLOT_CREATE_BY: Int = 3

        public const val SLOT_UPDATE_BY: Int = 4

        public const val SLOT_DELETED: Int = 5

        public const val SLOT_VERSION: Int = 6

        public const val SLOT_ORDER_NO: Int = 7

        public const val SLOT_CUSTOMER_NAME: Int = 8

        public const val SLOT_TOTAL_AMOUNT: Int = 9

        public const val SLOT_ORDER_DATE: Int = 10

        public const val SLOT_DELIVERY_DATE: Int = 11

        public const val SLOT_STATUS: Int = 12

        public const val SLOT_PRIORITY: Int = 13

        public const val SLOT_PAYMENT_STATUS: Int = 14

        public const val SLOT_INVOICE_TYPE: Int = 15

        public const val SLOT_REMARK: Int = 16

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                SalesOrderEnum::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as SalesOrderEnum?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createTime", SLOT_CREATE_TIME)
            .redefine("updateTime", SLOT_UPDATE_TIME)
            .redefine("createBy", SLOT_CREATE_BY)
            .redefine("updateBy", SLOT_UPDATE_BY)
            .redefine("deleted", SLOT_DELETED)
            .redefine("version", SLOT_VERSION)
            .key(SLOT_ORDER_NO, "orderNo", String::class.java, false)
            .add(SLOT_CUSTOMER_NAME, "customerName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_TOTAL_AMOUNT, "totalAmount", ImmutablePropCategory.SCALAR, BigDecimal::class.java, false)
            .add(SLOT_ORDER_DATE, "orderDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
            .add(SLOT_DELIVERY_DATE, "deliveryDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
            .add(SLOT_STATUS, "status", ImmutablePropCategory.SCALAR, SalesOrderStatus::class.java, false)
            .add(SLOT_PRIORITY, "priority", ImmutablePropCategory.SCALAR, Priority::class.java, false)
            .add(SLOT_PAYMENT_STATUS, "paymentStatus", ImmutablePropCategory.SCALAR, PaymentStatus::class.java, false)
            .add(SLOT_INVOICE_TYPE, "invoiceType", ImmutablePropCategory.SCALAR, InvoiceType::class.java, true)
            .add(SLOT_REMARK, "remark", ImmutablePropCategory.SCALAR, String::class.java, true)
            .build()

        public fun produce(base: SalesOrderEnum? = null, resolveImmediately: Boolean = false): SalesOrderEnum {
            val consumer = DraftConsumer<SalesOrderEnumDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as SalesOrderEnum
        }

        public fun produce(
            base: SalesOrderEnum? = null,
            resolveImmediately: Boolean = false,
            block: SalesOrderEnumDraft.() -> Unit,
        ): SalesOrderEnum {
            val consumer = DraftConsumer<SalesOrderEnumDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as SalesOrderEnum
        }

        @GeneratedBy(type = SalesOrderEnum::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createTime", "updateTime", "createBy", "updateBy", "deleted", "version", "orderNo", "customerName", "totalAmount", "orderDate", "deliveryDate", "status", "priority", "paymentStatus", "invoiceType", "remark")
        private abstract interface Implementor : SalesOrderEnum, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATE_TIME ->
                	createTime
                SLOT_UPDATE_TIME ->
                	updateTime
                SLOT_CREATE_BY ->
                	createBy
                SLOT_UPDATE_BY ->
                	updateBy
                SLOT_DELETED ->
                	deleted
                SLOT_VERSION ->
                	version
                SLOT_ORDER_NO ->
                	orderNo
                SLOT_CUSTOMER_NAME ->
                	customerName
                SLOT_TOTAL_AMOUNT ->
                	totalAmount
                SLOT_ORDER_DATE ->
                	orderDate
                SLOT_DELIVERY_DATE ->
                	deliveryDate
                SLOT_STATUS ->
                	status
                SLOT_PRIORITY ->
                	priority
                SLOT_PAYMENT_STATUS ->
                	paymentStatus
                SLOT_INVOICE_TYPE ->
                	invoiceType
                SLOT_REMARK ->
                	remark
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createTime" ->
                	createTime
                "updateTime" ->
                	updateTime
                "createBy" ->
                	createBy
                "updateBy" ->
                	updateBy
                "deleted" ->
                	deleted
                "version" ->
                	version
                "orderNo" ->
                	orderNo
                "customerName" ->
                	customerName
                "totalAmount" ->
                	totalAmount
                "orderDate" ->
                	orderDate
                "deliveryDate" ->
                	deliveryDate
                "status" ->
                	status
                "priority" ->
                	priority
                "paymentStatus" ->
                	paymentStatus
                "invoiceType" ->
                	invoiceType
                "remark" ->
                	remark
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = SalesOrderEnum::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updateTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updateTimeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __createByValue: String? = null

            @get:JsonIgnore
            internal var __createByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updateByValue: String? = null

            @get:JsonIgnore
            internal var __updateByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Long = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orderNoValue: String? = null

            @get:JsonIgnore
            internal var __customerNameValue: String? = null

            @get:JsonIgnore
            internal var __totalAmountValue: BigDecimal? = null

            @get:JsonIgnore
            internal var __orderDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __deliveryDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __statusValue: SalesOrderStatus? = null

            @get:JsonIgnore
            internal var __priorityValue: Priority? = null

            @get:JsonIgnore
            internal var __paymentStatusValue: PaymentStatus? = null

            @get:JsonIgnore
            internal var __invoiceTypeValue: InvoiceType? = null

            @get:JsonIgnore
            internal var __invoiceTypeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __remarkValue: String? = null

            @get:JsonIgnore
            internal var __remarkLoaded: Boolean = false

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "id")
                    }
                    return __idValue
                }

            override val createTime: LocalDateTime
                get() {
                    val __createTimeValue = this.__createTimeValue
                    if (__createTimeValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "createTime")
                    }
                    return __createTimeValue
                }

            override val updateTime: LocalDateTime?
                get() {
                    if (!__updateTimeLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "updateTime")
                    }
                    return __updateTimeValue
                }

            override val createBy: String?
                get() {
                    if (!__createByLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "createBy")
                    }
                    return __createByValue
                }

            override val updateBy: String?
                get() {
                    if (!__updateByLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "updateBy")
                    }
                    return __updateByValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val version: Long
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "version")
                    }
                    return __versionValue
                }

            override val orderNo: String
                get() {
                    val __orderNoValue = this.__orderNoValue
                    if (__orderNoValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "orderNo")
                    }
                    return __orderNoValue
                }

            override val customerName: String
                get() {
                    val __customerNameValue = this.__customerNameValue
                    if (__customerNameValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "customerName")
                    }
                    return __customerNameValue
                }

            override val totalAmount: BigDecimal
                get() {
                    val __totalAmountValue = this.__totalAmountValue
                    if (__totalAmountValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "totalAmount")
                    }
                    return __totalAmountValue
                }

            override val orderDate: LocalDateTime
                get() {
                    val __orderDateValue = this.__orderDateValue
                    if (__orderDateValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "orderDate")
                    }
                    return __orderDateValue
                }

            override val deliveryDate: LocalDateTime
                get() {
                    val __deliveryDateValue = this.__deliveryDateValue
                    if (__deliveryDateValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "deliveryDate")
                    }
                    return __deliveryDateValue
                }

            override val status: SalesOrderStatus
                get() {
                    val __statusValue = this.__statusValue
                    if (__statusValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "status")
                    }
                    return __statusValue
                }

            override val priority: Priority
                get() {
                    val __priorityValue = this.__priorityValue
                    if (__priorityValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "priority")
                    }
                    return __priorityValue
                }

            override val paymentStatus: PaymentStatus
                get() {
                    val __paymentStatusValue = this.__paymentStatusValue
                    if (__paymentStatusValue === null) {
                        throw UnloadedException(SalesOrderEnum::class.java, "paymentStatus")
                    }
                    return __paymentStatusValue
                }

            override val invoiceType: InvoiceType?
                get() {
                    if (!__invoiceTypeLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "invoiceType")
                    }
                    return __invoiceTypeValue
                }

            override val remark: String?
                get() {
                    if (!__remarkLoaded) {
                        throw UnloadedException(SalesOrderEnum::class.java, "remark")
                    }
                    return __remarkValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATE_TIME ->
                	__createTimeValue !== null
                SLOT_UPDATE_TIME ->
                	__updateTimeLoaded
                SLOT_CREATE_BY ->
                	__createByLoaded
                SLOT_UPDATE_BY ->
                	__updateByLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_ORDER_NO ->
                	__orderNoValue !== null
                SLOT_CUSTOMER_NAME ->
                	__customerNameValue !== null
                SLOT_TOTAL_AMOUNT ->
                	__totalAmountValue !== null
                SLOT_ORDER_DATE ->
                	__orderDateValue !== null
                SLOT_DELIVERY_DATE ->
                	__deliveryDateValue !== null
                SLOT_STATUS ->
                	__statusValue !== null
                SLOT_PRIORITY ->
                	__priorityValue !== null
                SLOT_PAYMENT_STATUS ->
                	__paymentStatusValue !== null
                SLOT_INVOICE_TYPE ->
                	__invoiceTypeLoaded
                SLOT_REMARK ->
                	__remarkLoaded
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createTime" ->
                	__createTimeValue !== null
                "updateTime" ->
                	__updateTimeLoaded
                "createBy" ->
                	__createByLoaded
                "updateBy" ->
                	__updateByLoaded
                "deleted" ->
                	__deletedLoaded
                "version" ->
                	__versionLoaded
                "orderNo" ->
                	__orderNoValue !== null
                "customerName" ->
                	__customerNameValue !== null
                "totalAmount" ->
                	__totalAmountValue !== null
                "orderDate" ->
                	__orderDateValue !== null
                "deliveryDate" ->
                	__deliveryDateValue !== null
                "status" ->
                	__statusValue !== null
                "priority" ->
                	__priorityValue !== null
                "paymentStatus" ->
                	__paymentStatusValue !== null
                "invoiceType" ->
                	__invoiceTypeLoaded
                "remark" ->
                	__remarkLoaded
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATE_TIME ->
                    	__visibility.visible(SLOT_CREATE_TIME)
                    SLOT_UPDATE_TIME ->
                    	__visibility.visible(SLOT_UPDATE_TIME)
                    SLOT_CREATE_BY ->
                    	__visibility.visible(SLOT_CREATE_BY)
                    SLOT_UPDATE_BY ->
                    	__visibility.visible(SLOT_UPDATE_BY)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_ORDER_NO ->
                    	__visibility.visible(SLOT_ORDER_NO)
                    SLOT_CUSTOMER_NAME ->
                    	__visibility.visible(SLOT_CUSTOMER_NAME)
                    SLOT_TOTAL_AMOUNT ->
                    	__visibility.visible(SLOT_TOTAL_AMOUNT)
                    SLOT_ORDER_DATE ->
                    	__visibility.visible(SLOT_ORDER_DATE)
                    SLOT_DELIVERY_DATE ->
                    	__visibility.visible(SLOT_DELIVERY_DATE)
                    SLOT_STATUS ->
                    	__visibility.visible(SLOT_STATUS)
                    SLOT_PRIORITY ->
                    	__visibility.visible(SLOT_PRIORITY)
                    SLOT_PAYMENT_STATUS ->
                    	__visibility.visible(SLOT_PAYMENT_STATUS)
                    SLOT_INVOICE_TYPE ->
                    	__visibility.visible(SLOT_INVOICE_TYPE)
                    SLOT_REMARK ->
                    	__visibility.visible(SLOT_REMARK)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createTime" ->
                    	__visibility.visible(SLOT_CREATE_TIME)
                    "updateTime" ->
                    	__visibility.visible(SLOT_UPDATE_TIME)
                    "createBy" ->
                    	__visibility.visible(SLOT_CREATE_BY)
                    "updateBy" ->
                    	__visibility.visible(SLOT_UPDATE_BY)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "orderNo" ->
                    	__visibility.visible(SLOT_ORDER_NO)
                    "customerName" ->
                    	__visibility.visible(SLOT_CUSTOMER_NAME)
                    "totalAmount" ->
                    	__visibility.visible(SLOT_TOTAL_AMOUNT)
                    "orderDate" ->
                    	__visibility.visible(SLOT_ORDER_DATE)
                    "deliveryDate" ->
                    	__visibility.visible(SLOT_DELIVERY_DATE)
                    "status" ->
                    	__visibility.visible(SLOT_STATUS)
                    "priority" ->
                    	__visibility.visible(SLOT_PRIORITY)
                    "paymentStatus" ->
                    	__visibility.visible(SLOT_PAYMENT_STATUS)
                    "invoiceType" ->
                    	__visibility.visible(SLOT_INVOICE_TYPE)
                    "remark" ->
                    	__visibility.visible(SLOT_REMARK)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createTimeValue !== null) {
                    hash = 31 * hash + __createTimeValue.hashCode()
                }
                if (__updateTimeLoaded) {
                    hash = 31 * hash + (__updateTimeValue?.hashCode() ?: 0)
                }
                if (__createByLoaded) {
                    hash = 31 * hash + (__createByValue?.hashCode() ?: 0)
                }
                if (__updateByLoaded) {
                    hash = 31 * hash + (__updateByValue?.hashCode() ?: 0)
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__orderNoValue !== null) {
                    hash = 31 * hash + __orderNoValue.hashCode()
                }
                if (__customerNameValue !== null) {
                    hash = 31 * hash + __customerNameValue.hashCode()
                }
                if (__totalAmountValue !== null) {
                    hash = 31 * hash + __totalAmountValue.hashCode()
                }
                if (__orderDateValue !== null) {
                    hash = 31 * hash + __orderDateValue.hashCode()
                }
                if (__deliveryDateValue !== null) {
                    hash = 31 * hash + __deliveryDateValue.hashCode()
                }
                if (__statusValue !== null) {
                    hash = 31 * hash + __statusValue.hashCode()
                }
                if (__priorityValue !== null) {
                    hash = 31 * hash + __priorityValue.hashCode()
                }
                if (__paymentStatusValue !== null) {
                    hash = 31 * hash + __paymentStatusValue.hashCode()
                }
                if (__invoiceTypeLoaded) {
                    hash = 31 * hash + (__invoiceTypeValue?.hashCode() ?: 0)
                }
                if (__remarkLoaded) {
                    hash = 31 * hash + (__remarkValue?.hashCode() ?: 0)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createTimeValue !== null) {
                    hash = 31 * hash + __createTimeValue.hashCode()
                }
                if (__updateTimeLoaded) {
                    hash = 31 * hash + (__updateTimeValue?.hashCode() ?: 0)
                }
                if (__createByLoaded) {
                    hash = 31 * hash + (__createByValue?.hashCode() ?: 0)
                }
                if (__updateByLoaded) {
                    hash = 31 * hash + (__updateByValue?.hashCode() ?: 0)
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__orderNoValue !== null) {
                    hash = 31 * hash + __orderNoValue.hashCode()
                }
                if (__customerNameValue !== null) {
                    hash = 31 * hash + __customerNameValue.hashCode()
                }
                if (__totalAmountValue !== null) {
                    hash = 31 * hash + __totalAmountValue.hashCode()
                }
                if (__orderDateValue !== null) {
                    hash = 31 * hash + __orderDateValue.hashCode()
                }
                if (__deliveryDateValue !== null) {
                    hash = 31 * hash + __deliveryDateValue.hashCode()
                }
                if (__statusValue !== null) {
                    hash = 31 * hash + __statusValue.hashCode()
                }
                if (__priorityValue !== null) {
                    hash = 31 * hash + __priorityValue.hashCode()
                }
                if (__paymentStatusValue !== null) {
                    hash = 31 * hash + __paymentStatusValue.hashCode()
                }
                if (__invoiceTypeLoaded) {
                    hash = 31 * hash + (__invoiceTypeValue?.hashCode() ?: 0)
                }
                if (__remarkLoaded) {
                    hash = 31 * hash + (__remarkValue?.hashCode() ?: 0)
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false
                }
                val __createTimeLoaded = 
                    this.__createTimeValue !== null
                if (__createTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME)))) {
                    return false
                }
                if (__createTimeLoaded && this.__createTimeValue != __other.createTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATE_TIME))) {
                    return false
                }
                val __updateTimeLoaded = 
                    this.__updateTimeLoaded
                if (__updateTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATE_TIME)))) {
                    return false
                }
                if (__updateTimeLoaded && this.__updateTimeValue != __other.updateTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_BY))) {
                    return false
                }
                val __createByLoaded = 
                    this.__createByLoaded
                if (__createByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATE_BY)))) {
                    return false
                }
                if (__createByLoaded && this.__createByValue != __other.createBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATE_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATE_BY))) {
                    return false
                }
                val __updateByLoaded = 
                    this.__updateByLoaded
                if (__updateByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATE_BY)))) {
                    return false
                }
                if (__updateByLoaded && this.__updateByValue != __other.updateBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_NO)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_NO))) {
                    return false
                }
                val __orderNoLoaded = 
                    this.__orderNoValue !== null
                if (__orderNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_NO)))) {
                    return false
                }
                if (__orderNoLoaded && this.__orderNoValue != __other.orderNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME))) {
                    return false
                }
                val __customerNameLoaded = 
                    this.__customerNameValue !== null
                if (__customerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CUSTOMER_NAME)))) {
                    return false
                }
                if (__customerNameLoaded && this.__customerNameValue != __other.customerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT))) {
                    return false
                }
                val __totalAmountLoaded = 
                    this.__totalAmountValue !== null
                if (__totalAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TOTAL_AMOUNT)))) {
                    return false
                }
                if (__totalAmountLoaded && this.__totalAmountValue != __other.totalAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_DATE))) {
                    return false
                }
                val __orderDateLoaded = 
                    this.__orderDateValue !== null
                if (__orderDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_DATE)))) {
                    return false
                }
                if (__orderDateLoaded && this.__orderDateValue != __other.orderDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE))) {
                    return false
                }
                val __deliveryDateLoaded = 
                    this.__deliveryDateValue !== null
                if (__deliveryDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELIVERY_DATE)))) {
                    return false
                }
                if (__deliveryDateLoaded && this.__deliveryDateValue != __other.deliveryDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false
                }
                val __statusLoaded = 
                    this.__statusValue !== null
                if (__statusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATUS)))) {
                    return false
                }
                if (__statusLoaded && this.__statusValue != __other.status) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRIORITY)) != __other.__isVisible(PropId.byIndex(SLOT_PRIORITY))) {
                    return false
                }
                val __priorityLoaded = 
                    this.__priorityValue !== null
                if (__priorityLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRIORITY)))) {
                    return false
                }
                if (__priorityLoaded && this.__priorityValue != __other.priority) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS))) {
                    return false
                }
                val __paymentStatusLoaded = 
                    this.__paymentStatusValue !== null
                if (__paymentStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENT_STATUS)))) {
                    return false
                }
                if (__paymentStatusLoaded && this.__paymentStatusValue != __other.paymentStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE))) {
                    return false
                }
                val __invoiceTypeLoaded = 
                    this.__invoiceTypeLoaded
                if (__invoiceTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICE_TYPE)))) {
                    return false
                }
                if (__invoiceTypeLoaded && this.__invoiceTypeValue != __other.invoiceType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REMARK)) != __other.__isVisible(PropId.byIndex(SLOT_REMARK))) {
                    return false
                }
                val __remarkLoaded = 
                    this.__remarkLoaded
                if (__remarkLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REMARK)))) {
                    return false
                }
                if (__remarkLoaded && this.__remarkValue != __other.remark) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_TIME))) {
                    return false
                }
                val __createTimeLoaded = 
                    this.__createTimeValue !== null
                if (__createTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATE_TIME)))) {
                    return false
                }
                if (__createTimeLoaded && this.__createTimeValue != __other.createTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATE_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATE_TIME))) {
                    return false
                }
                val __updateTimeLoaded = 
                    this.__updateTimeLoaded
                if (__updateTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATE_TIME)))) {
                    return false
                }
                if (__updateTimeLoaded && this.__updateTimeValue != __other.updateTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATE_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATE_BY))) {
                    return false
                }
                val __createByLoaded = 
                    this.__createByLoaded
                if (__createByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATE_BY)))) {
                    return false
                }
                if (__createByLoaded && this.__createByValue != __other.createBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATE_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATE_BY))) {
                    return false
                }
                val __updateByLoaded = 
                    this.__updateByLoaded
                if (__updateByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATE_BY)))) {
                    return false
                }
                if (__updateByLoaded && this.__updateByValue != __other.updateBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_NO)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_NO))) {
                    return false
                }
                val __orderNoLoaded = 
                    this.__orderNoValue !== null
                if (__orderNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_NO)))) {
                    return false
                }
                if (__orderNoLoaded && this.__orderNoValue != __other.orderNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME))) {
                    return false
                }
                val __customerNameLoaded = 
                    this.__customerNameValue !== null
                if (__customerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CUSTOMER_NAME)))) {
                    return false
                }
                if (__customerNameLoaded && this.__customerNameValue != __other.customerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT))) {
                    return false
                }
                val __totalAmountLoaded = 
                    this.__totalAmountValue !== null
                if (__totalAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TOTAL_AMOUNT)))) {
                    return false
                }
                if (__totalAmountLoaded && this.__totalAmountValue != __other.totalAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_DATE))) {
                    return false
                }
                val __orderDateLoaded = 
                    this.__orderDateValue !== null
                if (__orderDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_DATE)))) {
                    return false
                }
                if (__orderDateLoaded && this.__orderDateValue != __other.orderDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE))) {
                    return false
                }
                val __deliveryDateLoaded = 
                    this.__deliveryDateValue !== null
                if (__deliveryDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELIVERY_DATE)))) {
                    return false
                }
                if (__deliveryDateLoaded && this.__deliveryDateValue != __other.deliveryDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false
                }
                val __statusLoaded = 
                    this.__statusValue !== null
                if (__statusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATUS)))) {
                    return false
                }
                if (__statusLoaded && this.__statusValue != __other.status) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRIORITY)) != __other.__isVisible(PropId.byIndex(SLOT_PRIORITY))) {
                    return false
                }
                val __priorityLoaded = 
                    this.__priorityValue !== null
                if (__priorityLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRIORITY)))) {
                    return false
                }
                if (__priorityLoaded && this.__priorityValue != __other.priority) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS))) {
                    return false
                }
                val __paymentStatusLoaded = 
                    this.__paymentStatusValue !== null
                if (__paymentStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENT_STATUS)))) {
                    return false
                }
                if (__paymentStatusLoaded && this.__paymentStatusValue != __other.paymentStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE))) {
                    return false
                }
                val __invoiceTypeLoaded = 
                    this.__invoiceTypeLoaded
                if (__invoiceTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICE_TYPE)))) {
                    return false
                }
                if (__invoiceTypeLoaded && this.__invoiceTypeValue != __other.invoiceType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REMARK)) != __other.__isVisible(PropId.byIndex(SLOT_REMARK))) {
                    return false
                }
                val __remarkLoaded = 
                    this.__remarkLoaded
                if (__remarkLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REMARK)))) {
                    return false
                }
                if (__remarkLoaded && this.__remarkValue != __other.remark) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = SalesOrderEnum::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: SalesOrderEnum?,
        ) : Implementor,
            SalesOrderEnumDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: SalesOrderEnum? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createTime: LocalDateTime
                get() = (__modified ?: __base!!).createTime
                set(createTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createTimeValue = createTime
                }

            override var updateTime: LocalDateTime?
                get() = (__modified ?: __base!!).updateTime
                set(updateTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updateTimeValue = updateTime
                    __tmpModified.__updateTimeLoaded = true
                }

            override var createBy: String?
                get() = (__modified ?: __base!!).createBy
                set(createBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createByValue = createBy
                    __tmpModified.__createByLoaded = true
                }

            override var updateBy: String?
                get() = (__modified ?: __base!!).updateBy
                set(updateBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updateByValue = updateBy
                    __tmpModified.__updateByLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var version: Long
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var orderNo: String
                get() = (__modified ?: __base!!).orderNo
                set(orderNo) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orderNoValue = orderNo
                }

            override var customerName: String
                get() = (__modified ?: __base!!).customerName
                set(customerName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__customerNameValue = customerName
                }

            override var totalAmount: BigDecimal
                get() = (__modified ?: __base!!).totalAmount
                set(totalAmount) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__totalAmountValue = totalAmount
                }

            override var orderDate: LocalDateTime
                get() = (__modified ?: __base!!).orderDate
                set(orderDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orderDateValue = orderDate
                }

            override var deliveryDate: LocalDateTime
                get() = (__modified ?: __base!!).deliveryDate
                set(deliveryDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deliveryDateValue = deliveryDate
                }

            override var status: SalesOrderStatus
                get() = (__modified ?: __base!!).status
                set(status) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__statusValue = status
                }

            override var priority: Priority
                get() = (__modified ?: __base!!).priority
                set(priority) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__priorityValue = priority
                }

            override var paymentStatus: PaymentStatus
                get() = (__modified ?: __base!!).paymentStatus
                set(paymentStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__paymentStatusValue = paymentStatus
                }

            override var invoiceType: InvoiceType?
                get() = (__modified ?: __base!!).invoiceType
                set(invoiceType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__invoiceTypeValue = invoiceType
                    __tmpModified.__invoiceTypeLoaded = true
                }

            override var remark: String?
                get() = (__modified ?: __base!!).remark
                set(remark) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__remarkValue = remark
                    __tmpModified.__remarkLoaded = true
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATE_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createTimeValue = null
                    SLOT_UPDATE_TIME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateTimeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateTimeLoaded = false
                        }
                    SLOT_CREATE_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createByLoaded = false
                        }
                    SLOT_UPDATE_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateByLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_ORDER_NO ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderNoValue = null
                    SLOT_CUSTOMER_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__customerNameValue = null
                    SLOT_TOTAL_AMOUNT ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__totalAmountValue = null
                    SLOT_ORDER_DATE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderDateValue = null
                    SLOT_DELIVERY_DATE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__deliveryDateValue = null
                    SLOT_STATUS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statusValue = null
                    SLOT_PRIORITY ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__priorityValue = null
                    SLOT_PAYMENT_STATUS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentStatusValue = null
                    SLOT_INVOICE_TYPE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeLoaded = false
                        }
                    SLOT_REMARK ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkLoaded = false
                        }
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createTimeValue = null
                    "updateTime" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateTimeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateTimeLoaded = false
                        }
                    "createBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createByLoaded = false
                        }
                    "updateBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updateByLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "orderNo" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderNoValue = null
                    "customerName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__customerNameValue = null
                    "totalAmount" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__totalAmountValue = null
                    "orderDate" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderDateValue = null
                    "deliveryDate" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__deliveryDateValue = null
                    "status" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statusValue = null
                    "priority" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__priorityValue = null
                    "paymentStatus" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentStatusValue = null
                    "invoiceType" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeLoaded = false
                        }
                    "remark" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkLoaded = false
                        }
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATE_TIME ->
                    	this.createTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createTime cannot be null")
                    SLOT_UPDATE_TIME ->
                    	this.updateTime = value as LocalDateTime?
                    SLOT_CREATE_BY ->
                    	this.createBy = value as String?
                    SLOT_UPDATE_BY ->
                    	this.updateBy = value as String?
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_VERSION ->
                    	this.version = value as Long?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_ORDER_NO ->
                    	this.orderNo = value as String?
                    	?: throw IllegalArgumentException("'orderNo cannot be null")
                    SLOT_CUSTOMER_NAME ->
                    	this.customerName = value as String?
                    	?: throw IllegalArgumentException("'customerName cannot be null")
                    SLOT_TOTAL_AMOUNT ->
                    	this.totalAmount = value as BigDecimal?
                    	?: throw IllegalArgumentException("'totalAmount cannot be null")
                    SLOT_ORDER_DATE ->
                    	this.orderDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'orderDate cannot be null")
                    SLOT_DELIVERY_DATE ->
                    	this.deliveryDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'deliveryDate cannot be null")
                    SLOT_STATUS ->
                    	this.status = value as SalesOrderStatus?
                    	?: throw IllegalArgumentException("'status cannot be null")
                    SLOT_PRIORITY ->
                    	this.priority = value as Priority?
                    	?: throw IllegalArgumentException("'priority cannot be null")
                    SLOT_PAYMENT_STATUS ->
                    	this.paymentStatus = value as PaymentStatus?
                    	?: throw IllegalArgumentException("'paymentStatus cannot be null")
                    SLOT_INVOICE_TYPE ->
                    	this.invoiceType = value as InvoiceType?
                    SLOT_REMARK ->
                    	this.remark = value as String?
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createTime" ->
                    	this.createTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createTime cannot be null")
                    "updateTime" ->
                    	this.updateTime = value as LocalDateTime?
                    "createBy" ->
                    	this.createBy = value as String?
                    "updateBy" ->
                    	this.updateBy = value as String?
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "version" ->
                    	this.version = value as Long?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "orderNo" ->
                    	this.orderNo = value as String?
                    	?: throw IllegalArgumentException("'orderNo cannot be null")
                    "customerName" ->
                    	this.customerName = value as String?
                    	?: throw IllegalArgumentException("'customerName cannot be null")
                    "totalAmount" ->
                    	this.totalAmount = value as BigDecimal?
                    	?: throw IllegalArgumentException("'totalAmount cannot be null")
                    "orderDate" ->
                    	this.orderDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'orderDate cannot be null")
                    "deliveryDate" ->
                    	this.deliveryDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'deliveryDate cannot be null")
                    "status" ->
                    	this.status = value as SalesOrderStatus?
                    	?: throw IllegalArgumentException("'status cannot be null")
                    "priority" ->
                    	this.priority = value as Priority?
                    	?: throw IllegalArgumentException("'priority cannot be null")
                    "paymentStatus" ->
                    	this.paymentStatus = value as PaymentStatus?
                    	?: throw IllegalArgumentException("'paymentStatus cannot be null")
                    "invoiceType" ->
                    	this.invoiceType = value as InvoiceType?
                    "remark" ->
                    	this.remark = value as String?
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderEnum\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(17).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATE_TIME ->
                    	__visibility.show(SLOT_CREATE_TIME, visible)
                    SLOT_UPDATE_TIME ->
                    	__visibility.show(SLOT_UPDATE_TIME, visible)
                    SLOT_CREATE_BY ->
                    	__visibility.show(SLOT_CREATE_BY, visible)
                    SLOT_UPDATE_BY ->
                    	__visibility.show(SLOT_UPDATE_BY, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_ORDER_NO ->
                    	__visibility.show(SLOT_ORDER_NO, visible)
                    SLOT_CUSTOMER_NAME ->
                    	__visibility.show(SLOT_CUSTOMER_NAME, visible)
                    SLOT_TOTAL_AMOUNT ->
                    	__visibility.show(SLOT_TOTAL_AMOUNT, visible)
                    SLOT_ORDER_DATE ->
                    	__visibility.show(SLOT_ORDER_DATE, visible)
                    SLOT_DELIVERY_DATE ->
                    	__visibility.show(SLOT_DELIVERY_DATE, visible)
                    SLOT_STATUS ->
                    	__visibility.show(SLOT_STATUS, visible)
                    SLOT_PRIORITY ->
                    	__visibility.show(SLOT_PRIORITY, visible)
                    SLOT_PAYMENT_STATUS ->
                    	__visibility.show(SLOT_PAYMENT_STATUS, visible)
                    SLOT_INVOICE_TYPE ->
                    	__visibility.show(SLOT_INVOICE_TYPE, visible)
                    SLOT_REMARK ->
                    	__visibility.show(SLOT_REMARK, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(17).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createTime" ->
                    	__visibility.show(SLOT_CREATE_TIME, visible)
                    "updateTime" ->
                    	__visibility.show(SLOT_UPDATE_TIME, visible)
                    "createBy" ->
                    	__visibility.show(SLOT_CREATE_BY, visible)
                    "updateBy" ->
                    	__visibility.show(SLOT_UPDATE_BY, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "orderNo" ->
                    	__visibility.show(SLOT_ORDER_NO, visible)
                    "customerName" ->
                    	__visibility.show(SLOT_CUSTOMER_NAME, visible)
                    "totalAmount" ->
                    	__visibility.show(SLOT_TOTAL_AMOUNT, visible)
                    "orderDate" ->
                    	__visibility.show(SLOT_ORDER_DATE, visible)
                    "deliveryDate" ->
                    	__visibility.show(SLOT_DELIVERY_DATE, visible)
                    "status" ->
                    	__visibility.show(SLOT_STATUS, visible)
                    "priority" ->
                    	__visibility.show(SLOT_PRIORITY, visible)
                    "paymentStatus" ->
                    	__visibility.show(SLOT_PAYMENT_STATUS, visible)
                    "invoiceType" ->
                    	__visibility.show(SLOT_INVOICE_TYPE, visible)
                    "remark" ->
                    	__visibility.show(SLOT_REMARK, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = SalesOrderEnum::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: SalesOrderEnum?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createTime(createTime: LocalDateTime?): Builder {
            if (createTime !== null) {
                __draft.createTime = createTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATE_TIME), true)
            }
            return this
        }

        public fun updateTime(updateTime: LocalDateTime?): Builder {
            __draft.updateTime = updateTime
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATE_TIME), true)
            return this
        }

        public fun createBy(createBy: String?): Builder {
            __draft.createBy = createBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATE_BY), true)
            return this
        }

        public fun updateBy(updateBy: String?): Builder {
            __draft.updateBy = updateBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATE_BY), true)
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun version(version: Long?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun orderNo(orderNo: String?): Builder {
            if (orderNo !== null) {
                __draft.orderNo = orderNo
                __draft.__show(PropId.byIndex(`$`.SLOT_ORDER_NO), true)
            }
            return this
        }

        public fun customerName(customerName: String?): Builder {
            if (customerName !== null) {
                __draft.customerName = customerName
                __draft.__show(PropId.byIndex(`$`.SLOT_CUSTOMER_NAME), true)
            }
            return this
        }

        public fun totalAmount(totalAmount: BigDecimal?): Builder {
            if (totalAmount !== null) {
                __draft.totalAmount = totalAmount
                __draft.__show(PropId.byIndex(`$`.SLOT_TOTAL_AMOUNT), true)
            }
            return this
        }

        public fun orderDate(orderDate: LocalDateTime?): Builder {
            if (orderDate !== null) {
                __draft.orderDate = orderDate
                __draft.__show(PropId.byIndex(`$`.SLOT_ORDER_DATE), true)
            }
            return this
        }

        public fun deliveryDate(deliveryDate: LocalDateTime?): Builder {
            if (deliveryDate !== null) {
                __draft.deliveryDate = deliveryDate
                __draft.__show(PropId.byIndex(`$`.SLOT_DELIVERY_DATE), true)
            }
            return this
        }

        public fun status(status: SalesOrderStatus?): Builder {
            if (status !== null) {
                __draft.status = status
                __draft.__show(PropId.byIndex(`$`.SLOT_STATUS), true)
            }
            return this
        }

        public fun priority(priority: Priority?): Builder {
            if (priority !== null) {
                __draft.priority = priority
                __draft.__show(PropId.byIndex(`$`.SLOT_PRIORITY), true)
            }
            return this
        }

        public fun paymentStatus(paymentStatus: PaymentStatus?): Builder {
            if (paymentStatus !== null) {
                __draft.paymentStatus = paymentStatus
                __draft.__show(PropId.byIndex(`$`.SLOT_PAYMENT_STATUS), true)
            }
            return this
        }

        public fun invoiceType(invoiceType: InvoiceType?): Builder {
            __draft.invoiceType = invoiceType
            __draft.__show(PropId.byIndex(`$`.SLOT_INVOICE_TYPE), true)
            return this
        }

        public fun remark(remark: String?): Builder {
            __draft.remark = remark
            __draft.__show(PropId.byIndex(`$`.SLOT_REMARK), true)
            return this
        }

        public fun build(): SalesOrderEnum = __draft.__unwrap() as SalesOrderEnum
    }
}

@GeneratedBy(type = SalesOrderEnum::class)
public fun ImmutableCreator<SalesOrderEnum>.`by`(resolveImmediately: Boolean = false, block: SalesOrderEnumDraft.() -> Unit): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = SalesOrderEnum::class)
public fun ImmutableCreator<SalesOrderEnum>.`by`(base: SalesOrderEnum?, resolveImmediately: Boolean = false): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = SalesOrderEnum::class)
public fun ImmutableCreator<SalesOrderEnum>.`by`(
    base: SalesOrderEnum?,
    resolveImmediately: Boolean = false,
    block: SalesOrderEnumDraft.() -> Unit,
): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = SalesOrderEnum::class)
public fun SalesOrderEnum(resolveImmediately: Boolean = false, block: SalesOrderEnumDraft.() -> Unit): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = SalesOrderEnum::class)
public fun SalesOrderEnum(
    base: SalesOrderEnum?,
    resolveImmediately: Boolean = false,
    block: SalesOrderEnumDraft.() -> Unit,
): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = SalesOrderEnum::class)
public fun MutableList<SalesOrderEnumDraft>.addBy(resolveImmediately: Boolean = false, block: SalesOrderEnumDraft.() -> Unit): MutableList<SalesOrderEnumDraft> {
    add(SalesOrderEnumDraft.`$`.produce(null, resolveImmediately, block) as SalesOrderEnumDraft)
    return this
}

@GeneratedBy(type = SalesOrderEnum::class)
public fun MutableList<SalesOrderEnumDraft>.addBy(base: SalesOrderEnum?, resolveImmediately: Boolean = false): MutableList<SalesOrderEnumDraft> {
    add(SalesOrderEnumDraft.`$`.produce(base, resolveImmediately) as SalesOrderEnumDraft)
    return this
}

@GeneratedBy(type = SalesOrderEnum::class)
public fun MutableList<SalesOrderEnumDraft>.addBy(
    base: SalesOrderEnum?,
    resolveImmediately: Boolean = false,
    block: SalesOrderEnumDraft.() -> Unit,
): MutableList<SalesOrderEnumDraft> {
    add(SalesOrderEnumDraft.`$`.produce(base, resolveImmediately, block) as SalesOrderEnumDraft)
    return this
}

@GeneratedBy(type = SalesOrderEnum::class)
public fun SalesOrderEnum.copy(resolveImmediately: Boolean = false, block: SalesOrderEnumDraft.() -> Unit): SalesOrderEnum = SalesOrderEnumDraft.`$`.produce(this, resolveImmediately, block)
