@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = DocCard::class)
public interface DocCardDraft : DocCard, BusinessEntityDraft {
    override var cardCode: String

    override var cardName: String

    override var cardDesc: String?

    override var cardType: String

    override var cardPosition: String

    override var docType: DocType

    @get:JsonIgnore
    public var docTypeId: String

    override var componentName: String

    override var cardConfig: String?

    override var enabled: Boolean

    override var required: Boolean

    override var readonly: Boolean

    override var visible: Boolean

    override var visibleExpr: String?

    override var readonlyExpr: String?

    override var requiredExpr: String?

    override var calcExpr: String?

    override var validateExpr: String?

    override var fields: List<DocCardField>

    override var cardData: List<DocumentCardData>

    public fun docType(): DocTypeDraft

    public fun docType(block: DocTypeDraft.() -> Unit)

    public fun fields(): MutableList<DocCardFieldDraft>

    public fun cardData(): MutableList<DocumentCardDataDraft>

    @GeneratedBy(type = DocCard::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_CARD_CODE: Int = 14

        public const val SLOT_CARD_NAME: Int = 15

        public const val SLOT_CARD_DESC: Int = 16

        public const val SLOT_CARD_TYPE: Int = 17

        public const val SLOT_CARD_POSITION: Int = 18

        public const val SLOT_DOC_TYPE: Int = 19

        public const val SLOT_COMPONENT_NAME: Int = 20

        public const val SLOT_CARD_CONFIG: Int = 21

        public const val SLOT_ENABLED: Int = 22

        public const val SLOT_REQUIRED: Int = 23

        public const val SLOT_READONLY: Int = 24

        public const val SLOT_VISIBLE: Int = 25

        public const val SLOT_VISIBLE_EXPR: Int = 26

        public const val SLOT_READONLY_EXPR: Int = 27

        public const val SLOT_REQUIRED_EXPR: Int = 28

        public const val SLOT_CALC_EXPR: Int = 29

        public const val SLOT_VALIDATE_EXPR: Int = 30

        public const val SLOT_FIELDS: Int = 31

        public const val SLOT_CARD_DATA: Int = 32

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                DocCard::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as DocCard?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_CARD_CODE, "cardCode", String::class.java, false)
            .add(SLOT_CARD_NAME, "cardName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_CARD_DESC, "cardDesc", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_CARD_TYPE, "cardType", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_CARD_POSITION, "cardPosition", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_DOC_TYPE, "docType", ManyToOne::class.java, DocType::class.java, false)
            .add(SLOT_COMPONENT_NAME, "componentName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_CARD_CONFIG, "cardConfig", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_ENABLED, "enabled", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_REQUIRED, "required", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_READONLY, "readonly", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_VISIBLE, "visible", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_VISIBLE_EXPR, "visibleExpr", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_READONLY_EXPR, "readonlyExpr", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_REQUIRED_EXPR, "requiredExpr", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_CALC_EXPR, "calcExpr", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_VALIDATE_EXPR, "validateExpr", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_FIELDS, "fields", OneToMany::class.java, DocCardField::class.java, false)
            .add(SLOT_CARD_DATA, "cardData", OneToMany::class.java, DocumentCardData::class.java, false)
            .build()

        public fun produce(base: DocCard? = null, resolveImmediately: Boolean = false): DocCard {
            val consumer = DraftConsumer<DocCardDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as DocCard
        }

        public fun produce(
            base: DocCard? = null,
            resolveImmediately: Boolean = false,
            block: DocCardDraft.() -> Unit,
        ): DocCard {
            val consumer = DraftConsumer<DocCardDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as DocCard
        }

        @GeneratedBy(type = DocCard::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "cardCode", "cardName", "cardDesc", "cardType", "cardPosition", "docType", "componentName", "cardConfig", "enabled", "required", "readonly", "visible", "visibleExpr", "readonlyExpr", "requiredExpr", "calcExpr", "validateExpr", "fields", "cardData")
        private abstract interface Implementor : DocCard, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_CARD_CODE ->
                	cardCode
                SLOT_CARD_NAME ->
                	cardName
                SLOT_CARD_DESC ->
                	cardDesc
                SLOT_CARD_TYPE ->
                	cardType
                SLOT_CARD_POSITION ->
                	cardPosition
                SLOT_DOC_TYPE ->
                	docType
                SLOT_COMPONENT_NAME ->
                	componentName
                SLOT_CARD_CONFIG ->
                	cardConfig
                SLOT_ENABLED ->
                	enabled
                SLOT_REQUIRED ->
                	required
                SLOT_READONLY ->
                	readonly
                SLOT_VISIBLE ->
                	visible
                SLOT_VISIBLE_EXPR ->
                	visibleExpr
                SLOT_READONLY_EXPR ->
                	readonlyExpr
                SLOT_REQUIRED_EXPR ->
                	requiredExpr
                SLOT_CALC_EXPR ->
                	calcExpr
                SLOT_VALIDATE_EXPR ->
                	validateExpr
                SLOT_FIELDS ->
                	fields
                SLOT_CARD_DATA ->
                	cardData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "cardCode" ->
                	cardCode
                "cardName" ->
                	cardName
                "cardDesc" ->
                	cardDesc
                "cardType" ->
                	cardType
                "cardPosition" ->
                	cardPosition
                "docType" ->
                	docType
                "componentName" ->
                	componentName
                "cardConfig" ->
                	cardConfig
                "enabled" ->
                	enabled
                "required" ->
                	required
                "readonly" ->
                	readonly
                "visible" ->
                	visible
                "visibleExpr" ->
                	visibleExpr
                "readonlyExpr" ->
                	readonlyExpr
                "requiredExpr" ->
                	requiredExpr
                "calcExpr" ->
                	calcExpr
                "validateExpr" ->
                	validateExpr
                "fields" ->
                	fields
                "cardData" ->
                	cardData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = DocCard::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __cardCodeValue: String? = null

            @get:JsonIgnore
            internal var __cardNameValue: String? = null

            @get:JsonIgnore
            internal var __cardDescValue: String? = null

            @get:JsonIgnore
            internal var __cardDescLoaded: Boolean = false

            @get:JsonIgnore
            internal var __cardTypeValue: String? = null

            @get:JsonIgnore
            internal var __cardPositionValue: String? = null

            @get:JsonIgnore
            internal var __docTypeValue: DocType? = null

            @get:JsonIgnore
            internal var __componentNameValue: String? = null

            @get:JsonIgnore
            internal var __cardConfigValue: String? = null

            @get:JsonIgnore
            internal var __cardConfigLoaded: Boolean = false

            @get:JsonIgnore
            internal var __enabledValue: Boolean = false

            @get:JsonIgnore
            internal var __enabledLoaded: Boolean = false

            @get:JsonIgnore
            internal var __requiredValue: Boolean = false

            @get:JsonIgnore
            internal var __requiredLoaded: Boolean = false

            @get:JsonIgnore
            internal var __readonlyValue: Boolean = false

            @get:JsonIgnore
            internal var __readonlyLoaded: Boolean = false

            @get:JsonIgnore
            internal var __visibleValue: Boolean = false

            @get:JsonIgnore
            internal var __visibleLoaded: Boolean = false

            @get:JsonIgnore
            internal var __visibleExprValue: String? = null

            @get:JsonIgnore
            internal var __visibleExprLoaded: Boolean = false

            @get:JsonIgnore
            internal var __readonlyExprValue: String? = null

            @get:JsonIgnore
            internal var __readonlyExprLoaded: Boolean = false

            @get:JsonIgnore
            internal var __requiredExprValue: String? = null

            @get:JsonIgnore
            internal var __requiredExprLoaded: Boolean = false

            @get:JsonIgnore
            internal var __calcExprValue: String? = null

            @get:JsonIgnore
            internal var __calcExprLoaded: Boolean = false

            @get:JsonIgnore
            internal var __validateExprValue: String? = null

            @get:JsonIgnore
            internal var __validateExprLoaded: Boolean = false

            @get:JsonIgnore
            internal var __fieldsValue: NonSharedList<DocCardField>? = null

            @get:JsonIgnore
            internal var __cardDataValue: NonSharedList<DocumentCardData>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(DocCard::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(DocCard::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(DocCard::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(DocCard::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(DocCard::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(DocCard::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(DocCard::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(DocCard::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(DocCard::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(DocCard::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(DocCard::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(DocCard::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(DocCard::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(DocCard::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val cardCode: String
                get() {
                    val __cardCodeValue = this.__cardCodeValue
                    if (__cardCodeValue === null) {
                        throw UnloadedException(DocCard::class.java, "cardCode")
                    }
                    return __cardCodeValue
                }

            override val cardName: String
                get() {
                    val __cardNameValue = this.__cardNameValue
                    if (__cardNameValue === null) {
                        throw UnloadedException(DocCard::class.java, "cardName")
                    }
                    return __cardNameValue
                }

            override val cardDesc: String?
                get() {
                    if (!__cardDescLoaded) {
                        throw UnloadedException(DocCard::class.java, "cardDesc")
                    }
                    return __cardDescValue
                }

            override val cardType: String
                get() {
                    val __cardTypeValue = this.__cardTypeValue
                    if (__cardTypeValue === null) {
                        throw UnloadedException(DocCard::class.java, "cardType")
                    }
                    return __cardTypeValue
                }

            override val cardPosition: String
                get() {
                    val __cardPositionValue = this.__cardPositionValue
                    if (__cardPositionValue === null) {
                        throw UnloadedException(DocCard::class.java, "cardPosition")
                    }
                    return __cardPositionValue
                }

            override val docType: DocType
                get() {
                    val __docTypeValue = this.__docTypeValue
                    if (__docTypeValue === null) {
                        throw UnloadedException(DocCard::class.java, "docType")
                    }
                    return __docTypeValue
                }

            override val componentName: String
                get() {
                    val __componentNameValue = this.__componentNameValue
                    if (__componentNameValue === null) {
                        throw UnloadedException(DocCard::class.java, "componentName")
                    }
                    return __componentNameValue
                }

            override val cardConfig: String?
                get() {
                    if (!__cardConfigLoaded) {
                        throw UnloadedException(DocCard::class.java, "cardConfig")
                    }
                    return __cardConfigValue
                }

            override val enabled: Boolean
                get() {
                    if (!__enabledLoaded) {
                        throw UnloadedException(DocCard::class.java, "enabled")
                    }
                    return __enabledValue
                }

            override val required: Boolean
                get() {
                    if (!__requiredLoaded) {
                        throw UnloadedException(DocCard::class.java, "required")
                    }
                    return __requiredValue
                }

            override val readonly: Boolean
                get() {
                    if (!__readonlyLoaded) {
                        throw UnloadedException(DocCard::class.java, "readonly")
                    }
                    return __readonlyValue
                }

            override val visible: Boolean
                get() {
                    if (!__visibleLoaded) {
                        throw UnloadedException(DocCard::class.java, "visible")
                    }
                    return __visibleValue
                }

            override val visibleExpr: String?
                get() {
                    if (!__visibleExprLoaded) {
                        throw UnloadedException(DocCard::class.java, "visibleExpr")
                    }
                    return __visibleExprValue
                }

            override val readonlyExpr: String?
                get() {
                    if (!__readonlyExprLoaded) {
                        throw UnloadedException(DocCard::class.java, "readonlyExpr")
                    }
                    return __readonlyExprValue
                }

            override val requiredExpr: String?
                get() {
                    if (!__requiredExprLoaded) {
                        throw UnloadedException(DocCard::class.java, "requiredExpr")
                    }
                    return __requiredExprValue
                }

            override val calcExpr: String?
                get() {
                    if (!__calcExprLoaded) {
                        throw UnloadedException(DocCard::class.java, "calcExpr")
                    }
                    return __calcExprValue
                }

            override val validateExpr: String?
                get() {
                    if (!__validateExprLoaded) {
                        throw UnloadedException(DocCard::class.java, "validateExpr")
                    }
                    return __validateExprValue
                }

            override val fields: List<DocCardField>
                get() {
                    val __fieldsValue = this.__fieldsValue
                    if (__fieldsValue === null) {
                        throw UnloadedException(DocCard::class.java, "fields")
                    }
                    return __fieldsValue
                }

            override val cardData: List<DocumentCardData>
                get() {
                    val __cardDataValue = this.__cardDataValue
                    if (__cardDataValue === null) {
                        throw UnloadedException(DocCard::class.java, "cardData")
                    }
                    return __cardDataValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_CARD_CODE ->
                	__cardCodeValue !== null
                SLOT_CARD_NAME ->
                	__cardNameValue !== null
                SLOT_CARD_DESC ->
                	__cardDescLoaded
                SLOT_CARD_TYPE ->
                	__cardTypeValue !== null
                SLOT_CARD_POSITION ->
                	__cardPositionValue !== null
                SLOT_DOC_TYPE ->
                	__docTypeValue !== null
                SLOT_COMPONENT_NAME ->
                	__componentNameValue !== null
                SLOT_CARD_CONFIG ->
                	__cardConfigLoaded
                SLOT_ENABLED ->
                	__enabledLoaded
                SLOT_REQUIRED ->
                	__requiredLoaded
                SLOT_READONLY ->
                	__readonlyLoaded
                SLOT_VISIBLE ->
                	__visibleLoaded
                SLOT_VISIBLE_EXPR ->
                	__visibleExprLoaded
                SLOT_READONLY_EXPR ->
                	__readonlyExprLoaded
                SLOT_REQUIRED_EXPR ->
                	__requiredExprLoaded
                SLOT_CALC_EXPR ->
                	__calcExprLoaded
                SLOT_VALIDATE_EXPR ->
                	__validateExprLoaded
                SLOT_FIELDS ->
                	__fieldsValue !== null
                SLOT_CARD_DATA ->
                	__cardDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "cardCode" ->
                	__cardCodeValue !== null
                "cardName" ->
                	__cardNameValue !== null
                "cardDesc" ->
                	__cardDescLoaded
                "cardType" ->
                	__cardTypeValue !== null
                "cardPosition" ->
                	__cardPositionValue !== null
                "docType" ->
                	__docTypeValue !== null
                "componentName" ->
                	__componentNameValue !== null
                "cardConfig" ->
                	__cardConfigLoaded
                "enabled" ->
                	__enabledLoaded
                "required" ->
                	__requiredLoaded
                "readonly" ->
                	__readonlyLoaded
                "visible" ->
                	__visibleLoaded
                "visibleExpr" ->
                	__visibleExprLoaded
                "readonlyExpr" ->
                	__readonlyExprLoaded
                "requiredExpr" ->
                	__requiredExprLoaded
                "calcExpr" ->
                	__calcExprLoaded
                "validateExpr" ->
                	__validateExprLoaded
                "fields" ->
                	__fieldsValue !== null
                "cardData" ->
                	__cardDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_CARD_CODE ->
                    	__visibility.visible(SLOT_CARD_CODE)
                    SLOT_CARD_NAME ->
                    	__visibility.visible(SLOT_CARD_NAME)
                    SLOT_CARD_DESC ->
                    	__visibility.visible(SLOT_CARD_DESC)
                    SLOT_CARD_TYPE ->
                    	__visibility.visible(SLOT_CARD_TYPE)
                    SLOT_CARD_POSITION ->
                    	__visibility.visible(SLOT_CARD_POSITION)
                    SLOT_DOC_TYPE ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    SLOT_COMPONENT_NAME ->
                    	__visibility.visible(SLOT_COMPONENT_NAME)
                    SLOT_CARD_CONFIG ->
                    	__visibility.visible(SLOT_CARD_CONFIG)
                    SLOT_ENABLED ->
                    	__visibility.visible(SLOT_ENABLED)
                    SLOT_REQUIRED ->
                    	__visibility.visible(SLOT_REQUIRED)
                    SLOT_READONLY ->
                    	__visibility.visible(SLOT_READONLY)
                    SLOT_VISIBLE ->
                    	__visibility.visible(SLOT_VISIBLE)
                    SLOT_VISIBLE_EXPR ->
                    	__visibility.visible(SLOT_VISIBLE_EXPR)
                    SLOT_READONLY_EXPR ->
                    	__visibility.visible(SLOT_READONLY_EXPR)
                    SLOT_REQUIRED_EXPR ->
                    	__visibility.visible(SLOT_REQUIRED_EXPR)
                    SLOT_CALC_EXPR ->
                    	__visibility.visible(SLOT_CALC_EXPR)
                    SLOT_VALIDATE_EXPR ->
                    	__visibility.visible(SLOT_VALIDATE_EXPR)
                    SLOT_FIELDS ->
                    	__visibility.visible(SLOT_FIELDS)
                    SLOT_CARD_DATA ->
                    	__visibility.visible(SLOT_CARD_DATA)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "cardCode" ->
                    	__visibility.visible(SLOT_CARD_CODE)
                    "cardName" ->
                    	__visibility.visible(SLOT_CARD_NAME)
                    "cardDesc" ->
                    	__visibility.visible(SLOT_CARD_DESC)
                    "cardType" ->
                    	__visibility.visible(SLOT_CARD_TYPE)
                    "cardPosition" ->
                    	__visibility.visible(SLOT_CARD_POSITION)
                    "docType" ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    "componentName" ->
                    	__visibility.visible(SLOT_COMPONENT_NAME)
                    "cardConfig" ->
                    	__visibility.visible(SLOT_CARD_CONFIG)
                    "enabled" ->
                    	__visibility.visible(SLOT_ENABLED)
                    "required" ->
                    	__visibility.visible(SLOT_REQUIRED)
                    "readonly" ->
                    	__visibility.visible(SLOT_READONLY)
                    "visible" ->
                    	__visibility.visible(SLOT_VISIBLE)
                    "visibleExpr" ->
                    	__visibility.visible(SLOT_VISIBLE_EXPR)
                    "readonlyExpr" ->
                    	__visibility.visible(SLOT_READONLY_EXPR)
                    "requiredExpr" ->
                    	__visibility.visible(SLOT_REQUIRED_EXPR)
                    "calcExpr" ->
                    	__visibility.visible(SLOT_CALC_EXPR)
                    "validateExpr" ->
                    	__visibility.visible(SLOT_VALIDATE_EXPR)
                    "fields" ->
                    	__visibility.visible(SLOT_FIELDS)
                    "cardData" ->
                    	__visibility.visible(SLOT_CARD_DATA)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__cardCodeValue !== null) {
                    hash = 31 * hash + __cardCodeValue.hashCode()
                }
                if (__cardNameValue !== null) {
                    hash = 31 * hash + __cardNameValue.hashCode()
                }
                if (__cardDescLoaded) {
                    hash = 31 * hash + (__cardDescValue?.hashCode() ?: 0)
                }
                if (__cardTypeValue !== null) {
                    hash = 31 * hash + __cardTypeValue.hashCode()
                }
                if (__cardPositionValue !== null) {
                    hash = 31 * hash + __cardPositionValue.hashCode()
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__docTypeValue)
                }
                if (__componentNameValue !== null) {
                    hash = 31 * hash + __componentNameValue.hashCode()
                }
                if (__cardConfigLoaded) {
                    hash = 31 * hash + (__cardConfigValue?.hashCode() ?: 0)
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__requiredLoaded) {
                    hash = 31 * hash + __requiredValue.hashCode()
                }
                if (__readonlyLoaded) {
                    hash = 31 * hash + __readonlyValue.hashCode()
                }
                if (__visibleLoaded) {
                    hash = 31 * hash + __visibleValue.hashCode()
                }
                if (__visibleExprLoaded) {
                    hash = 31 * hash + (__visibleExprValue?.hashCode() ?: 0)
                }
                if (__readonlyExprLoaded) {
                    hash = 31 * hash + (__readonlyExprValue?.hashCode() ?: 0)
                }
                if (__requiredExprLoaded) {
                    hash = 31 * hash + (__requiredExprValue?.hashCode() ?: 0)
                }
                if (__calcExprLoaded) {
                    hash = 31 * hash + (__calcExprValue?.hashCode() ?: 0)
                }
                if (__validateExprLoaded) {
                    hash = 31 * hash + (__validateExprValue?.hashCode() ?: 0)
                }
                if (__fieldsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__fieldsValue)
                }
                if (__cardDataValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__cardDataValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__cardCodeValue !== null) {
                    hash = 31 * hash + __cardCodeValue.hashCode()
                }
                if (__cardNameValue !== null) {
                    hash = 31 * hash + __cardNameValue.hashCode()
                }
                if (__cardDescLoaded) {
                    hash = 31 * hash + (__cardDescValue?.hashCode() ?: 0)
                }
                if (__cardTypeValue !== null) {
                    hash = 31 * hash + __cardTypeValue.hashCode()
                }
                if (__cardPositionValue !== null) {
                    hash = 31 * hash + __cardPositionValue.hashCode()
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + __docTypeValue.hashCode()
                }
                if (__componentNameValue !== null) {
                    hash = 31 * hash + __componentNameValue.hashCode()
                }
                if (__cardConfigLoaded) {
                    hash = 31 * hash + (__cardConfigValue?.hashCode() ?: 0)
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__requiredLoaded) {
                    hash = 31 * hash + __requiredValue.hashCode()
                }
                if (__readonlyLoaded) {
                    hash = 31 * hash + __readonlyValue.hashCode()
                }
                if (__visibleLoaded) {
                    hash = 31 * hash + __visibleValue.hashCode()
                }
                if (__visibleExprLoaded) {
                    hash = 31 * hash + (__visibleExprValue?.hashCode() ?: 0)
                }
                if (__readonlyExprLoaded) {
                    hash = 31 * hash + (__readonlyExprValue?.hashCode() ?: 0)
                }
                if (__requiredExprLoaded) {
                    hash = 31 * hash + (__requiredExprValue?.hashCode() ?: 0)
                }
                if (__calcExprLoaded) {
                    hash = 31 * hash + (__calcExprValue?.hashCode() ?: 0)
                }
                if (__validateExprLoaded) {
                    hash = 31 * hash + (__validateExprValue?.hashCode() ?: 0)
                }
                if (__fieldsValue !== null) {
                    hash = 31 * hash + __fieldsValue.hashCode()
                }
                if (__cardDataValue !== null) {
                    hash = 31 * hash + __cardDataValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_CODE))) {
                    return false
                }
                val __cardCodeLoaded = 
                    this.__cardCodeValue !== null
                if (__cardCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_CODE)))) {
                    return false
                }
                if (__cardCodeLoaded && this.__cardCodeValue != __other.cardCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_NAME))) {
                    return false
                }
                val __cardNameLoaded = 
                    this.__cardNameValue !== null
                if (__cardNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_NAME)))) {
                    return false
                }
                if (__cardNameLoaded && this.__cardNameValue != __other.cardName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DESC))) {
                    return false
                }
                val __cardDescLoaded = 
                    this.__cardDescLoaded
                if (__cardDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DESC)))) {
                    return false
                }
                if (__cardDescLoaded && this.__cardDescValue != __other.cardDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_TYPE))) {
                    return false
                }
                val __cardTypeLoaded = 
                    this.__cardTypeValue !== null
                if (__cardTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_TYPE)))) {
                    return false
                }
                if (__cardTypeLoaded && this.__cardTypeValue != __other.cardType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_POSITION)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_POSITION))) {
                    return false
                }
                val __cardPositionLoaded = 
                    this.__cardPositionValue !== null
                if (__cardPositionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_POSITION)))) {
                    return false
                }
                if (__cardPositionLoaded && this.__cardPositionValue != __other.cardPosition) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue !== __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME))) {
                    return false
                }
                val __componentNameLoaded = 
                    this.__componentNameValue !== null
                if (__componentNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_NAME)))) {
                    return false
                }
                if (__componentNameLoaded && this.__componentNameValue != __other.componentName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_CONFIG))) {
                    return false
                }
                val __cardConfigLoaded = 
                    this.__cardConfigLoaded
                if (__cardConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_CONFIG)))) {
                    return false
                }
                if (__cardConfigLoaded && this.__cardConfigValue != __other.cardConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REQUIRED)) != __other.__isVisible(PropId.byIndex(SLOT_REQUIRED))) {
                    return false
                }
                val __requiredLoaded = 
                    this.__requiredLoaded
                if (__requiredLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REQUIRED)))) {
                    return false
                }
                if (__requiredLoaded && this.__requiredValue != __other.required) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_READONLY)) != __other.__isVisible(PropId.byIndex(SLOT_READONLY))) {
                    return false
                }
                val __readonlyLoaded = 
                    this.__readonlyLoaded
                if (__readonlyLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_READONLY)))) {
                    return false
                }
                if (__readonlyLoaded && this.__readonlyValue != __other.readonly) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VISIBLE)) != __other.__isVisible(PropId.byIndex(SLOT_VISIBLE))) {
                    return false
                }
                val __visibleLoaded = 
                    this.__visibleLoaded
                if (__visibleLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VISIBLE)))) {
                    return false
                }
                if (__visibleLoaded && this.__visibleValue != __other.visible) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VISIBLE_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_VISIBLE_EXPR))) {
                    return false
                }
                val __visibleExprLoaded = 
                    this.__visibleExprLoaded
                if (__visibleExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VISIBLE_EXPR)))) {
                    return false
                }
                if (__visibleExprLoaded && this.__visibleExprValue != __other.visibleExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_READONLY_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_READONLY_EXPR))) {
                    return false
                }
                val __readonlyExprLoaded = 
                    this.__readonlyExprLoaded
                if (__readonlyExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_READONLY_EXPR)))) {
                    return false
                }
                if (__readonlyExprLoaded && this.__readonlyExprValue != __other.readonlyExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REQUIRED_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_REQUIRED_EXPR))) {
                    return false
                }
                val __requiredExprLoaded = 
                    this.__requiredExprLoaded
                if (__requiredExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REQUIRED_EXPR)))) {
                    return false
                }
                if (__requiredExprLoaded && this.__requiredExprValue != __other.requiredExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CALC_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_CALC_EXPR))) {
                    return false
                }
                val __calcExprLoaded = 
                    this.__calcExprLoaded
                if (__calcExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CALC_EXPR)))) {
                    return false
                }
                if (__calcExprLoaded && this.__calcExprValue != __other.calcExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VALIDATE_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_VALIDATE_EXPR))) {
                    return false
                }
                val __validateExprLoaded = 
                    this.__validateExprLoaded
                if (__validateExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VALIDATE_EXPR)))) {
                    return false
                }
                if (__validateExprLoaded && this.__validateExprValue != __other.validateExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FIELDS)) != __other.__isVisible(PropId.byIndex(SLOT_FIELDS))) {
                    return false
                }
                val __fieldsLoaded = 
                    this.__fieldsValue !== null
                if (__fieldsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FIELDS)))) {
                    return false
                }
                if (__fieldsLoaded && this.__fieldsValue !== __other.fields) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DATA))) {
                    return false
                }
                val __cardDataLoaded = 
                    this.__cardDataValue !== null
                if (__cardDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DATA)))) {
                    return false
                }
                if (__cardDataLoaded && this.__cardDataValue !== __other.cardData) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_CODE))) {
                    return false
                }
                val __cardCodeLoaded = 
                    this.__cardCodeValue !== null
                if (__cardCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_CODE)))) {
                    return false
                }
                if (__cardCodeLoaded && this.__cardCodeValue != __other.cardCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_NAME))) {
                    return false
                }
                val __cardNameLoaded = 
                    this.__cardNameValue !== null
                if (__cardNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_NAME)))) {
                    return false
                }
                if (__cardNameLoaded && this.__cardNameValue != __other.cardName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DESC))) {
                    return false
                }
                val __cardDescLoaded = 
                    this.__cardDescLoaded
                if (__cardDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DESC)))) {
                    return false
                }
                if (__cardDescLoaded && this.__cardDescValue != __other.cardDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_TYPE))) {
                    return false
                }
                val __cardTypeLoaded = 
                    this.__cardTypeValue !== null
                if (__cardTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_TYPE)))) {
                    return false
                }
                if (__cardTypeLoaded && this.__cardTypeValue != __other.cardType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_POSITION)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_POSITION))) {
                    return false
                }
                val __cardPositionLoaded = 
                    this.__cardPositionValue !== null
                if (__cardPositionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_POSITION)))) {
                    return false
                }
                if (__cardPositionLoaded && this.__cardPositionValue != __other.cardPosition) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue != __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME))) {
                    return false
                }
                val __componentNameLoaded = 
                    this.__componentNameValue !== null
                if (__componentNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_NAME)))) {
                    return false
                }
                if (__componentNameLoaded && this.__componentNameValue != __other.componentName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_CONFIG))) {
                    return false
                }
                val __cardConfigLoaded = 
                    this.__cardConfigLoaded
                if (__cardConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_CONFIG)))) {
                    return false
                }
                if (__cardConfigLoaded && this.__cardConfigValue != __other.cardConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REQUIRED)) != __other.__isVisible(PropId.byIndex(SLOT_REQUIRED))) {
                    return false
                }
                val __requiredLoaded = 
                    this.__requiredLoaded
                if (__requiredLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REQUIRED)))) {
                    return false
                }
                if (__requiredLoaded && this.__requiredValue != __other.required) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_READONLY)) != __other.__isVisible(PropId.byIndex(SLOT_READONLY))) {
                    return false
                }
                val __readonlyLoaded = 
                    this.__readonlyLoaded
                if (__readonlyLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_READONLY)))) {
                    return false
                }
                if (__readonlyLoaded && this.__readonlyValue != __other.readonly) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VISIBLE)) != __other.__isVisible(PropId.byIndex(SLOT_VISIBLE))) {
                    return false
                }
                val __visibleLoaded = 
                    this.__visibleLoaded
                if (__visibleLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VISIBLE)))) {
                    return false
                }
                if (__visibleLoaded && this.__visibleValue != __other.visible) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VISIBLE_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_VISIBLE_EXPR))) {
                    return false
                }
                val __visibleExprLoaded = 
                    this.__visibleExprLoaded
                if (__visibleExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VISIBLE_EXPR)))) {
                    return false
                }
                if (__visibleExprLoaded && this.__visibleExprValue != __other.visibleExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_READONLY_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_READONLY_EXPR))) {
                    return false
                }
                val __readonlyExprLoaded = 
                    this.__readonlyExprLoaded
                if (__readonlyExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_READONLY_EXPR)))) {
                    return false
                }
                if (__readonlyExprLoaded && this.__readonlyExprValue != __other.readonlyExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REQUIRED_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_REQUIRED_EXPR))) {
                    return false
                }
                val __requiredExprLoaded = 
                    this.__requiredExprLoaded
                if (__requiredExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REQUIRED_EXPR)))) {
                    return false
                }
                if (__requiredExprLoaded && this.__requiredExprValue != __other.requiredExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CALC_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_CALC_EXPR))) {
                    return false
                }
                val __calcExprLoaded = 
                    this.__calcExprLoaded
                if (__calcExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CALC_EXPR)))) {
                    return false
                }
                if (__calcExprLoaded && this.__calcExprValue != __other.calcExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VALIDATE_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_VALIDATE_EXPR))) {
                    return false
                }
                val __validateExprLoaded = 
                    this.__validateExprLoaded
                if (__validateExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VALIDATE_EXPR)))) {
                    return false
                }
                if (__validateExprLoaded && this.__validateExprValue != __other.validateExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FIELDS)) != __other.__isVisible(PropId.byIndex(SLOT_FIELDS))) {
                    return false
                }
                val __fieldsLoaded = 
                    this.__fieldsValue !== null
                if (__fieldsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FIELDS)))) {
                    return false
                }
                if (__fieldsLoaded && this.__fieldsValue != __other.fields) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARD_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_CARD_DATA))) {
                    return false
                }
                val __cardDataLoaded = 
                    this.__cardDataValue !== null
                if (__cardDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARD_DATA)))) {
                    return false
                }
                if (__cardDataLoaded && this.__cardDataValue != __other.cardData) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = DocCard::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: DocCard?,
        ) : Implementor,
            DocCardDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: DocCard? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var cardCode: String
                get() = (__modified ?: __base!!).cardCode
                set(cardCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardCodeValue = cardCode
                }

            override var cardName: String
                get() = (__modified ?: __base!!).cardName
                set(cardName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardNameValue = cardName
                }

            override var cardDesc: String?
                get() = (__modified ?: __base!!).cardDesc
                set(cardDesc) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardDescValue = cardDesc
                    __tmpModified.__cardDescLoaded = true
                }

            override var cardType: String
                get() = (__modified ?: __base!!).cardType
                set(cardType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardTypeValue = cardType
                }

            override var cardPosition: String
                get() = (__modified ?: __base!!).cardPosition
                set(cardPosition) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardPositionValue = cardPosition
                }

            override var docType: DocType
                get() = __ctx().toDraftObject((__modified ?: __base!!).docType)
                set(docType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docTypeValue = docType
                }

            @get:JsonIgnore
            public override var docTypeId: String
                get() = docType.id
                set(docTypeId) {
                    docType().id = docTypeId
                }

            override var componentName: String
                get() = (__modified ?: __base!!).componentName
                set(componentName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentNameValue = componentName
                }

            override var cardConfig: String?
                get() = (__modified ?: __base!!).cardConfig
                set(cardConfig) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardConfigValue = cardConfig
                    __tmpModified.__cardConfigLoaded = true
                }

            override var enabled: Boolean
                get() = (__modified ?: __base!!).enabled
                set(enabled) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__enabledValue = enabled
                    __tmpModified.__enabledLoaded = true
                }

            override var required: Boolean
                get() = (__modified ?: __base!!).required
                set(required) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__requiredValue = required
                    __tmpModified.__requiredLoaded = true
                }

            override var readonly: Boolean
                get() = (__modified ?: __base!!).readonly
                set(readonly) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__readonlyValue = readonly
                    __tmpModified.__readonlyLoaded = true
                }

            override var visible: Boolean
                get() = (__modified ?: __base!!).visible
                set(visible) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__visibleValue = visible
                    __tmpModified.__visibleLoaded = true
                }

            override var visibleExpr: String?
                get() = (__modified ?: __base!!).visibleExpr
                set(visibleExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__visibleExprValue = visibleExpr
                    __tmpModified.__visibleExprLoaded = true
                }

            override var readonlyExpr: String?
                get() = (__modified ?: __base!!).readonlyExpr
                set(readonlyExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__readonlyExprValue = readonlyExpr
                    __tmpModified.__readonlyExprLoaded = true
                }

            override var requiredExpr: String?
                get() = (__modified ?: __base!!).requiredExpr
                set(requiredExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__requiredExprValue = requiredExpr
                    __tmpModified.__requiredExprLoaded = true
                }

            override var calcExpr: String?
                get() = (__modified ?: __base!!).calcExpr
                set(calcExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__calcExprValue = calcExpr
                    __tmpModified.__calcExprLoaded = true
                }

            override var validateExpr: String?
                get() = (__modified ?: __base!!).validateExpr
                set(validateExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__validateExprValue = validateExpr
                    __tmpModified.__validateExprLoaded = true
                }

            override var fields: List<DocCardField>
                get() = __ctx().toDraftList((__modified ?: __base!!).fields, DocCardField::class.java, true)
                set(fields) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__fieldsValue = NonSharedList.of(__tmpModified.__fieldsValue, fields)
                }

            override var cardData: List<DocumentCardData>
                get() = __ctx().toDraftList((__modified ?: __base!!).cardData, DocumentCardData::class.java, true)
                set(cardData) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardDataValue = NonSharedList.of(__tmpModified.__cardDataValue, cardData)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun docType(): DocTypeDraft {
                if (!__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                    docType = DocTypeDraft.`$`.produce()
                }
                return docType as DocTypeDraft
            }

            override fun docType(block: DocTypeDraft.() -> Unit) {
                docType().apply(block)
            }

            override fun fields(): MutableList<DocCardFieldDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_FIELDS))) {
                    fields = emptyList()
                }
                return fields as MutableList<DocCardFieldDraft>
            }

            override fun cardData(): MutableList<DocumentCardDataDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_CARD_DATA))) {
                    cardData = emptyList()
                }
                return cardData as MutableList<DocumentCardDataDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_CARD_CODE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardCodeValue = null
                    SLOT_CARD_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardNameValue = null
                    SLOT_CARD_DESC ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardDescLoaded = false
                        }
                    SLOT_CARD_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardTypeValue = null
                    SLOT_CARD_POSITION ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardPositionValue = null
                    SLOT_DOC_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    SLOT_COMPONENT_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentNameValue = null
                    SLOT_CARD_CONFIG ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardConfigLoaded = false
                        }
                    SLOT_ENABLED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    SLOT_REQUIRED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredLoaded = false
                        }
                    SLOT_READONLY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyLoaded = false
                        }
                    SLOT_VISIBLE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleLoaded = false
                        }
                    SLOT_VISIBLE_EXPR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleExprLoaded = false
                        }
                    SLOT_READONLY_EXPR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyExprLoaded = false
                        }
                    SLOT_REQUIRED_EXPR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredExprLoaded = false
                        }
                    SLOT_CALC_EXPR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__calcExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__calcExprLoaded = false
                        }
                    SLOT_VALIDATE_EXPR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__validateExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__validateExprLoaded = false
                        }
                    SLOT_FIELDS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__fieldsValue = null
                    SLOT_CARD_DATA ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "cardCode" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardCodeValue = null
                    "cardName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardNameValue = null
                    "cardDesc" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardDescLoaded = false
                        }
                    "cardType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardTypeValue = null
                    "cardPosition" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardPositionValue = null
                    "docType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    "componentName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentNameValue = null
                    "cardConfig" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__cardConfigLoaded = false
                        }
                    "enabled" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    "required" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredLoaded = false
                        }
                    "readonly" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyLoaded = false
                        }
                    "visible" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleLoaded = false
                        }
                    "visibleExpr" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__visibleExprLoaded = false
                        }
                    "readonlyExpr" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__readonlyExprLoaded = false
                        }
                    "requiredExpr" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__requiredExprLoaded = false
                        }
                    "calcExpr" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__calcExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__calcExprLoaded = false
                        }
                    "validateExpr" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__validateExprValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__validateExprLoaded = false
                        }
                    "fields" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__fieldsValue = null
                    "cardData" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_CARD_CODE ->
                    	this.cardCode = value as String?
                    	?: throw IllegalArgumentException("'cardCode cannot be null")
                    SLOT_CARD_NAME ->
                    	this.cardName = value as String?
                    	?: throw IllegalArgumentException("'cardName cannot be null")
                    SLOT_CARD_DESC ->
                    	this.cardDesc = value as String?
                    SLOT_CARD_TYPE ->
                    	this.cardType = value as String?
                    	?: throw IllegalArgumentException("'cardType cannot be null")
                    SLOT_CARD_POSITION ->
                    	this.cardPosition = value as String?
                    	?: throw IllegalArgumentException("'cardPosition cannot be null")
                    SLOT_DOC_TYPE ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    SLOT_COMPONENT_NAME ->
                    	this.componentName = value as String?
                    	?: throw IllegalArgumentException("'componentName cannot be null")
                    SLOT_CARD_CONFIG ->
                    	this.cardConfig = value as String?
                    SLOT_ENABLED ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    SLOT_REQUIRED ->
                    	this.required = value as Boolean?
                    	?: throw IllegalArgumentException("'required cannot be null")
                    SLOT_READONLY ->
                    	this.readonly = value as Boolean?
                    	?: throw IllegalArgumentException("'readonly cannot be null")
                    SLOT_VISIBLE ->
                    	this.visible = value as Boolean?
                    	?: throw IllegalArgumentException("'visible cannot be null")
                    SLOT_VISIBLE_EXPR ->
                    	this.visibleExpr = value as String?
                    SLOT_READONLY_EXPR ->
                    	this.readonlyExpr = value as String?
                    SLOT_REQUIRED_EXPR ->
                    	this.requiredExpr = value as String?
                    SLOT_CALC_EXPR ->
                    	this.calcExpr = value as String?
                    SLOT_VALIDATE_EXPR ->
                    	this.validateExpr = value as String?
                    SLOT_FIELDS ->
                    	this.fields = value as List<DocCardField>?
                    	?: throw IllegalArgumentException("'fields cannot be null")
                    SLOT_CARD_DATA ->
                    	this.cardData = value as List<DocumentCardData>?
                    	?: throw IllegalArgumentException("'cardData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "cardCode" ->
                    	this.cardCode = value as String?
                    	?: throw IllegalArgumentException("'cardCode cannot be null")
                    "cardName" ->
                    	this.cardName = value as String?
                    	?: throw IllegalArgumentException("'cardName cannot be null")
                    "cardDesc" ->
                    	this.cardDesc = value as String?
                    "cardType" ->
                    	this.cardType = value as String?
                    	?: throw IllegalArgumentException("'cardType cannot be null")
                    "cardPosition" ->
                    	this.cardPosition = value as String?
                    	?: throw IllegalArgumentException("'cardPosition cannot be null")
                    "docType" ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    "componentName" ->
                    	this.componentName = value as String?
                    	?: throw IllegalArgumentException("'componentName cannot be null")
                    "cardConfig" ->
                    	this.cardConfig = value as String?
                    "enabled" ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    "required" ->
                    	this.required = value as Boolean?
                    	?: throw IllegalArgumentException("'required cannot be null")
                    "readonly" ->
                    	this.readonly = value as Boolean?
                    	?: throw IllegalArgumentException("'readonly cannot be null")
                    "visible" ->
                    	this.visible = value as Boolean?
                    	?: throw IllegalArgumentException("'visible cannot be null")
                    "visibleExpr" ->
                    	this.visibleExpr = value as String?
                    "readonlyExpr" ->
                    	this.readonlyExpr = value as String?
                    "requiredExpr" ->
                    	this.requiredExpr = value as String?
                    "calcExpr" ->
                    	this.calcExpr = value as String?
                    "validateExpr" ->
                    	this.validateExpr = value as String?
                    "fields" ->
                    	this.fields = value as List<DocCardField>?
                    	?: throw IllegalArgumentException("'fields cannot be null")
                    "cardData" ->
                    	this.cardData = value as List<DocumentCardData>?
                    	?: throw IllegalArgumentException("'cardData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocCard\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(33).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_CARD_CODE ->
                    	__visibility.show(SLOT_CARD_CODE, visible)
                    SLOT_CARD_NAME ->
                    	__visibility.show(SLOT_CARD_NAME, visible)
                    SLOT_CARD_DESC ->
                    	__visibility.show(SLOT_CARD_DESC, visible)
                    SLOT_CARD_TYPE ->
                    	__visibility.show(SLOT_CARD_TYPE, visible)
                    SLOT_CARD_POSITION ->
                    	__visibility.show(SLOT_CARD_POSITION, visible)
                    SLOT_DOC_TYPE ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    SLOT_COMPONENT_NAME ->
                    	__visibility.show(SLOT_COMPONENT_NAME, visible)
                    SLOT_CARD_CONFIG ->
                    	__visibility.show(SLOT_CARD_CONFIG, visible)
                    SLOT_ENABLED ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    SLOT_REQUIRED ->
                    	__visibility.show(SLOT_REQUIRED, visible)
                    SLOT_READONLY ->
                    	__visibility.show(SLOT_READONLY, visible)
                    SLOT_VISIBLE ->
                    	__visibility.show(SLOT_VISIBLE, visible)
                    SLOT_VISIBLE_EXPR ->
                    	__visibility.show(SLOT_VISIBLE_EXPR, visible)
                    SLOT_READONLY_EXPR ->
                    	__visibility.show(SLOT_READONLY_EXPR, visible)
                    SLOT_REQUIRED_EXPR ->
                    	__visibility.show(SLOT_REQUIRED_EXPR, visible)
                    SLOT_CALC_EXPR ->
                    	__visibility.show(SLOT_CALC_EXPR, visible)
                    SLOT_VALIDATE_EXPR ->
                    	__visibility.show(SLOT_VALIDATE_EXPR, visible)
                    SLOT_FIELDS ->
                    	__visibility.show(SLOT_FIELDS, visible)
                    SLOT_CARD_DATA ->
                    	__visibility.show(SLOT_CARD_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(33).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "cardCode" ->
                    	__visibility.show(SLOT_CARD_CODE, visible)
                    "cardName" ->
                    	__visibility.show(SLOT_CARD_NAME, visible)
                    "cardDesc" ->
                    	__visibility.show(SLOT_CARD_DESC, visible)
                    "cardType" ->
                    	__visibility.show(SLOT_CARD_TYPE, visible)
                    "cardPosition" ->
                    	__visibility.show(SLOT_CARD_POSITION, visible)
                    "docType" ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    "componentName" ->
                    	__visibility.show(SLOT_COMPONENT_NAME, visible)
                    "cardConfig" ->
                    	__visibility.show(SLOT_CARD_CONFIG, visible)
                    "enabled" ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    "required" ->
                    	__visibility.show(SLOT_REQUIRED, visible)
                    "readonly" ->
                    	__visibility.show(SLOT_READONLY, visible)
                    "visible" ->
                    	__visibility.show(SLOT_VISIBLE, visible)
                    "visibleExpr" ->
                    	__visibility.show(SLOT_VISIBLE_EXPR, visible)
                    "readonlyExpr" ->
                    	__visibility.show(SLOT_READONLY_EXPR, visible)
                    "requiredExpr" ->
                    	__visibility.show(SLOT_REQUIRED_EXPR, visible)
                    "calcExpr" ->
                    	__visibility.show(SLOT_CALC_EXPR, visible)
                    "validateExpr" ->
                    	__visibility.show(SLOT_VALIDATE_EXPR, visible)
                    "fields" ->
                    	__visibility.show(SLOT_FIELDS, visible)
                    "cardData" ->
                    	__visibility.show(SLOT_CARD_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                            val oldValue = base!!.docType
                            val newValue = __ctx.resolveObject(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_FIELDS))) {
                            val oldValue = base!!.fields
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_CARD_DATA))) {
                            val oldValue = base!!.cardData
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__docTypeValue = __ctx.resolveObject(__tmpModified.__docTypeValue)
                        __tmpModified.__fieldsValue = NonSharedList.of(__tmpModified.__fieldsValue, __ctx.resolveList(__tmpModified.__fieldsValue))
                        __tmpModified.__cardDataValue = NonSharedList.of(__tmpModified.__cardDataValue, __ctx.resolveList(__tmpModified.__cardDataValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = DocCard::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: DocCard?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun cardCode(cardCode: String?): Builder {
            if (cardCode !== null) {
                __draft.cardCode = cardCode
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_CODE), true)
            }
            return this
        }

        public fun cardName(cardName: String?): Builder {
            if (cardName !== null) {
                __draft.cardName = cardName
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_NAME), true)
            }
            return this
        }

        public fun cardDesc(cardDesc: String?): Builder {
            __draft.cardDesc = cardDesc
            __draft.__show(PropId.byIndex(`$`.SLOT_CARD_DESC), true)
            return this
        }

        public fun cardType(cardType: String?): Builder {
            if (cardType !== null) {
                __draft.cardType = cardType
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_TYPE), true)
            }
            return this
        }

        public fun cardPosition(cardPosition: String?): Builder {
            if (cardPosition !== null) {
                __draft.cardPosition = cardPosition
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_POSITION), true)
            }
            return this
        }

        public fun docType(docType: DocType?): Builder {
            if (docType !== null) {
                __draft.docType = docType
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_TYPE), true)
            }
            return this
        }

        public fun componentName(componentName: String?): Builder {
            if (componentName !== null) {
                __draft.componentName = componentName
                __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_NAME), true)
            }
            return this
        }

        public fun cardConfig(cardConfig: String?): Builder {
            __draft.cardConfig = cardConfig
            __draft.__show(PropId.byIndex(`$`.SLOT_CARD_CONFIG), true)
            return this
        }

        public fun enabled(enabled: Boolean?): Builder {
            if (enabled !== null) {
                __draft.enabled = enabled
                __draft.__show(PropId.byIndex(`$`.SLOT_ENABLED), true)
            }
            return this
        }

        public fun required(required: Boolean?): Builder {
            if (required !== null) {
                __draft.required = required
                __draft.__show(PropId.byIndex(`$`.SLOT_REQUIRED), true)
            }
            return this
        }

        public fun readonly(readonly: Boolean?): Builder {
            if (readonly !== null) {
                __draft.readonly = readonly
                __draft.__show(PropId.byIndex(`$`.SLOT_READONLY), true)
            }
            return this
        }

        public fun visible(visible: Boolean?): Builder {
            if (visible !== null) {
                __draft.visible = visible
                __draft.__show(PropId.byIndex(`$`.SLOT_VISIBLE), true)
            }
            return this
        }

        public fun visibleExpr(visibleExpr: String?): Builder {
            __draft.visibleExpr = visibleExpr
            __draft.__show(PropId.byIndex(`$`.SLOT_VISIBLE_EXPR), true)
            return this
        }

        public fun readonlyExpr(readonlyExpr: String?): Builder {
            __draft.readonlyExpr = readonlyExpr
            __draft.__show(PropId.byIndex(`$`.SLOT_READONLY_EXPR), true)
            return this
        }

        public fun requiredExpr(requiredExpr: String?): Builder {
            __draft.requiredExpr = requiredExpr
            __draft.__show(PropId.byIndex(`$`.SLOT_REQUIRED_EXPR), true)
            return this
        }

        public fun calcExpr(calcExpr: String?): Builder {
            __draft.calcExpr = calcExpr
            __draft.__show(PropId.byIndex(`$`.SLOT_CALC_EXPR), true)
            return this
        }

        public fun validateExpr(validateExpr: String?): Builder {
            __draft.validateExpr = validateExpr
            __draft.__show(PropId.byIndex(`$`.SLOT_VALIDATE_EXPR), true)
            return this
        }

        public fun fields(fields: List<DocCardField>?): Builder {
            if (fields !== null) {
                __draft.fields = fields
                __draft.__show(PropId.byIndex(`$`.SLOT_FIELDS), true)
            }
            return this
        }

        public fun cardData(cardData: List<DocumentCardData>?): Builder {
            if (cardData !== null) {
                __draft.cardData = cardData
                __draft.__show(PropId.byIndex(`$`.SLOT_CARD_DATA), true)
            }
            return this
        }

        public fun build(): DocCard = __draft.__unwrap() as DocCard
    }
}

@GeneratedBy(type = DocCard::class)
public fun ImmutableCreator<DocCard>.`by`(resolveImmediately: Boolean = false, block: DocCardDraft.() -> Unit): DocCard = DocCardDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocCard::class)
public fun ImmutableCreator<DocCard>.`by`(base: DocCard?, resolveImmediately: Boolean = false): DocCard = DocCardDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = DocCard::class)
public fun ImmutableCreator<DocCard>.`by`(
    base: DocCard?,
    resolveImmediately: Boolean = false,
    block: DocCardDraft.() -> Unit,
): DocCard = DocCardDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocCard::class)
public fun DocCard(resolveImmediately: Boolean = false, block: DocCardDraft.() -> Unit): DocCard = DocCardDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocCard::class)
public fun DocCard(
    base: DocCard?,
    resolveImmediately: Boolean = false,
    block: DocCardDraft.() -> Unit,
): DocCard = DocCardDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocCard::class)
public fun MutableList<DocCardDraft>.addBy(resolveImmediately: Boolean = false, block: DocCardDraft.() -> Unit): MutableList<DocCardDraft> {
    add(DocCardDraft.`$`.produce(null, resolveImmediately, block) as DocCardDraft)
    return this
}

@GeneratedBy(type = DocCard::class)
public fun MutableList<DocCardDraft>.addBy(base: DocCard?, resolveImmediately: Boolean = false): MutableList<DocCardDraft> {
    add(DocCardDraft.`$`.produce(base, resolveImmediately) as DocCardDraft)
    return this
}

@GeneratedBy(type = DocCard::class)
public fun MutableList<DocCardDraft>.addBy(
    base: DocCard?,
    resolveImmediately: Boolean = false,
    block: DocCardDraft.() -> Unit,
): MutableList<DocCardDraft> {
    add(DocCardDraft.`$`.produce(base, resolveImmediately, block) as DocCardDraft)
    return this
}

@GeneratedBy(type = DocCard::class)
public fun DocCard.copy(resolveImmediately: Boolean = false, block: DocCardDraft.() -> Unit): DocCard = DocCardDraft.`$`.produce(this, resolveImmediately, block)
