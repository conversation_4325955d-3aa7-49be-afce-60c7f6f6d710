@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = BusinessFlow::class)
public interface BusinessFlowDraft : BusinessFlow, BusinessEntityDraft {
    override var flowCode: String

    override var flowName: String

    override var flowDesc: String?

    override var flowType: String

    override var enabled: Boolean

    override var systemBuiltIn: Boolean

    override var flowConfig: String?

    override var nodes: List<BusinessFlowNode>

    override var connections: List<BusinessFlowConnection>

    override var instances: List<BusinessFlowInstance>

    public fun nodes(): MutableList<BusinessFlowNodeDraft>

    public fun connections(): MutableList<BusinessFlowConnectionDraft>

    public fun instances(): MutableList<BusinessFlowInstanceDraft>

    @GeneratedBy(type = BusinessFlow::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_FLOW_CODE: Int = 14

        public const val SLOT_FLOW_NAME: Int = 15

        public const val SLOT_FLOW_DESC: Int = 16

        public const val SLOT_FLOW_TYPE: Int = 17

        public const val SLOT_ENABLED: Int = 18

        public const val SLOT_SYSTEM_BUILT_IN: Int = 19

        public const val SLOT_FLOW_CONFIG: Int = 20

        public const val SLOT_NODES: Int = 21

        public const val SLOT_CONNECTIONS: Int = 22

        public const val SLOT_INSTANCES: Int = 23

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                BusinessFlow::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as BusinessFlow?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_FLOW_CODE, "flowCode", String::class.java, false)
            .add(SLOT_FLOW_NAME, "flowName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_FLOW_DESC, "flowDesc", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_FLOW_TYPE, "flowType", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_ENABLED, "enabled", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_SYSTEM_BUILT_IN, "systemBuiltIn", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_FLOW_CONFIG, "flowConfig", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_NODES, "nodes", OneToMany::class.java, BusinessFlowNode::class.java, false)
            .add(SLOT_CONNECTIONS, "connections", OneToMany::class.java, BusinessFlowConnection::class.java, false)
            .add(SLOT_INSTANCES, "instances", OneToMany::class.java, BusinessFlowInstance::class.java, false)
            .build()

        public fun produce(base: BusinessFlow? = null, resolveImmediately: Boolean = false): BusinessFlow {
            val consumer = DraftConsumer<BusinessFlowDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as BusinessFlow
        }

        public fun produce(
            base: BusinessFlow? = null,
            resolveImmediately: Boolean = false,
            block: BusinessFlowDraft.() -> Unit,
        ): BusinessFlow {
            val consumer = DraftConsumer<BusinessFlowDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as BusinessFlow
        }

        @GeneratedBy(type = BusinessFlow::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "flowCode", "flowName", "flowDesc", "flowType", "enabled", "systemBuiltIn", "flowConfig", "nodes", "connections", "instances")
        private abstract interface Implementor : BusinessFlow, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_FLOW_CODE ->
                	flowCode
                SLOT_FLOW_NAME ->
                	flowName
                SLOT_FLOW_DESC ->
                	flowDesc
                SLOT_FLOW_TYPE ->
                	flowType
                SLOT_ENABLED ->
                	enabled
                SLOT_SYSTEM_BUILT_IN ->
                	systemBuiltIn
                SLOT_FLOW_CONFIG ->
                	flowConfig
                SLOT_NODES ->
                	nodes
                SLOT_CONNECTIONS ->
                	connections
                SLOT_INSTANCES ->
                	instances
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "flowCode" ->
                	flowCode
                "flowName" ->
                	flowName
                "flowDesc" ->
                	flowDesc
                "flowType" ->
                	flowType
                "enabled" ->
                	enabled
                "systemBuiltIn" ->
                	systemBuiltIn
                "flowConfig" ->
                	flowConfig
                "nodes" ->
                	nodes
                "connections" ->
                	connections
                "instances" ->
                	instances
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = BusinessFlow::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __flowCodeValue: String? = null

            @get:JsonIgnore
            internal var __flowNameValue: String? = null

            @get:JsonIgnore
            internal var __flowDescValue: String? = null

            @get:JsonIgnore
            internal var __flowDescLoaded: Boolean = false

            @get:JsonIgnore
            internal var __flowTypeValue: String? = null

            @get:JsonIgnore
            internal var __enabledValue: Boolean = false

            @get:JsonIgnore
            internal var __enabledLoaded: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInValue: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInLoaded: Boolean = false

            @get:JsonIgnore
            internal var __flowConfigValue: String? = null

            @get:JsonIgnore
            internal var __flowConfigLoaded: Boolean = false

            @get:JsonIgnore
            internal var __nodesValue: NonSharedList<BusinessFlowNode>? = null

            @get:JsonIgnore
            internal var __connectionsValue: NonSharedList<BusinessFlowConnection>? = null

            @get:JsonIgnore
            internal var __instancesValue: NonSharedList<BusinessFlowInstance>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val flowCode: String
                get() {
                    val __flowCodeValue = this.__flowCodeValue
                    if (__flowCodeValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "flowCode")
                    }
                    return __flowCodeValue
                }

            override val flowName: String
                get() {
                    val __flowNameValue = this.__flowNameValue
                    if (__flowNameValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "flowName")
                    }
                    return __flowNameValue
                }

            override val flowDesc: String?
                get() {
                    if (!__flowDescLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "flowDesc")
                    }
                    return __flowDescValue
                }

            override val flowType: String
                get() {
                    val __flowTypeValue = this.__flowTypeValue
                    if (__flowTypeValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "flowType")
                    }
                    return __flowTypeValue
                }

            override val enabled: Boolean
                get() {
                    if (!__enabledLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "enabled")
                    }
                    return __enabledValue
                }

            override val systemBuiltIn: Boolean
                get() {
                    if (!__systemBuiltInLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "systemBuiltIn")
                    }
                    return __systemBuiltInValue
                }

            override val flowConfig: String?
                get() {
                    if (!__flowConfigLoaded) {
                        throw UnloadedException(BusinessFlow::class.java, "flowConfig")
                    }
                    return __flowConfigValue
                }

            override val nodes: List<BusinessFlowNode>
                get() {
                    val __nodesValue = this.__nodesValue
                    if (__nodesValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "nodes")
                    }
                    return __nodesValue
                }

            override val connections: List<BusinessFlowConnection>
                get() {
                    val __connectionsValue = this.__connectionsValue
                    if (__connectionsValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "connections")
                    }
                    return __connectionsValue
                }

            override val instances: List<BusinessFlowInstance>
                get() {
                    val __instancesValue = this.__instancesValue
                    if (__instancesValue === null) {
                        throw UnloadedException(BusinessFlow::class.java, "instances")
                    }
                    return __instancesValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_FLOW_CODE ->
                	__flowCodeValue !== null
                SLOT_FLOW_NAME ->
                	__flowNameValue !== null
                SLOT_FLOW_DESC ->
                	__flowDescLoaded
                SLOT_FLOW_TYPE ->
                	__flowTypeValue !== null
                SLOT_ENABLED ->
                	__enabledLoaded
                SLOT_SYSTEM_BUILT_IN ->
                	__systemBuiltInLoaded
                SLOT_FLOW_CONFIG ->
                	__flowConfigLoaded
                SLOT_NODES ->
                	__nodesValue !== null
                SLOT_CONNECTIONS ->
                	__connectionsValue !== null
                SLOT_INSTANCES ->
                	__instancesValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "flowCode" ->
                	__flowCodeValue !== null
                "flowName" ->
                	__flowNameValue !== null
                "flowDesc" ->
                	__flowDescLoaded
                "flowType" ->
                	__flowTypeValue !== null
                "enabled" ->
                	__enabledLoaded
                "systemBuiltIn" ->
                	__systemBuiltInLoaded
                "flowConfig" ->
                	__flowConfigLoaded
                "nodes" ->
                	__nodesValue !== null
                "connections" ->
                	__connectionsValue !== null
                "instances" ->
                	__instancesValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_FLOW_CODE ->
                    	__visibility.visible(SLOT_FLOW_CODE)
                    SLOT_FLOW_NAME ->
                    	__visibility.visible(SLOT_FLOW_NAME)
                    SLOT_FLOW_DESC ->
                    	__visibility.visible(SLOT_FLOW_DESC)
                    SLOT_FLOW_TYPE ->
                    	__visibility.visible(SLOT_FLOW_TYPE)
                    SLOT_ENABLED ->
                    	__visibility.visible(SLOT_ENABLED)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    SLOT_FLOW_CONFIG ->
                    	__visibility.visible(SLOT_FLOW_CONFIG)
                    SLOT_NODES ->
                    	__visibility.visible(SLOT_NODES)
                    SLOT_CONNECTIONS ->
                    	__visibility.visible(SLOT_CONNECTIONS)
                    SLOT_INSTANCES ->
                    	__visibility.visible(SLOT_INSTANCES)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "flowCode" ->
                    	__visibility.visible(SLOT_FLOW_CODE)
                    "flowName" ->
                    	__visibility.visible(SLOT_FLOW_NAME)
                    "flowDesc" ->
                    	__visibility.visible(SLOT_FLOW_DESC)
                    "flowType" ->
                    	__visibility.visible(SLOT_FLOW_TYPE)
                    "enabled" ->
                    	__visibility.visible(SLOT_ENABLED)
                    "systemBuiltIn" ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    "flowConfig" ->
                    	__visibility.visible(SLOT_FLOW_CONFIG)
                    "nodes" ->
                    	__visibility.visible(SLOT_NODES)
                    "connections" ->
                    	__visibility.visible(SLOT_CONNECTIONS)
                    "instances" ->
                    	__visibility.visible(SLOT_INSTANCES)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__flowCodeValue !== null) {
                    hash = 31 * hash + __flowCodeValue.hashCode()
                }
                if (__flowNameValue !== null) {
                    hash = 31 * hash + __flowNameValue.hashCode()
                }
                if (__flowDescLoaded) {
                    hash = 31 * hash + (__flowDescValue?.hashCode() ?: 0)
                }
                if (__flowTypeValue !== null) {
                    hash = 31 * hash + __flowTypeValue.hashCode()
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__flowConfigLoaded) {
                    hash = 31 * hash + (__flowConfigValue?.hashCode() ?: 0)
                }
                if (__nodesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__nodesValue)
                }
                if (__connectionsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__connectionsValue)
                }
                if (__instancesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__instancesValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__flowCodeValue !== null) {
                    hash = 31 * hash + __flowCodeValue.hashCode()
                }
                if (__flowNameValue !== null) {
                    hash = 31 * hash + __flowNameValue.hashCode()
                }
                if (__flowDescLoaded) {
                    hash = 31 * hash + (__flowDescValue?.hashCode() ?: 0)
                }
                if (__flowTypeValue !== null) {
                    hash = 31 * hash + __flowTypeValue.hashCode()
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__flowConfigLoaded) {
                    hash = 31 * hash + (__flowConfigValue?.hashCode() ?: 0)
                }
                if (__nodesValue !== null) {
                    hash = 31 * hash + __nodesValue.hashCode()
                }
                if (__connectionsValue !== null) {
                    hash = 31 * hash + __connectionsValue.hashCode()
                }
                if (__instancesValue !== null) {
                    hash = 31 * hash + __instancesValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_CODE))) {
                    return false
                }
                val __flowCodeLoaded = 
                    this.__flowCodeValue !== null
                if (__flowCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_CODE)))) {
                    return false
                }
                if (__flowCodeLoaded && this.__flowCodeValue != __other.flowCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_NAME))) {
                    return false
                }
                val __flowNameLoaded = 
                    this.__flowNameValue !== null
                if (__flowNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_NAME)))) {
                    return false
                }
                if (__flowNameLoaded && this.__flowNameValue != __other.flowName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_DESC))) {
                    return false
                }
                val __flowDescLoaded = 
                    this.__flowDescLoaded
                if (__flowDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_DESC)))) {
                    return false
                }
                if (__flowDescLoaded && this.__flowDescValue != __other.flowDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_TYPE))) {
                    return false
                }
                val __flowTypeLoaded = 
                    this.__flowTypeValue !== null
                if (__flowTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_TYPE)))) {
                    return false
                }
                if (__flowTypeLoaded && this.__flowTypeValue != __other.flowType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_CONFIG))) {
                    return false
                }
                val __flowConfigLoaded = 
                    this.__flowConfigLoaded
                if (__flowConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_CONFIG)))) {
                    return false
                }
                if (__flowConfigLoaded && this.__flowConfigValue != __other.flowConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_NODES)) != __other.__isVisible(PropId.byIndex(SLOT_NODES))) {
                    return false
                }
                val __nodesLoaded = 
                    this.__nodesValue !== null
                if (__nodesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_NODES)))) {
                    return false
                }
                if (__nodesLoaded && this.__nodesValue !== __other.nodes) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CONNECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_CONNECTIONS))) {
                    return false
                }
                val __connectionsLoaded = 
                    this.__connectionsValue !== null
                if (__connectionsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CONNECTIONS)))) {
                    return false
                }
                if (__connectionsLoaded && this.__connectionsValue !== __other.connections) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INSTANCES)) != __other.__isVisible(PropId.byIndex(SLOT_INSTANCES))) {
                    return false
                }
                val __instancesLoaded = 
                    this.__instancesValue !== null
                if (__instancesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INSTANCES)))) {
                    return false
                }
                if (__instancesLoaded && this.__instancesValue !== __other.instances) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_CODE))) {
                    return false
                }
                val __flowCodeLoaded = 
                    this.__flowCodeValue !== null
                if (__flowCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_CODE)))) {
                    return false
                }
                if (__flowCodeLoaded && this.__flowCodeValue != __other.flowCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_NAME))) {
                    return false
                }
                val __flowNameLoaded = 
                    this.__flowNameValue !== null
                if (__flowNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_NAME)))) {
                    return false
                }
                if (__flowNameLoaded && this.__flowNameValue != __other.flowName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_DESC))) {
                    return false
                }
                val __flowDescLoaded = 
                    this.__flowDescLoaded
                if (__flowDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_DESC)))) {
                    return false
                }
                if (__flowDescLoaded && this.__flowDescValue != __other.flowDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_TYPE))) {
                    return false
                }
                val __flowTypeLoaded = 
                    this.__flowTypeValue !== null
                if (__flowTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_TYPE)))) {
                    return false
                }
                if (__flowTypeLoaded && this.__flowTypeValue != __other.flowType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_FLOW_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_FLOW_CONFIG))) {
                    return false
                }
                val __flowConfigLoaded = 
                    this.__flowConfigLoaded
                if (__flowConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_FLOW_CONFIG)))) {
                    return false
                }
                if (__flowConfigLoaded && this.__flowConfigValue != __other.flowConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_NODES)) != __other.__isVisible(PropId.byIndex(SLOT_NODES))) {
                    return false
                }
                val __nodesLoaded = 
                    this.__nodesValue !== null
                if (__nodesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_NODES)))) {
                    return false
                }
                if (__nodesLoaded && this.__nodesValue != __other.nodes) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CONNECTIONS)) != __other.__isVisible(PropId.byIndex(SLOT_CONNECTIONS))) {
                    return false
                }
                val __connectionsLoaded = 
                    this.__connectionsValue !== null
                if (__connectionsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CONNECTIONS)))) {
                    return false
                }
                if (__connectionsLoaded && this.__connectionsValue != __other.connections) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INSTANCES)) != __other.__isVisible(PropId.byIndex(SLOT_INSTANCES))) {
                    return false
                }
                val __instancesLoaded = 
                    this.__instancesValue !== null
                if (__instancesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INSTANCES)))) {
                    return false
                }
                if (__instancesLoaded && this.__instancesValue != __other.instances) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = BusinessFlow::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: BusinessFlow?,
        ) : Implementor,
            BusinessFlowDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: BusinessFlow? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var flowCode: String
                get() = (__modified ?: __base!!).flowCode
                set(flowCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__flowCodeValue = flowCode
                }

            override var flowName: String
                get() = (__modified ?: __base!!).flowName
                set(flowName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__flowNameValue = flowName
                }

            override var flowDesc: String?
                get() = (__modified ?: __base!!).flowDesc
                set(flowDesc) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__flowDescValue = flowDesc
                    __tmpModified.__flowDescLoaded = true
                }

            override var flowType: String
                get() = (__modified ?: __base!!).flowType
                set(flowType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__flowTypeValue = flowType
                }

            override var enabled: Boolean
                get() = (__modified ?: __base!!).enabled
                set(enabled) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__enabledValue = enabled
                    __tmpModified.__enabledLoaded = true
                }

            override var systemBuiltIn: Boolean
                get() = (__modified ?: __base!!).systemBuiltIn
                set(systemBuiltIn) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__systemBuiltInValue = systemBuiltIn
                    __tmpModified.__systemBuiltInLoaded = true
                }

            override var flowConfig: String?
                get() = (__modified ?: __base!!).flowConfig
                set(flowConfig) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__flowConfigValue = flowConfig
                    __tmpModified.__flowConfigLoaded = true
                }

            override var nodes: List<BusinessFlowNode>
                get() = __ctx().toDraftList((__modified ?: __base!!).nodes, BusinessFlowNode::class.java, true)
                set(nodes) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__nodesValue = NonSharedList.of(__tmpModified.__nodesValue, nodes)
                }

            override var connections: List<BusinessFlowConnection>
                get() = __ctx().toDraftList((__modified ?: __base!!).connections, BusinessFlowConnection::class.java, true)
                set(connections) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__connectionsValue = NonSharedList.of(__tmpModified.__connectionsValue, connections)
                }

            override var instances: List<BusinessFlowInstance>
                get() = __ctx().toDraftList((__modified ?: __base!!).instances, BusinessFlowInstance::class.java, true)
                set(instances) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__instancesValue = NonSharedList.of(__tmpModified.__instancesValue, instances)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun nodes(): MutableList<BusinessFlowNodeDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_NODES))) {
                    nodes = emptyList()
                }
                return nodes as MutableList<BusinessFlowNodeDraft>
            }

            override fun connections(): MutableList<BusinessFlowConnectionDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_CONNECTIONS))) {
                    connections = emptyList()
                }
                return connections as MutableList<BusinessFlowConnectionDraft>
            }

            override fun instances(): MutableList<BusinessFlowInstanceDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_INSTANCES))) {
                    instances = emptyList()
                }
                return instances as MutableList<BusinessFlowInstanceDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_FLOW_CODE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowCodeValue = null
                    SLOT_FLOW_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowNameValue = null
                    SLOT_FLOW_DESC ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowDescLoaded = false
                        }
                    SLOT_FLOW_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowTypeValue = null
                    SLOT_ENABLED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    SLOT_SYSTEM_BUILT_IN ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    SLOT_FLOW_CONFIG ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowConfigLoaded = false
                        }
                    SLOT_NODES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__nodesValue = null
                    SLOT_CONNECTIONS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__connectionsValue = null
                    SLOT_INSTANCES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__instancesValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "flowCode" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowCodeValue = null
                    "flowName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowNameValue = null
                    "flowDesc" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowDescLoaded = false
                        }
                    "flowType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__flowTypeValue = null
                    "enabled" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    "systemBuiltIn" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    "flowConfig" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__flowConfigLoaded = false
                        }
                    "nodes" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__nodesValue = null
                    "connections" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__connectionsValue = null
                    "instances" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__instancesValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_FLOW_CODE ->
                    	this.flowCode = value as String?
                    	?: throw IllegalArgumentException("'flowCode cannot be null")
                    SLOT_FLOW_NAME ->
                    	this.flowName = value as String?
                    	?: throw IllegalArgumentException("'flowName cannot be null")
                    SLOT_FLOW_DESC ->
                    	this.flowDesc = value as String?
                    SLOT_FLOW_TYPE ->
                    	this.flowType = value as String?
                    	?: throw IllegalArgumentException("'flowType cannot be null")
                    SLOT_ENABLED ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    SLOT_SYSTEM_BUILT_IN ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    SLOT_FLOW_CONFIG ->
                    	this.flowConfig = value as String?
                    SLOT_NODES ->
                    	this.nodes = value as List<BusinessFlowNode>?
                    	?: throw IllegalArgumentException("'nodes cannot be null")
                    SLOT_CONNECTIONS ->
                    	this.connections = value as List<BusinessFlowConnection>?
                    	?: throw IllegalArgumentException("'connections cannot be null")
                    SLOT_INSTANCES ->
                    	this.instances = value as List<BusinessFlowInstance>?
                    	?: throw IllegalArgumentException("'instances cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "flowCode" ->
                    	this.flowCode = value as String?
                    	?: throw IllegalArgumentException("'flowCode cannot be null")
                    "flowName" ->
                    	this.flowName = value as String?
                    	?: throw IllegalArgumentException("'flowName cannot be null")
                    "flowDesc" ->
                    	this.flowDesc = value as String?
                    "flowType" ->
                    	this.flowType = value as String?
                    	?: throw IllegalArgumentException("'flowType cannot be null")
                    "enabled" ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    "systemBuiltIn" ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    "flowConfig" ->
                    	this.flowConfig = value as String?
                    "nodes" ->
                    	this.nodes = value as List<BusinessFlowNode>?
                    	?: throw IllegalArgumentException("'nodes cannot be null")
                    "connections" ->
                    	this.connections = value as List<BusinessFlowConnection>?
                    	?: throw IllegalArgumentException("'connections cannot be null")
                    "instances" ->
                    	this.instances = value as List<BusinessFlowInstance>?
                    	?: throw IllegalArgumentException("'instances cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.BusinessFlow\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(24).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_FLOW_CODE ->
                    	__visibility.show(SLOT_FLOW_CODE, visible)
                    SLOT_FLOW_NAME ->
                    	__visibility.show(SLOT_FLOW_NAME, visible)
                    SLOT_FLOW_DESC ->
                    	__visibility.show(SLOT_FLOW_DESC, visible)
                    SLOT_FLOW_TYPE ->
                    	__visibility.show(SLOT_FLOW_TYPE, visible)
                    SLOT_ENABLED ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    SLOT_FLOW_CONFIG ->
                    	__visibility.show(SLOT_FLOW_CONFIG, visible)
                    SLOT_NODES ->
                    	__visibility.show(SLOT_NODES, visible)
                    SLOT_CONNECTIONS ->
                    	__visibility.show(SLOT_CONNECTIONS, visible)
                    SLOT_INSTANCES ->
                    	__visibility.show(SLOT_INSTANCES, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(24).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "flowCode" ->
                    	__visibility.show(SLOT_FLOW_CODE, visible)
                    "flowName" ->
                    	__visibility.show(SLOT_FLOW_NAME, visible)
                    "flowDesc" ->
                    	__visibility.show(SLOT_FLOW_DESC, visible)
                    "flowType" ->
                    	__visibility.show(SLOT_FLOW_TYPE, visible)
                    "enabled" ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    "systemBuiltIn" ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    "flowConfig" ->
                    	__visibility.show(SLOT_FLOW_CONFIG, visible)
                    "nodes" ->
                    	__visibility.show(SLOT_NODES, visible)
                    "connections" ->
                    	__visibility.show(SLOT_CONNECTIONS, visible)
                    "instances" ->
                    	__visibility.show(SLOT_INSTANCES, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_NODES))) {
                            val oldValue = base!!.nodes
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_CONNECTIONS))) {
                            val oldValue = base!!.connections
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_INSTANCES))) {
                            val oldValue = base!!.instances
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__nodesValue = NonSharedList.of(__tmpModified.__nodesValue, __ctx.resolveList(__tmpModified.__nodesValue))
                        __tmpModified.__connectionsValue = NonSharedList.of(__tmpModified.__connectionsValue, __ctx.resolveList(__tmpModified.__connectionsValue))
                        __tmpModified.__instancesValue = NonSharedList.of(__tmpModified.__instancesValue, __ctx.resolveList(__tmpModified.__instancesValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = BusinessFlow::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: BusinessFlow?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun flowCode(flowCode: String?): Builder {
            if (flowCode !== null) {
                __draft.flowCode = flowCode
                __draft.__show(PropId.byIndex(`$`.SLOT_FLOW_CODE), true)
            }
            return this
        }

        public fun flowName(flowName: String?): Builder {
            if (flowName !== null) {
                __draft.flowName = flowName
                __draft.__show(PropId.byIndex(`$`.SLOT_FLOW_NAME), true)
            }
            return this
        }

        public fun flowDesc(flowDesc: String?): Builder {
            __draft.flowDesc = flowDesc
            __draft.__show(PropId.byIndex(`$`.SLOT_FLOW_DESC), true)
            return this
        }

        public fun flowType(flowType: String?): Builder {
            if (flowType !== null) {
                __draft.flowType = flowType
                __draft.__show(PropId.byIndex(`$`.SLOT_FLOW_TYPE), true)
            }
            return this
        }

        public fun enabled(enabled: Boolean?): Builder {
            if (enabled !== null) {
                __draft.enabled = enabled
                __draft.__show(PropId.byIndex(`$`.SLOT_ENABLED), true)
            }
            return this
        }

        public fun systemBuiltIn(systemBuiltIn: Boolean?): Builder {
            if (systemBuiltIn !== null) {
                __draft.systemBuiltIn = systemBuiltIn
                __draft.__show(PropId.byIndex(`$`.SLOT_SYSTEM_BUILT_IN), true)
            }
            return this
        }

        public fun flowConfig(flowConfig: String?): Builder {
            __draft.flowConfig = flowConfig
            __draft.__show(PropId.byIndex(`$`.SLOT_FLOW_CONFIG), true)
            return this
        }

        public fun nodes(nodes: List<BusinessFlowNode>?): Builder {
            if (nodes !== null) {
                __draft.nodes = nodes
                __draft.__show(PropId.byIndex(`$`.SLOT_NODES), true)
            }
            return this
        }

        public fun connections(connections: List<BusinessFlowConnection>?): Builder {
            if (connections !== null) {
                __draft.connections = connections
                __draft.__show(PropId.byIndex(`$`.SLOT_CONNECTIONS), true)
            }
            return this
        }

        public fun instances(instances: List<BusinessFlowInstance>?): Builder {
            if (instances !== null) {
                __draft.instances = instances
                __draft.__show(PropId.byIndex(`$`.SLOT_INSTANCES), true)
            }
            return this
        }

        public fun build(): BusinessFlow = __draft.__unwrap() as BusinessFlow
    }
}

@GeneratedBy(type = BusinessFlow::class)
public fun ImmutableCreator<BusinessFlow>.`by`(resolveImmediately: Boolean = false, block: BusinessFlowDraft.() -> Unit): BusinessFlow = BusinessFlowDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = BusinessFlow::class)
public fun ImmutableCreator<BusinessFlow>.`by`(base: BusinessFlow?, resolveImmediately: Boolean = false): BusinessFlow = BusinessFlowDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = BusinessFlow::class)
public fun ImmutableCreator<BusinessFlow>.`by`(
    base: BusinessFlow?,
    resolveImmediately: Boolean = false,
    block: BusinessFlowDraft.() -> Unit,
): BusinessFlow = BusinessFlowDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = BusinessFlow::class)
public fun BusinessFlow(resolveImmediately: Boolean = false, block: BusinessFlowDraft.() -> Unit): BusinessFlow = BusinessFlowDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = BusinessFlow::class)
public fun BusinessFlow(
    base: BusinessFlow?,
    resolveImmediately: Boolean = false,
    block: BusinessFlowDraft.() -> Unit,
): BusinessFlow = BusinessFlowDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = BusinessFlow::class)
public fun MutableList<BusinessFlowDraft>.addBy(resolveImmediately: Boolean = false, block: BusinessFlowDraft.() -> Unit): MutableList<BusinessFlowDraft> {
    add(BusinessFlowDraft.`$`.produce(null, resolveImmediately, block) as BusinessFlowDraft)
    return this
}

@GeneratedBy(type = BusinessFlow::class)
public fun MutableList<BusinessFlowDraft>.addBy(base: BusinessFlow?, resolveImmediately: Boolean = false): MutableList<BusinessFlowDraft> {
    add(BusinessFlowDraft.`$`.produce(base, resolveImmediately) as BusinessFlowDraft)
    return this
}

@GeneratedBy(type = BusinessFlow::class)
public fun MutableList<BusinessFlowDraft>.addBy(
    base: BusinessFlow?,
    resolveImmediately: Boolean = false,
    block: BusinessFlowDraft.() -> Unit,
): MutableList<BusinessFlowDraft> {
    add(BusinessFlowDraft.`$`.produce(base, resolveImmediately, block) as BusinessFlowDraft)
    return this
}

@GeneratedBy(type = BusinessFlow::class)
public fun BusinessFlow.copy(resolveImmediately: Boolean = false, block: BusinessFlowDraft.() -> Unit): BusinessFlow = BusinessFlowDraft.`$`.produce(this, resolveImmediately, block)
