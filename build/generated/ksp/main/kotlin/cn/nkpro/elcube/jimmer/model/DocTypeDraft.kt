@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = DocType::class)
public interface DocTypeDraft : DocType, BusinessEntityDraft {
    override var docType: String

    override var docName: String

    override var docDesc: String?

    override var docIcon: String?

    override var docColor: String?

    override var enabled: Boolean

    override var systemBuiltIn: Boolean

    override var docConfig: String?

    override var states: List<DocState>

    override var cards: List<DocCard>

    override var indexes: List<DocIndex>

    override var documents: List<Document>

    public fun states(): MutableList<DocStateDraft>

    public fun cards(): MutableList<DocCardDraft>

    public fun indexes(): MutableList<DocIndexDraft>

    public fun documents(): MutableList<DocumentDraft>

    @GeneratedBy(type = DocType::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_DOC_TYPE: Int = 14

        public const val SLOT_DOC_NAME: Int = 15

        public const val SLOT_DOC_DESC: Int = 16

        public const val SLOT_DOC_ICON: Int = 17

        public const val SLOT_DOC_COLOR: Int = 18

        public const val SLOT_ENABLED: Int = 19

        public const val SLOT_SYSTEM_BUILT_IN: Int = 20

        public const val SLOT_DOC_CONFIG: Int = 21

        public const val SLOT_STATES: Int = 22

        public const val SLOT_CARDS: Int = 23

        public const val SLOT_INDEXES: Int = 24

        public const val SLOT_DOCUMENTS: Int = 25

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                DocType::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as DocType?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_DOC_TYPE, "docType", String::class.java, false)
            .add(SLOT_DOC_NAME, "docName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_DOC_DESC, "docDesc", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_ICON, "docIcon", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DOC_COLOR, "docColor", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_ENABLED, "enabled", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_SYSTEM_BUILT_IN, "systemBuiltIn", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_DOC_CONFIG, "docConfig", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_STATES, "states", OneToMany::class.java, DocState::class.java, false)
            .add(SLOT_CARDS, "cards", OneToMany::class.java, DocCard::class.java, false)
            .add(SLOT_INDEXES, "indexes", OneToMany::class.java, DocIndex::class.java, false)
            .add(SLOT_DOCUMENTS, "documents", OneToMany::class.java, Document::class.java, false)
            .build()

        public fun produce(base: DocType? = null, resolveImmediately: Boolean = false): DocType {
            val consumer = DraftConsumer<DocTypeDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as DocType
        }

        public fun produce(
            base: DocType? = null,
            resolveImmediately: Boolean = false,
            block: DocTypeDraft.() -> Unit,
        ): DocType {
            val consumer = DraftConsumer<DocTypeDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as DocType
        }

        @GeneratedBy(type = DocType::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "docType", "docName", "docDesc", "docIcon", "docColor", "enabled", "systemBuiltIn", "docConfig", "states", "cards", "indexes", "documents")
        private abstract interface Implementor : DocType, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_DOC_TYPE ->
                	docType
                SLOT_DOC_NAME ->
                	docName
                SLOT_DOC_DESC ->
                	docDesc
                SLOT_DOC_ICON ->
                	docIcon
                SLOT_DOC_COLOR ->
                	docColor
                SLOT_ENABLED ->
                	enabled
                SLOT_SYSTEM_BUILT_IN ->
                	systemBuiltIn
                SLOT_DOC_CONFIG ->
                	docConfig
                SLOT_STATES ->
                	states
                SLOT_CARDS ->
                	cards
                SLOT_INDEXES ->
                	indexes
                SLOT_DOCUMENTS ->
                	documents
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "docType" ->
                	docType
                "docName" ->
                	docName
                "docDesc" ->
                	docDesc
                "docIcon" ->
                	docIcon
                "docColor" ->
                	docColor
                "enabled" ->
                	enabled
                "systemBuiltIn" ->
                	systemBuiltIn
                "docConfig" ->
                	docConfig
                "states" ->
                	states
                "cards" ->
                	cards
                "indexes" ->
                	indexes
                "documents" ->
                	documents
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = DocType::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docTypeValue: String? = null

            @get:JsonIgnore
            internal var __docNameValue: String? = null

            @get:JsonIgnore
            internal var __docDescValue: String? = null

            @get:JsonIgnore
            internal var __docDescLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docIconValue: String? = null

            @get:JsonIgnore
            internal var __docIconLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docColorValue: String? = null

            @get:JsonIgnore
            internal var __docColorLoaded: Boolean = false

            @get:JsonIgnore
            internal var __enabledValue: Boolean = false

            @get:JsonIgnore
            internal var __enabledLoaded: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInValue: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInLoaded: Boolean = false

            @get:JsonIgnore
            internal var __docConfigValue: String? = null

            @get:JsonIgnore
            internal var __docConfigLoaded: Boolean = false

            @get:JsonIgnore
            internal var __statesValue: NonSharedList<DocState>? = null

            @get:JsonIgnore
            internal var __cardsValue: NonSharedList<DocCard>? = null

            @get:JsonIgnore
            internal var __indexesValue: NonSharedList<DocIndex>? = null

            @get:JsonIgnore
            internal var __documentsValue: NonSharedList<Document>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(DocType::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(DocType::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(DocType::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(DocType::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(DocType::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(DocType::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(DocType::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(DocType::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(DocType::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(DocType::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(DocType::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(DocType::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(DocType::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(DocType::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val docType: String
                get() {
                    val __docTypeValue = this.__docTypeValue
                    if (__docTypeValue === null) {
                        throw UnloadedException(DocType::class.java, "docType")
                    }
                    return __docTypeValue
                }

            override val docName: String
                get() {
                    val __docNameValue = this.__docNameValue
                    if (__docNameValue === null) {
                        throw UnloadedException(DocType::class.java, "docName")
                    }
                    return __docNameValue
                }

            override val docDesc: String?
                get() {
                    if (!__docDescLoaded) {
                        throw UnloadedException(DocType::class.java, "docDesc")
                    }
                    return __docDescValue
                }

            override val docIcon: String?
                get() {
                    if (!__docIconLoaded) {
                        throw UnloadedException(DocType::class.java, "docIcon")
                    }
                    return __docIconValue
                }

            override val docColor: String?
                get() {
                    if (!__docColorLoaded) {
                        throw UnloadedException(DocType::class.java, "docColor")
                    }
                    return __docColorValue
                }

            override val enabled: Boolean
                get() {
                    if (!__enabledLoaded) {
                        throw UnloadedException(DocType::class.java, "enabled")
                    }
                    return __enabledValue
                }

            override val systemBuiltIn: Boolean
                get() {
                    if (!__systemBuiltInLoaded) {
                        throw UnloadedException(DocType::class.java, "systemBuiltIn")
                    }
                    return __systemBuiltInValue
                }

            override val docConfig: String?
                get() {
                    if (!__docConfigLoaded) {
                        throw UnloadedException(DocType::class.java, "docConfig")
                    }
                    return __docConfigValue
                }

            override val states: List<DocState>
                get() {
                    val __statesValue = this.__statesValue
                    if (__statesValue === null) {
                        throw UnloadedException(DocType::class.java, "states")
                    }
                    return __statesValue
                }

            override val cards: List<DocCard>
                get() {
                    val __cardsValue = this.__cardsValue
                    if (__cardsValue === null) {
                        throw UnloadedException(DocType::class.java, "cards")
                    }
                    return __cardsValue
                }

            override val indexes: List<DocIndex>
                get() {
                    val __indexesValue = this.__indexesValue
                    if (__indexesValue === null) {
                        throw UnloadedException(DocType::class.java, "indexes")
                    }
                    return __indexesValue
                }

            override val documents: List<Document>
                get() {
                    val __documentsValue = this.__documentsValue
                    if (__documentsValue === null) {
                        throw UnloadedException(DocType::class.java, "documents")
                    }
                    return __documentsValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_DOC_TYPE ->
                	__docTypeValue !== null
                SLOT_DOC_NAME ->
                	__docNameValue !== null
                SLOT_DOC_DESC ->
                	__docDescLoaded
                SLOT_DOC_ICON ->
                	__docIconLoaded
                SLOT_DOC_COLOR ->
                	__docColorLoaded
                SLOT_ENABLED ->
                	__enabledLoaded
                SLOT_SYSTEM_BUILT_IN ->
                	__systemBuiltInLoaded
                SLOT_DOC_CONFIG ->
                	__docConfigLoaded
                SLOT_STATES ->
                	__statesValue !== null
                SLOT_CARDS ->
                	__cardsValue !== null
                SLOT_INDEXES ->
                	__indexesValue !== null
                SLOT_DOCUMENTS ->
                	__documentsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "docType" ->
                	__docTypeValue !== null
                "docName" ->
                	__docNameValue !== null
                "docDesc" ->
                	__docDescLoaded
                "docIcon" ->
                	__docIconLoaded
                "docColor" ->
                	__docColorLoaded
                "enabled" ->
                	__enabledLoaded
                "systemBuiltIn" ->
                	__systemBuiltInLoaded
                "docConfig" ->
                	__docConfigLoaded
                "states" ->
                	__statesValue !== null
                "cards" ->
                	__cardsValue !== null
                "indexes" ->
                	__indexesValue !== null
                "documents" ->
                	__documentsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_DOC_TYPE ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    SLOT_DOC_NAME ->
                    	__visibility.visible(SLOT_DOC_NAME)
                    SLOT_DOC_DESC ->
                    	__visibility.visible(SLOT_DOC_DESC)
                    SLOT_DOC_ICON ->
                    	__visibility.visible(SLOT_DOC_ICON)
                    SLOT_DOC_COLOR ->
                    	__visibility.visible(SLOT_DOC_COLOR)
                    SLOT_ENABLED ->
                    	__visibility.visible(SLOT_ENABLED)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    SLOT_DOC_CONFIG ->
                    	__visibility.visible(SLOT_DOC_CONFIG)
                    SLOT_STATES ->
                    	__visibility.visible(SLOT_STATES)
                    SLOT_CARDS ->
                    	__visibility.visible(SLOT_CARDS)
                    SLOT_INDEXES ->
                    	__visibility.visible(SLOT_INDEXES)
                    SLOT_DOCUMENTS ->
                    	__visibility.visible(SLOT_DOCUMENTS)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "docType" ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    "docName" ->
                    	__visibility.visible(SLOT_DOC_NAME)
                    "docDesc" ->
                    	__visibility.visible(SLOT_DOC_DESC)
                    "docIcon" ->
                    	__visibility.visible(SLOT_DOC_ICON)
                    "docColor" ->
                    	__visibility.visible(SLOT_DOC_COLOR)
                    "enabled" ->
                    	__visibility.visible(SLOT_ENABLED)
                    "systemBuiltIn" ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    "docConfig" ->
                    	__visibility.visible(SLOT_DOC_CONFIG)
                    "states" ->
                    	__visibility.visible(SLOT_STATES)
                    "cards" ->
                    	__visibility.visible(SLOT_CARDS)
                    "indexes" ->
                    	__visibility.visible(SLOT_INDEXES)
                    "documents" ->
                    	__visibility.visible(SLOT_DOCUMENTS)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + __docTypeValue.hashCode()
                }
                if (__docNameValue !== null) {
                    hash = 31 * hash + __docNameValue.hashCode()
                }
                if (__docDescLoaded) {
                    hash = 31 * hash + (__docDescValue?.hashCode() ?: 0)
                }
                if (__docIconLoaded) {
                    hash = 31 * hash + (__docIconValue?.hashCode() ?: 0)
                }
                if (__docColorLoaded) {
                    hash = 31 * hash + (__docColorValue?.hashCode() ?: 0)
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__docConfigLoaded) {
                    hash = 31 * hash + (__docConfigValue?.hashCode() ?: 0)
                }
                if (__statesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__statesValue)
                }
                if (__cardsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__cardsValue)
                }
                if (__indexesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__indexesValue)
                }
                if (__documentsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__documentsValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + __docTypeValue.hashCode()
                }
                if (__docNameValue !== null) {
                    hash = 31 * hash + __docNameValue.hashCode()
                }
                if (__docDescLoaded) {
                    hash = 31 * hash + (__docDescValue?.hashCode() ?: 0)
                }
                if (__docIconLoaded) {
                    hash = 31 * hash + (__docIconValue?.hashCode() ?: 0)
                }
                if (__docColorLoaded) {
                    hash = 31 * hash + (__docColorValue?.hashCode() ?: 0)
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__docConfigLoaded) {
                    hash = 31 * hash + (__docConfigValue?.hashCode() ?: 0)
                }
                if (__statesValue !== null) {
                    hash = 31 * hash + __statesValue.hashCode()
                }
                if (__cardsValue !== null) {
                    hash = 31 * hash + __cardsValue.hashCode()
                }
                if (__indexesValue !== null) {
                    hash = 31 * hash + __indexesValue.hashCode()
                }
                if (__documentsValue !== null) {
                    hash = 31 * hash + __documentsValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue != __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NAME))) {
                    return false
                }
                val __docNameLoaded = 
                    this.__docNameValue !== null
                if (__docNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NAME)))) {
                    return false
                }
                if (__docNameLoaded && this.__docNameValue != __other.docName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DESC))) {
                    return false
                }
                val __docDescLoaded = 
                    this.__docDescLoaded
                if (__docDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DESC)))) {
                    return false
                }
                if (__docDescLoaded && this.__docDescValue != __other.docDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_ICON)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_ICON))) {
                    return false
                }
                val __docIconLoaded = 
                    this.__docIconLoaded
                if (__docIconLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_ICON)))) {
                    return false
                }
                if (__docIconLoaded && this.__docIconValue != __other.docIcon) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_COLOR)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_COLOR))) {
                    return false
                }
                val __docColorLoaded = 
                    this.__docColorLoaded
                if (__docColorLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_COLOR)))) {
                    return false
                }
                if (__docColorLoaded && this.__docColorValue != __other.docColor) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_CONFIG))) {
                    return false
                }
                val __docConfigLoaded = 
                    this.__docConfigLoaded
                if (__docConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_CONFIG)))) {
                    return false
                }
                if (__docConfigLoaded && this.__docConfigValue != __other.docConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATES)) != __other.__isVisible(PropId.byIndex(SLOT_STATES))) {
                    return false
                }
                val __statesLoaded = 
                    this.__statesValue !== null
                if (__statesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATES)))) {
                    return false
                }
                if (__statesLoaded && this.__statesValue !== __other.states) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARDS)) != __other.__isVisible(PropId.byIndex(SLOT_CARDS))) {
                    return false
                }
                val __cardsLoaded = 
                    this.__cardsValue !== null
                if (__cardsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARDS)))) {
                    return false
                }
                if (__cardsLoaded && this.__cardsValue !== __other.cards) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEXES)) != __other.__isVisible(PropId.byIndex(SLOT_INDEXES))) {
                    return false
                }
                val __indexesLoaded = 
                    this.__indexesValue !== null
                if (__indexesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEXES)))) {
                    return false
                }
                if (__indexesLoaded && this.__indexesValue !== __other.indexes) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOCUMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_DOCUMENTS))) {
                    return false
                }
                val __documentsLoaded = 
                    this.__documentsValue !== null
                if (__documentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOCUMENTS)))) {
                    return false
                }
                if (__documentsLoaded && this.__documentsValue !== __other.documents) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue != __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_NAME))) {
                    return false
                }
                val __docNameLoaded = 
                    this.__docNameValue !== null
                if (__docNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_NAME)))) {
                    return false
                }
                if (__docNameLoaded && this.__docNameValue != __other.docName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_DESC))) {
                    return false
                }
                val __docDescLoaded = 
                    this.__docDescLoaded
                if (__docDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_DESC)))) {
                    return false
                }
                if (__docDescLoaded && this.__docDescValue != __other.docDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_ICON)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_ICON))) {
                    return false
                }
                val __docIconLoaded = 
                    this.__docIconLoaded
                if (__docIconLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_ICON)))) {
                    return false
                }
                if (__docIconLoaded && this.__docIconValue != __other.docIcon) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_COLOR)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_COLOR))) {
                    return false
                }
                val __docColorLoaded = 
                    this.__docColorLoaded
                if (__docColorLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_COLOR)))) {
                    return false
                }
                if (__docColorLoaded && this.__docColorValue != __other.docColor) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_CONFIG)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_CONFIG))) {
                    return false
                }
                val __docConfigLoaded = 
                    this.__docConfigLoaded
                if (__docConfigLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_CONFIG)))) {
                    return false
                }
                if (__docConfigLoaded && this.__docConfigValue != __other.docConfig) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATES)) != __other.__isVisible(PropId.byIndex(SLOT_STATES))) {
                    return false
                }
                val __statesLoaded = 
                    this.__statesValue !== null
                if (__statesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATES)))) {
                    return false
                }
                if (__statesLoaded && this.__statesValue != __other.states) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CARDS)) != __other.__isVisible(PropId.byIndex(SLOT_CARDS))) {
                    return false
                }
                val __cardsLoaded = 
                    this.__cardsValue !== null
                if (__cardsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CARDS)))) {
                    return false
                }
                if (__cardsLoaded && this.__cardsValue != __other.cards) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEXES)) != __other.__isVisible(PropId.byIndex(SLOT_INDEXES))) {
                    return false
                }
                val __indexesLoaded = 
                    this.__indexesValue !== null
                if (__indexesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEXES)))) {
                    return false
                }
                if (__indexesLoaded && this.__indexesValue != __other.indexes) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOCUMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_DOCUMENTS))) {
                    return false
                }
                val __documentsLoaded = 
                    this.__documentsValue !== null
                if (__documentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOCUMENTS)))) {
                    return false
                }
                if (__documentsLoaded && this.__documentsValue != __other.documents) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = DocType::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: DocType?,
        ) : Implementor,
            DocTypeDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: DocType? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var docType: String
                get() = (__modified ?: __base!!).docType
                set(docType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docTypeValue = docType
                }

            override var docName: String
                get() = (__modified ?: __base!!).docName
                set(docName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docNameValue = docName
                }

            override var docDesc: String?
                get() = (__modified ?: __base!!).docDesc
                set(docDesc) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docDescValue = docDesc
                    __tmpModified.__docDescLoaded = true
                }

            override var docIcon: String?
                get() = (__modified ?: __base!!).docIcon
                set(docIcon) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docIconValue = docIcon
                    __tmpModified.__docIconLoaded = true
                }

            override var docColor: String?
                get() = (__modified ?: __base!!).docColor
                set(docColor) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docColorValue = docColor
                    __tmpModified.__docColorLoaded = true
                }

            override var enabled: Boolean
                get() = (__modified ?: __base!!).enabled
                set(enabled) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__enabledValue = enabled
                    __tmpModified.__enabledLoaded = true
                }

            override var systemBuiltIn: Boolean
                get() = (__modified ?: __base!!).systemBuiltIn
                set(systemBuiltIn) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__systemBuiltInValue = systemBuiltIn
                    __tmpModified.__systemBuiltInLoaded = true
                }

            override var docConfig: String?
                get() = (__modified ?: __base!!).docConfig
                set(docConfig) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docConfigValue = docConfig
                    __tmpModified.__docConfigLoaded = true
                }

            override var states: List<DocState>
                get() = __ctx().toDraftList((__modified ?: __base!!).states, DocState::class.java, true)
                set(states) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__statesValue = NonSharedList.of(__tmpModified.__statesValue, states)
                }

            override var cards: List<DocCard>
                get() = __ctx().toDraftList((__modified ?: __base!!).cards, DocCard::class.java, true)
                set(cards) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__cardsValue = NonSharedList.of(__tmpModified.__cardsValue, cards)
                }

            override var indexes: List<DocIndex>
                get() = __ctx().toDraftList((__modified ?: __base!!).indexes, DocIndex::class.java, true)
                set(indexes) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexesValue = NonSharedList.of(__tmpModified.__indexesValue, indexes)
                }

            override var documents: List<Document>
                get() = __ctx().toDraftList((__modified ?: __base!!).documents, Document::class.java, true)
                set(documents) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__documentsValue = NonSharedList.of(__tmpModified.__documentsValue, documents)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun states(): MutableList<DocStateDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_STATES))) {
                    states = emptyList()
                }
                return states as MutableList<DocStateDraft>
            }

            override fun cards(): MutableList<DocCardDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_CARDS))) {
                    cards = emptyList()
                }
                return cards as MutableList<DocCardDraft>
            }

            override fun indexes(): MutableList<DocIndexDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_INDEXES))) {
                    indexes = emptyList()
                }
                return indexes as MutableList<DocIndexDraft>
            }

            override fun documents(): MutableList<DocumentDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_DOCUMENTS))) {
                    documents = emptyList()
                }
                return documents as MutableList<DocumentDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_DOC_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    SLOT_DOC_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docNameValue = null
                    SLOT_DOC_DESC ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDescLoaded = false
                        }
                    SLOT_DOC_ICON ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docIconValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docIconLoaded = false
                        }
                    SLOT_DOC_COLOR ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docColorValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docColorLoaded = false
                        }
                    SLOT_ENABLED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    SLOT_SYSTEM_BUILT_IN ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    SLOT_DOC_CONFIG ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docConfigLoaded = false
                        }
                    SLOT_STATES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statesValue = null
                    SLOT_CARDS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardsValue = null
                    SLOT_INDEXES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexesValue = null
                    SLOT_DOCUMENTS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__documentsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "docType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    "docName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docNameValue = null
                    "docDesc" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docDescLoaded = false
                        }
                    "docIcon" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docIconValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docIconLoaded = false
                        }
                    "docColor" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docColorValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docColorLoaded = false
                        }
                    "enabled" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    "systemBuiltIn" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    "docConfig" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docConfigValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__docConfigLoaded = false
                        }
                    "states" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statesValue = null
                    "cards" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__cardsValue = null
                    "indexes" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexesValue = null
                    "documents" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__documentsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_DOC_TYPE ->
                    	this.docType = value as String?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    SLOT_DOC_NAME ->
                    	this.docName = value as String?
                    	?: throw IllegalArgumentException("'docName cannot be null")
                    SLOT_DOC_DESC ->
                    	this.docDesc = value as String?
                    SLOT_DOC_ICON ->
                    	this.docIcon = value as String?
                    SLOT_DOC_COLOR ->
                    	this.docColor = value as String?
                    SLOT_ENABLED ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    SLOT_SYSTEM_BUILT_IN ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    SLOT_DOC_CONFIG ->
                    	this.docConfig = value as String?
                    SLOT_STATES ->
                    	this.states = value as List<DocState>?
                    	?: throw IllegalArgumentException("'states cannot be null")
                    SLOT_CARDS ->
                    	this.cards = value as List<DocCard>?
                    	?: throw IllegalArgumentException("'cards cannot be null")
                    SLOT_INDEXES ->
                    	this.indexes = value as List<DocIndex>?
                    	?: throw IllegalArgumentException("'indexes cannot be null")
                    SLOT_DOCUMENTS ->
                    	this.documents = value as List<Document>?
                    	?: throw IllegalArgumentException("'documents cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "docType" ->
                    	this.docType = value as String?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    "docName" ->
                    	this.docName = value as String?
                    	?: throw IllegalArgumentException("'docName cannot be null")
                    "docDesc" ->
                    	this.docDesc = value as String?
                    "docIcon" ->
                    	this.docIcon = value as String?
                    "docColor" ->
                    	this.docColor = value as String?
                    "enabled" ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    "systemBuiltIn" ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    "docConfig" ->
                    	this.docConfig = value as String?
                    "states" ->
                    	this.states = value as List<DocState>?
                    	?: throw IllegalArgumentException("'states cannot be null")
                    "cards" ->
                    	this.cards = value as List<DocCard>?
                    	?: throw IllegalArgumentException("'cards cannot be null")
                    "indexes" ->
                    	this.indexes = value as List<DocIndex>?
                    	?: throw IllegalArgumentException("'indexes cannot be null")
                    "documents" ->
                    	this.documents = value as List<Document>?
                    	?: throw IllegalArgumentException("'documents cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocType\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(26).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_DOC_TYPE ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    SLOT_DOC_NAME ->
                    	__visibility.show(SLOT_DOC_NAME, visible)
                    SLOT_DOC_DESC ->
                    	__visibility.show(SLOT_DOC_DESC, visible)
                    SLOT_DOC_ICON ->
                    	__visibility.show(SLOT_DOC_ICON, visible)
                    SLOT_DOC_COLOR ->
                    	__visibility.show(SLOT_DOC_COLOR, visible)
                    SLOT_ENABLED ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    SLOT_DOC_CONFIG ->
                    	__visibility.show(SLOT_DOC_CONFIG, visible)
                    SLOT_STATES ->
                    	__visibility.show(SLOT_STATES, visible)
                    SLOT_CARDS ->
                    	__visibility.show(SLOT_CARDS, visible)
                    SLOT_INDEXES ->
                    	__visibility.show(SLOT_INDEXES, visible)
                    SLOT_DOCUMENTS ->
                    	__visibility.show(SLOT_DOCUMENTS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(26).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "docType" ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    "docName" ->
                    	__visibility.show(SLOT_DOC_NAME, visible)
                    "docDesc" ->
                    	__visibility.show(SLOT_DOC_DESC, visible)
                    "docIcon" ->
                    	__visibility.show(SLOT_DOC_ICON, visible)
                    "docColor" ->
                    	__visibility.show(SLOT_DOC_COLOR, visible)
                    "enabled" ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    "systemBuiltIn" ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    "docConfig" ->
                    	__visibility.show(SLOT_DOC_CONFIG, visible)
                    "states" ->
                    	__visibility.show(SLOT_STATES, visible)
                    "cards" ->
                    	__visibility.show(SLOT_CARDS, visible)
                    "indexes" ->
                    	__visibility.show(SLOT_INDEXES, visible)
                    "documents" ->
                    	__visibility.show(SLOT_DOCUMENTS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_STATES))) {
                            val oldValue = base!!.states
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_CARDS))) {
                            val oldValue = base!!.cards
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_INDEXES))) {
                            val oldValue = base!!.indexes
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_DOCUMENTS))) {
                            val oldValue = base!!.documents
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__statesValue = NonSharedList.of(__tmpModified.__statesValue, __ctx.resolveList(__tmpModified.__statesValue))
                        __tmpModified.__cardsValue = NonSharedList.of(__tmpModified.__cardsValue, __ctx.resolveList(__tmpModified.__cardsValue))
                        __tmpModified.__indexesValue = NonSharedList.of(__tmpModified.__indexesValue, __ctx.resolveList(__tmpModified.__indexesValue))
                        __tmpModified.__documentsValue = NonSharedList.of(__tmpModified.__documentsValue, __ctx.resolveList(__tmpModified.__documentsValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = DocType::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: DocType?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun docType(docType: String?): Builder {
            if (docType !== null) {
                __draft.docType = docType
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_TYPE), true)
            }
            return this
        }

        public fun docName(docName: String?): Builder {
            if (docName !== null) {
                __draft.docName = docName
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_NAME), true)
            }
            return this
        }

        public fun docDesc(docDesc: String?): Builder {
            __draft.docDesc = docDesc
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_DESC), true)
            return this
        }

        public fun docIcon(docIcon: String?): Builder {
            __draft.docIcon = docIcon
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_ICON), true)
            return this
        }

        public fun docColor(docColor: String?): Builder {
            __draft.docColor = docColor
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_COLOR), true)
            return this
        }

        public fun enabled(enabled: Boolean?): Builder {
            if (enabled !== null) {
                __draft.enabled = enabled
                __draft.__show(PropId.byIndex(`$`.SLOT_ENABLED), true)
            }
            return this
        }

        public fun systemBuiltIn(systemBuiltIn: Boolean?): Builder {
            if (systemBuiltIn !== null) {
                __draft.systemBuiltIn = systemBuiltIn
                __draft.__show(PropId.byIndex(`$`.SLOT_SYSTEM_BUILT_IN), true)
            }
            return this
        }

        public fun docConfig(docConfig: String?): Builder {
            __draft.docConfig = docConfig
            __draft.__show(PropId.byIndex(`$`.SLOT_DOC_CONFIG), true)
            return this
        }

        public fun states(states: List<DocState>?): Builder {
            if (states !== null) {
                __draft.states = states
                __draft.__show(PropId.byIndex(`$`.SLOT_STATES), true)
            }
            return this
        }

        public fun cards(cards: List<DocCard>?): Builder {
            if (cards !== null) {
                __draft.cards = cards
                __draft.__show(PropId.byIndex(`$`.SLOT_CARDS), true)
            }
            return this
        }

        public fun indexes(indexes: List<DocIndex>?): Builder {
            if (indexes !== null) {
                __draft.indexes = indexes
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEXES), true)
            }
            return this
        }

        public fun documents(documents: List<Document>?): Builder {
            if (documents !== null) {
                __draft.documents = documents
                __draft.__show(PropId.byIndex(`$`.SLOT_DOCUMENTS), true)
            }
            return this
        }

        public fun build(): DocType = __draft.__unwrap() as DocType
    }
}

@GeneratedBy(type = DocType::class)
public fun ImmutableCreator<DocType>.`by`(resolveImmediately: Boolean = false, block: DocTypeDraft.() -> Unit): DocType = DocTypeDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocType::class)
public fun ImmutableCreator<DocType>.`by`(base: DocType?, resolveImmediately: Boolean = false): DocType = DocTypeDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = DocType::class)
public fun ImmutableCreator<DocType>.`by`(
    base: DocType?,
    resolveImmediately: Boolean = false,
    block: DocTypeDraft.() -> Unit,
): DocType = DocTypeDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocType::class)
public fun DocType(resolveImmediately: Boolean = false, block: DocTypeDraft.() -> Unit): DocType = DocTypeDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocType::class)
public fun DocType(
    base: DocType?,
    resolveImmediately: Boolean = false,
    block: DocTypeDraft.() -> Unit,
): DocType = DocTypeDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocType::class)
public fun MutableList<DocTypeDraft>.addBy(resolveImmediately: Boolean = false, block: DocTypeDraft.() -> Unit): MutableList<DocTypeDraft> {
    add(DocTypeDraft.`$`.produce(null, resolveImmediately, block) as DocTypeDraft)
    return this
}

@GeneratedBy(type = DocType::class)
public fun MutableList<DocTypeDraft>.addBy(base: DocType?, resolveImmediately: Boolean = false): MutableList<DocTypeDraft> {
    add(DocTypeDraft.`$`.produce(base, resolveImmediately) as DocTypeDraft)
    return this
}

@GeneratedBy(type = DocType::class)
public fun MutableList<DocTypeDraft>.addBy(
    base: DocType?,
    resolveImmediately: Boolean = false,
    block: DocTypeDraft.() -> Unit,
): MutableList<DocTypeDraft> {
    add(DocTypeDraft.`$`.produce(base, resolveImmediately, block) as DocTypeDraft)
    return this
}

@GeneratedBy(type = DocType::class)
public fun DocType.copy(resolveImmediately: Boolean = false, block: DocTypeDraft.() -> Unit): DocType = DocTypeDraft.`$`.produce(this, resolveImmediately, block)
