@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderEnum::class)

package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.enums.InvoiceType
import cn.nkpro.elcube.jimmer.enums.PaymentStatus
import cn.nkpro.elcube.jimmer.enums.Priority
import cn.nkpro.elcube.jimmer.enums.SalesOrderStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<SalesOrderEnum>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderEnum>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderEnum>.createTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.CREATE_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderEnum>.createTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.CREATE_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<SalesOrderEnum>.updateTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.UPDATE_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<SalesOrderEnum>.createBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.CREATE_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderEnum>.updateBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.UPDATE_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderEnum>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Boolean>(SalesOrderEnumProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<SalesOrderEnum>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Boolean>(SalesOrderEnumProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<SalesOrderEnum>.version: KNonNullPropExpression<Long>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Long>(SalesOrderEnumProps.VERSION.unwrap()) as KNonNullPropExpression<Long>

public val KNullableProps<SalesOrderEnum>.version: KNullablePropExpression<Long>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Long>(SalesOrderEnumProps.VERSION.unwrap()) as KNullablePropExpression<Long>

public val KNonNullProps<SalesOrderEnum>.orderNo: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.ORDER_NO.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderEnum>.orderNo: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.ORDER_NO.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderEnum>.customerName: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.CUSTOMER_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderEnum>.customerName: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.CUSTOMER_NAME.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderEnum>.totalAmount: KNonNullPropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<BigDecimal>(SalesOrderEnumProps.TOTAL_AMOUNT.unwrap()) as KNonNullPropExpression<BigDecimal>

public val KNullableProps<SalesOrderEnum>.totalAmount: KNullablePropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<BigDecimal>(SalesOrderEnumProps.TOTAL_AMOUNT.unwrap()) as KNullablePropExpression<BigDecimal>

public val KNonNullProps<SalesOrderEnum>.orderDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.ORDER_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderEnum>.orderDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.ORDER_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderEnum>.deliveryDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.DELIVERY_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderEnum>.deliveryDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<LocalDateTime>(SalesOrderEnumProps.DELIVERY_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderEnum>.status: KNonNullPropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<SalesOrderStatus>(SalesOrderEnumProps.STATUS.unwrap()) as KNonNullPropExpression<SalesOrderStatus>

public val KNullableProps<SalesOrderEnum>.status: KNullablePropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<SalesOrderStatus>(SalesOrderEnumProps.STATUS.unwrap()) as KNullablePropExpression<SalesOrderStatus>

public val KNonNullProps<SalesOrderEnum>.priority: KNonNullPropExpression<Priority>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Priority>(SalesOrderEnumProps.PRIORITY.unwrap()) as KNonNullPropExpression<Priority>

public val KNullableProps<SalesOrderEnum>.priority: KNullablePropExpression<Priority>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<Priority>(SalesOrderEnumProps.PRIORITY.unwrap()) as KNullablePropExpression<Priority>

public val KNonNullProps<SalesOrderEnum>.paymentStatus: KNonNullPropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<PaymentStatus>(SalesOrderEnumProps.PAYMENT_STATUS.unwrap()) as KNonNullPropExpression<PaymentStatus>

public val KNullableProps<SalesOrderEnum>.paymentStatus: KNullablePropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<PaymentStatus>(SalesOrderEnumProps.PAYMENT_STATUS.unwrap()) as KNullablePropExpression<PaymentStatus>

public val KProps<SalesOrderEnum>.invoiceType: KNullablePropExpression<InvoiceType>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<InvoiceType>(SalesOrderEnumProps.INVOICE_TYPE.unwrap()) as KNullablePropExpression<InvoiceType>

public val KProps<SalesOrderEnum>.remark: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = get<String>(SalesOrderEnumProps.REMARK.unwrap()) as KNullablePropExpression<String>

public val KRemoteRef.NonNull<SalesOrderEnum>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<SalesOrderEnum>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderEnum::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = SalesOrderEnum::class)
public fun KNonNullTable<SalesOrderEnum>.fetchBy(block: SalesOrderEnumFetcherDsl.() -> Unit): Selection<SalesOrderEnum> = fetch(newFetcher(SalesOrderEnum::class).`by`(block))

@GeneratedBy(type = SalesOrderEnum::class)
public fun KNullableTable<SalesOrderEnum>.fetchBy(block: SalesOrderEnumFetcherDsl.() -> Unit): Selection<SalesOrderEnum?> = fetch(newFetcher(SalesOrderEnum::class).`by`(block))

@GeneratedBy(type = SalesOrderEnum::class)
public object SalesOrderEnumProps {
    public val ID: TypedProp.Scalar<SalesOrderEnum, String> =
            TypedProp.scalar(SalesOrderEnum::id.toImmutableProp())

    public val CREATE_TIME: TypedProp.Scalar<SalesOrderEnum, LocalDateTime> =
            TypedProp.scalar(SalesOrderEnum::createTime.toImmutableProp())

    public val UPDATE_TIME: TypedProp.Scalar<SalesOrderEnum, LocalDateTime?> =
            TypedProp.scalar(SalesOrderEnum::updateTime.toImmutableProp())

    public val CREATE_BY: TypedProp.Scalar<SalesOrderEnum, String?> =
            TypedProp.scalar(SalesOrderEnum::createBy.toImmutableProp())

    public val UPDATE_BY: TypedProp.Scalar<SalesOrderEnum, String?> =
            TypedProp.scalar(SalesOrderEnum::updateBy.toImmutableProp())

    public val DELETED: TypedProp.Scalar<SalesOrderEnum, Boolean> =
            TypedProp.scalar(SalesOrderEnum::deleted.toImmutableProp())

    public val VERSION: TypedProp.Scalar<SalesOrderEnum, Long> =
            TypedProp.scalar(SalesOrderEnum::version.toImmutableProp())

    public val ORDER_NO: TypedProp.Scalar<SalesOrderEnum, String> =
            TypedProp.scalar(SalesOrderEnum::orderNo.toImmutableProp())

    public val CUSTOMER_NAME: TypedProp.Scalar<SalesOrderEnum, String> =
            TypedProp.scalar(SalesOrderEnum::customerName.toImmutableProp())

    public val TOTAL_AMOUNT: TypedProp.Scalar<SalesOrderEnum, BigDecimal> =
            TypedProp.scalar(SalesOrderEnum::totalAmount.toImmutableProp())

    public val ORDER_DATE: TypedProp.Scalar<SalesOrderEnum, LocalDateTime> =
            TypedProp.scalar(SalesOrderEnum::orderDate.toImmutableProp())

    public val DELIVERY_DATE: TypedProp.Scalar<SalesOrderEnum, LocalDateTime> =
            TypedProp.scalar(SalesOrderEnum::deliveryDate.toImmutableProp())

    public val STATUS: TypedProp.Scalar<SalesOrderEnum, SalesOrderStatus> =
            TypedProp.scalar(SalesOrderEnum::status.toImmutableProp())

    public val PRIORITY: TypedProp.Scalar<SalesOrderEnum, Priority> =
            TypedProp.scalar(SalesOrderEnum::priority.toImmutableProp())

    public val PAYMENT_STATUS: TypedProp.Scalar<SalesOrderEnum, PaymentStatus> =
            TypedProp.scalar(SalesOrderEnum::paymentStatus.toImmutableProp())

    public val INVOICE_TYPE: TypedProp.Scalar<SalesOrderEnum, InvoiceType?> =
            TypedProp.scalar(SalesOrderEnum::invoiceType.toImmutableProp())

    public val REMARK: TypedProp.Scalar<SalesOrderEnum, String?> =
            TypedProp.scalar(SalesOrderEnum::remark.toImmutableProp())
}
