@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = Component::class)
public interface ComponentDraft : Component, BusinessEntityDraft {
    override var componentCode: String

    override var componentName: String

    override var componentType: String

    override var componentCategory: String?

    override var componentVersion: String

    override var componentDesc: String?

    override var componentIcon: String?

    override var systemBuiltIn: Boolean

    override var enabled: Boolean

    override var groovyScript: String?

    override var vueMain: String?

    override var vueDefs: String?

    override var configSchema: String?

    override var dependencies: List<ComponentDependency>

    override var versions: List<ComponentVersion>

    public fun dependencies(): MutableList<ComponentDependencyDraft>

    public fun versions(): MutableList<ComponentVersionDraft>

    @GeneratedBy(type = Component::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_COMPONENT_CODE: Int = 14

        public const val SLOT_COMPONENT_NAME: Int = 15

        public const val SLOT_COMPONENT_TYPE: Int = 16

        public const val SLOT_COMPONENT_CATEGORY: Int = 17

        public const val SLOT_COMPONENT_VERSION: Int = 18

        public const val SLOT_COMPONENT_DESC: Int = 19

        public const val SLOT_COMPONENT_ICON: Int = 20

        public const val SLOT_SYSTEM_BUILT_IN: Int = 21

        public const val SLOT_ENABLED: Int = 22

        public const val SLOT_GROOVY_SCRIPT: Int = 23

        public const val SLOT_VUE_MAIN: Int = 24

        public const val SLOT_VUE_DEFS: Int = 25

        public const val SLOT_CONFIG_SCHEMA: Int = 26

        public const val SLOT_DEPENDENCIES: Int = 27

        public const val SLOT_VERSIONS: Int = 28

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                Component::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as Component?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_COMPONENT_CODE, "componentCode", String::class.java, false)
            .add(SLOT_COMPONENT_NAME, "componentName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_COMPONENT_TYPE, "componentType", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_COMPONENT_CATEGORY, "componentCategory", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_COMPONENT_VERSION, "componentVersion", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_COMPONENT_DESC, "componentDesc", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_COMPONENT_ICON, "componentIcon", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_SYSTEM_BUILT_IN, "systemBuiltIn", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_ENABLED, "enabled", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_GROOVY_SCRIPT, "groovyScript", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_VUE_MAIN, "vueMain", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_VUE_DEFS, "vueDefs", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_CONFIG_SCHEMA, "configSchema", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_DEPENDENCIES, "dependencies", OneToMany::class.java, ComponentDependency::class.java, false)
            .add(SLOT_VERSIONS, "versions", OneToMany::class.java, ComponentVersion::class.java, false)
            .build()

        public fun produce(base: Component? = null, resolveImmediately: Boolean = false): Component {
            val consumer = DraftConsumer<ComponentDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as Component
        }

        public fun produce(
            base: Component? = null,
            resolveImmediately: Boolean = false,
            block: ComponentDraft.() -> Unit,
        ): Component {
            val consumer = DraftConsumer<ComponentDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as Component
        }

        @GeneratedBy(type = Component::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "componentCode", "componentName", "componentType", "componentCategory", "componentVersion", "componentDesc", "componentIcon", "systemBuiltIn", "enabled", "groovyScript", "vueMain", "vueDefs", "configSchema", "dependencies", "versions")
        private abstract interface Implementor : Component, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_COMPONENT_CODE ->
                	componentCode
                SLOT_COMPONENT_NAME ->
                	componentName
                SLOT_COMPONENT_TYPE ->
                	componentType
                SLOT_COMPONENT_CATEGORY ->
                	componentCategory
                SLOT_COMPONENT_VERSION ->
                	componentVersion
                SLOT_COMPONENT_DESC ->
                	componentDesc
                SLOT_COMPONENT_ICON ->
                	componentIcon
                SLOT_SYSTEM_BUILT_IN ->
                	systemBuiltIn
                SLOT_ENABLED ->
                	enabled
                SLOT_GROOVY_SCRIPT ->
                	groovyScript
                SLOT_VUE_MAIN ->
                	vueMain
                SLOT_VUE_DEFS ->
                	vueDefs
                SLOT_CONFIG_SCHEMA ->
                	configSchema
                SLOT_DEPENDENCIES ->
                	dependencies
                SLOT_VERSIONS ->
                	versions
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "componentCode" ->
                	componentCode
                "componentName" ->
                	componentName
                "componentType" ->
                	componentType
                "componentCategory" ->
                	componentCategory
                "componentVersion" ->
                	componentVersion
                "componentDesc" ->
                	componentDesc
                "componentIcon" ->
                	componentIcon
                "systemBuiltIn" ->
                	systemBuiltIn
                "enabled" ->
                	enabled
                "groovyScript" ->
                	groovyScript
                "vueMain" ->
                	vueMain
                "vueDefs" ->
                	vueDefs
                "configSchema" ->
                	configSchema
                "dependencies" ->
                	dependencies
                "versions" ->
                	versions
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = Component::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __componentCodeValue: String? = null

            @get:JsonIgnore
            internal var __componentNameValue: String? = null

            @get:JsonIgnore
            internal var __componentTypeValue: String? = null

            @get:JsonIgnore
            internal var __componentCategoryValue: String? = null

            @get:JsonIgnore
            internal var __componentCategoryLoaded: Boolean = false

            @get:JsonIgnore
            internal var __componentVersionValue: String? = null

            @get:JsonIgnore
            internal var __componentDescValue: String? = null

            @get:JsonIgnore
            internal var __componentDescLoaded: Boolean = false

            @get:JsonIgnore
            internal var __componentIconValue: String? = null

            @get:JsonIgnore
            internal var __componentIconLoaded: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInValue: Boolean = false

            @get:JsonIgnore
            internal var __systemBuiltInLoaded: Boolean = false

            @get:JsonIgnore
            internal var __enabledValue: Boolean = false

            @get:JsonIgnore
            internal var __enabledLoaded: Boolean = false

            @get:JsonIgnore
            internal var __groovyScriptValue: String? = null

            @get:JsonIgnore
            internal var __groovyScriptLoaded: Boolean = false

            @get:JsonIgnore
            internal var __vueMainValue: String? = null

            @get:JsonIgnore
            internal var __vueMainLoaded: Boolean = false

            @get:JsonIgnore
            internal var __vueDefsValue: String? = null

            @get:JsonIgnore
            internal var __vueDefsLoaded: Boolean = false

            @get:JsonIgnore
            internal var __configSchemaValue: String? = null

            @get:JsonIgnore
            internal var __configSchemaLoaded: Boolean = false

            @get:JsonIgnore
            internal var __dependenciesValue: NonSharedList<ComponentDependency>? = null

            @get:JsonIgnore
            internal var __versionsValue: NonSharedList<ComponentVersion>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(Component::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(Component::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(Component::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(Component::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(Component::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(Component::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(Component::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(Component::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(Component::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(Component::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(Component::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(Component::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(Component::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(Component::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val componentCode: String
                get() {
                    val __componentCodeValue = this.__componentCodeValue
                    if (__componentCodeValue === null) {
                        throw UnloadedException(Component::class.java, "componentCode")
                    }
                    return __componentCodeValue
                }

            override val componentName: String
                get() {
                    val __componentNameValue = this.__componentNameValue
                    if (__componentNameValue === null) {
                        throw UnloadedException(Component::class.java, "componentName")
                    }
                    return __componentNameValue
                }

            override val componentType: String
                get() {
                    val __componentTypeValue = this.__componentTypeValue
                    if (__componentTypeValue === null) {
                        throw UnloadedException(Component::class.java, "componentType")
                    }
                    return __componentTypeValue
                }

            override val componentCategory: String?
                get() {
                    if (!__componentCategoryLoaded) {
                        throw UnloadedException(Component::class.java, "componentCategory")
                    }
                    return __componentCategoryValue
                }

            override val componentVersion: String
                get() {
                    val __componentVersionValue = this.__componentVersionValue
                    if (__componentVersionValue === null) {
                        throw UnloadedException(Component::class.java, "componentVersion")
                    }
                    return __componentVersionValue
                }

            override val componentDesc: String?
                get() {
                    if (!__componentDescLoaded) {
                        throw UnloadedException(Component::class.java, "componentDesc")
                    }
                    return __componentDescValue
                }

            override val componentIcon: String?
                get() {
                    if (!__componentIconLoaded) {
                        throw UnloadedException(Component::class.java, "componentIcon")
                    }
                    return __componentIconValue
                }

            override val systemBuiltIn: Boolean
                get() {
                    if (!__systemBuiltInLoaded) {
                        throw UnloadedException(Component::class.java, "systemBuiltIn")
                    }
                    return __systemBuiltInValue
                }

            override val enabled: Boolean
                get() {
                    if (!__enabledLoaded) {
                        throw UnloadedException(Component::class.java, "enabled")
                    }
                    return __enabledValue
                }

            override val groovyScript: String?
                get() {
                    if (!__groovyScriptLoaded) {
                        throw UnloadedException(Component::class.java, "groovyScript")
                    }
                    return __groovyScriptValue
                }

            override val vueMain: String?
                get() {
                    if (!__vueMainLoaded) {
                        throw UnloadedException(Component::class.java, "vueMain")
                    }
                    return __vueMainValue
                }

            override val vueDefs: String?
                get() {
                    if (!__vueDefsLoaded) {
                        throw UnloadedException(Component::class.java, "vueDefs")
                    }
                    return __vueDefsValue
                }

            override val configSchema: String?
                get() {
                    if (!__configSchemaLoaded) {
                        throw UnloadedException(Component::class.java, "configSchema")
                    }
                    return __configSchemaValue
                }

            override val dependencies: List<ComponentDependency>
                get() {
                    val __dependenciesValue = this.__dependenciesValue
                    if (__dependenciesValue === null) {
                        throw UnloadedException(Component::class.java, "dependencies")
                    }
                    return __dependenciesValue
                }

            override val versions: List<ComponentVersion>
                get() {
                    val __versionsValue = this.__versionsValue
                    if (__versionsValue === null) {
                        throw UnloadedException(Component::class.java, "versions")
                    }
                    return __versionsValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_COMPONENT_CODE ->
                	__componentCodeValue !== null
                SLOT_COMPONENT_NAME ->
                	__componentNameValue !== null
                SLOT_COMPONENT_TYPE ->
                	__componentTypeValue !== null
                SLOT_COMPONENT_CATEGORY ->
                	__componentCategoryLoaded
                SLOT_COMPONENT_VERSION ->
                	__componentVersionValue !== null
                SLOT_COMPONENT_DESC ->
                	__componentDescLoaded
                SLOT_COMPONENT_ICON ->
                	__componentIconLoaded
                SLOT_SYSTEM_BUILT_IN ->
                	__systemBuiltInLoaded
                SLOT_ENABLED ->
                	__enabledLoaded
                SLOT_GROOVY_SCRIPT ->
                	__groovyScriptLoaded
                SLOT_VUE_MAIN ->
                	__vueMainLoaded
                SLOT_VUE_DEFS ->
                	__vueDefsLoaded
                SLOT_CONFIG_SCHEMA ->
                	__configSchemaLoaded
                SLOT_DEPENDENCIES ->
                	__dependenciesValue !== null
                SLOT_VERSIONS ->
                	__versionsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "componentCode" ->
                	__componentCodeValue !== null
                "componentName" ->
                	__componentNameValue !== null
                "componentType" ->
                	__componentTypeValue !== null
                "componentCategory" ->
                	__componentCategoryLoaded
                "componentVersion" ->
                	__componentVersionValue !== null
                "componentDesc" ->
                	__componentDescLoaded
                "componentIcon" ->
                	__componentIconLoaded
                "systemBuiltIn" ->
                	__systemBuiltInLoaded
                "enabled" ->
                	__enabledLoaded
                "groovyScript" ->
                	__groovyScriptLoaded
                "vueMain" ->
                	__vueMainLoaded
                "vueDefs" ->
                	__vueDefsLoaded
                "configSchema" ->
                	__configSchemaLoaded
                "dependencies" ->
                	__dependenciesValue !== null
                "versions" ->
                	__versionsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_COMPONENT_CODE ->
                    	__visibility.visible(SLOT_COMPONENT_CODE)
                    SLOT_COMPONENT_NAME ->
                    	__visibility.visible(SLOT_COMPONENT_NAME)
                    SLOT_COMPONENT_TYPE ->
                    	__visibility.visible(SLOT_COMPONENT_TYPE)
                    SLOT_COMPONENT_CATEGORY ->
                    	__visibility.visible(SLOT_COMPONENT_CATEGORY)
                    SLOT_COMPONENT_VERSION ->
                    	__visibility.visible(SLOT_COMPONENT_VERSION)
                    SLOT_COMPONENT_DESC ->
                    	__visibility.visible(SLOT_COMPONENT_DESC)
                    SLOT_COMPONENT_ICON ->
                    	__visibility.visible(SLOT_COMPONENT_ICON)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    SLOT_ENABLED ->
                    	__visibility.visible(SLOT_ENABLED)
                    SLOT_GROOVY_SCRIPT ->
                    	__visibility.visible(SLOT_GROOVY_SCRIPT)
                    SLOT_VUE_MAIN ->
                    	__visibility.visible(SLOT_VUE_MAIN)
                    SLOT_VUE_DEFS ->
                    	__visibility.visible(SLOT_VUE_DEFS)
                    SLOT_CONFIG_SCHEMA ->
                    	__visibility.visible(SLOT_CONFIG_SCHEMA)
                    SLOT_DEPENDENCIES ->
                    	__visibility.visible(SLOT_DEPENDENCIES)
                    SLOT_VERSIONS ->
                    	__visibility.visible(SLOT_VERSIONS)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "componentCode" ->
                    	__visibility.visible(SLOT_COMPONENT_CODE)
                    "componentName" ->
                    	__visibility.visible(SLOT_COMPONENT_NAME)
                    "componentType" ->
                    	__visibility.visible(SLOT_COMPONENT_TYPE)
                    "componentCategory" ->
                    	__visibility.visible(SLOT_COMPONENT_CATEGORY)
                    "componentVersion" ->
                    	__visibility.visible(SLOT_COMPONENT_VERSION)
                    "componentDesc" ->
                    	__visibility.visible(SLOT_COMPONENT_DESC)
                    "componentIcon" ->
                    	__visibility.visible(SLOT_COMPONENT_ICON)
                    "systemBuiltIn" ->
                    	__visibility.visible(SLOT_SYSTEM_BUILT_IN)
                    "enabled" ->
                    	__visibility.visible(SLOT_ENABLED)
                    "groovyScript" ->
                    	__visibility.visible(SLOT_GROOVY_SCRIPT)
                    "vueMain" ->
                    	__visibility.visible(SLOT_VUE_MAIN)
                    "vueDefs" ->
                    	__visibility.visible(SLOT_VUE_DEFS)
                    "configSchema" ->
                    	__visibility.visible(SLOT_CONFIG_SCHEMA)
                    "dependencies" ->
                    	__visibility.visible(SLOT_DEPENDENCIES)
                    "versions" ->
                    	__visibility.visible(SLOT_VERSIONS)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__componentCodeValue !== null) {
                    hash = 31 * hash + __componentCodeValue.hashCode()
                }
                if (__componentNameValue !== null) {
                    hash = 31 * hash + __componentNameValue.hashCode()
                }
                if (__componentTypeValue !== null) {
                    hash = 31 * hash + __componentTypeValue.hashCode()
                }
                if (__componentCategoryLoaded) {
                    hash = 31 * hash + (__componentCategoryValue?.hashCode() ?: 0)
                }
                if (__componentVersionValue !== null) {
                    hash = 31 * hash + __componentVersionValue.hashCode()
                }
                if (__componentDescLoaded) {
                    hash = 31 * hash + (__componentDescValue?.hashCode() ?: 0)
                }
                if (__componentIconLoaded) {
                    hash = 31 * hash + (__componentIconValue?.hashCode() ?: 0)
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__groovyScriptLoaded) {
                    hash = 31 * hash + (__groovyScriptValue?.hashCode() ?: 0)
                }
                if (__vueMainLoaded) {
                    hash = 31 * hash + (__vueMainValue?.hashCode() ?: 0)
                }
                if (__vueDefsLoaded) {
                    hash = 31 * hash + (__vueDefsValue?.hashCode() ?: 0)
                }
                if (__configSchemaLoaded) {
                    hash = 31 * hash + (__configSchemaValue?.hashCode() ?: 0)
                }
                if (__dependenciesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__dependenciesValue)
                }
                if (__versionsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__versionsValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__componentCodeValue !== null) {
                    hash = 31 * hash + __componentCodeValue.hashCode()
                }
                if (__componentNameValue !== null) {
                    hash = 31 * hash + __componentNameValue.hashCode()
                }
                if (__componentTypeValue !== null) {
                    hash = 31 * hash + __componentTypeValue.hashCode()
                }
                if (__componentCategoryLoaded) {
                    hash = 31 * hash + (__componentCategoryValue?.hashCode() ?: 0)
                }
                if (__componentVersionValue !== null) {
                    hash = 31 * hash + __componentVersionValue.hashCode()
                }
                if (__componentDescLoaded) {
                    hash = 31 * hash + (__componentDescValue?.hashCode() ?: 0)
                }
                if (__componentIconLoaded) {
                    hash = 31 * hash + (__componentIconValue?.hashCode() ?: 0)
                }
                if (__systemBuiltInLoaded) {
                    hash = 31 * hash + __systemBuiltInValue.hashCode()
                }
                if (__enabledLoaded) {
                    hash = 31 * hash + __enabledValue.hashCode()
                }
                if (__groovyScriptLoaded) {
                    hash = 31 * hash + (__groovyScriptValue?.hashCode() ?: 0)
                }
                if (__vueMainLoaded) {
                    hash = 31 * hash + (__vueMainValue?.hashCode() ?: 0)
                }
                if (__vueDefsLoaded) {
                    hash = 31 * hash + (__vueDefsValue?.hashCode() ?: 0)
                }
                if (__configSchemaLoaded) {
                    hash = 31 * hash + (__configSchemaValue?.hashCode() ?: 0)
                }
                if (__dependenciesValue !== null) {
                    hash = 31 * hash + __dependenciesValue.hashCode()
                }
                if (__versionsValue !== null) {
                    hash = 31 * hash + __versionsValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_CODE))) {
                    return false
                }
                val __componentCodeLoaded = 
                    this.__componentCodeValue !== null
                if (__componentCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_CODE)))) {
                    return false
                }
                if (__componentCodeLoaded && this.__componentCodeValue != __other.componentCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME))) {
                    return false
                }
                val __componentNameLoaded = 
                    this.__componentNameValue !== null
                if (__componentNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_NAME)))) {
                    return false
                }
                if (__componentNameLoaded && this.__componentNameValue != __other.componentName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_TYPE))) {
                    return false
                }
                val __componentTypeLoaded = 
                    this.__componentTypeValue !== null
                if (__componentTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_TYPE)))) {
                    return false
                }
                if (__componentTypeLoaded && this.__componentTypeValue != __other.componentType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_CATEGORY)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_CATEGORY))) {
                    return false
                }
                val __componentCategoryLoaded = 
                    this.__componentCategoryLoaded
                if (__componentCategoryLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_CATEGORY)))) {
                    return false
                }
                if (__componentCategoryLoaded && this.__componentCategoryValue != __other.componentCategory) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_VERSION))) {
                    return false
                }
                val __componentVersionLoaded = 
                    this.__componentVersionValue !== null
                if (__componentVersionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_VERSION)))) {
                    return false
                }
                if (__componentVersionLoaded && this.__componentVersionValue != __other.componentVersion) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_DESC))) {
                    return false
                }
                val __componentDescLoaded = 
                    this.__componentDescLoaded
                if (__componentDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_DESC)))) {
                    return false
                }
                if (__componentDescLoaded && this.__componentDescValue != __other.componentDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_ICON)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_ICON))) {
                    return false
                }
                val __componentIconLoaded = 
                    this.__componentIconLoaded
                if (__componentIconLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_ICON)))) {
                    return false
                }
                if (__componentIconLoaded && this.__componentIconValue != __other.componentIcon) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_GROOVY_SCRIPT)) != __other.__isVisible(PropId.byIndex(SLOT_GROOVY_SCRIPT))) {
                    return false
                }
                val __groovyScriptLoaded = 
                    this.__groovyScriptLoaded
                if (__groovyScriptLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_GROOVY_SCRIPT)))) {
                    return false
                }
                if (__groovyScriptLoaded && this.__groovyScriptValue != __other.groovyScript) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VUE_MAIN)) != __other.__isVisible(PropId.byIndex(SLOT_VUE_MAIN))) {
                    return false
                }
                val __vueMainLoaded = 
                    this.__vueMainLoaded
                if (__vueMainLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VUE_MAIN)))) {
                    return false
                }
                if (__vueMainLoaded && this.__vueMainValue != __other.vueMain) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VUE_DEFS)) != __other.__isVisible(PropId.byIndex(SLOT_VUE_DEFS))) {
                    return false
                }
                val __vueDefsLoaded = 
                    this.__vueDefsLoaded
                if (__vueDefsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VUE_DEFS)))) {
                    return false
                }
                if (__vueDefsLoaded && this.__vueDefsValue != __other.vueDefs) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CONFIG_SCHEMA)) != __other.__isVisible(PropId.byIndex(SLOT_CONFIG_SCHEMA))) {
                    return false
                }
                val __configSchemaLoaded = 
                    this.__configSchemaLoaded
                if (__configSchemaLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CONFIG_SCHEMA)))) {
                    return false
                }
                if (__configSchemaLoaded && this.__configSchemaValue != __other.configSchema) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPENDENCIES)) != __other.__isVisible(PropId.byIndex(SLOT_DEPENDENCIES))) {
                    return false
                }
                val __dependenciesLoaded = 
                    this.__dependenciesValue !== null
                if (__dependenciesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPENDENCIES)))) {
                    return false
                }
                if (__dependenciesLoaded && this.__dependenciesValue !== __other.dependencies) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSIONS)) != __other.__isVisible(PropId.byIndex(SLOT_VERSIONS))) {
                    return false
                }
                val __versionsLoaded = 
                    this.__versionsValue !== null
                if (__versionsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSIONS)))) {
                    return false
                }
                if (__versionsLoaded && this.__versionsValue !== __other.versions) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_CODE))) {
                    return false
                }
                val __componentCodeLoaded = 
                    this.__componentCodeValue !== null
                if (__componentCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_CODE)))) {
                    return false
                }
                if (__componentCodeLoaded && this.__componentCodeValue != __other.componentCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_NAME))) {
                    return false
                }
                val __componentNameLoaded = 
                    this.__componentNameValue !== null
                if (__componentNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_NAME)))) {
                    return false
                }
                if (__componentNameLoaded && this.__componentNameValue != __other.componentName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_TYPE))) {
                    return false
                }
                val __componentTypeLoaded = 
                    this.__componentTypeValue !== null
                if (__componentTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_TYPE)))) {
                    return false
                }
                if (__componentTypeLoaded && this.__componentTypeValue != __other.componentType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_CATEGORY)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_CATEGORY))) {
                    return false
                }
                val __componentCategoryLoaded = 
                    this.__componentCategoryLoaded
                if (__componentCategoryLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_CATEGORY)))) {
                    return false
                }
                if (__componentCategoryLoaded && this.__componentCategoryValue != __other.componentCategory) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_VERSION))) {
                    return false
                }
                val __componentVersionLoaded = 
                    this.__componentVersionValue !== null
                if (__componentVersionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_VERSION)))) {
                    return false
                }
                if (__componentVersionLoaded && this.__componentVersionValue != __other.componentVersion) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_DESC)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_DESC))) {
                    return false
                }
                val __componentDescLoaded = 
                    this.__componentDescLoaded
                if (__componentDescLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_DESC)))) {
                    return false
                }
                if (__componentDescLoaded && this.__componentDescValue != __other.componentDesc) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_COMPONENT_ICON)) != __other.__isVisible(PropId.byIndex(SLOT_COMPONENT_ICON))) {
                    return false
                }
                val __componentIconLoaded = 
                    this.__componentIconLoaded
                if (__componentIconLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_COMPONENT_ICON)))) {
                    return false
                }
                if (__componentIconLoaded && this.__componentIconValue != __other.componentIcon) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)) != __other.__isVisible(PropId.byIndex(SLOT_SYSTEM_BUILT_IN))) {
                    return false
                }
                val __systemBuiltInLoaded = 
                    this.__systemBuiltInLoaded
                if (__systemBuiltInLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SYSTEM_BUILT_IN)))) {
                    return false
                }
                if (__systemBuiltInLoaded && this.__systemBuiltInValue != __other.systemBuiltIn) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ENABLED)) != __other.__isVisible(PropId.byIndex(SLOT_ENABLED))) {
                    return false
                }
                val __enabledLoaded = 
                    this.__enabledLoaded
                if (__enabledLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ENABLED)))) {
                    return false
                }
                if (__enabledLoaded && this.__enabledValue != __other.enabled) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_GROOVY_SCRIPT)) != __other.__isVisible(PropId.byIndex(SLOT_GROOVY_SCRIPT))) {
                    return false
                }
                val __groovyScriptLoaded = 
                    this.__groovyScriptLoaded
                if (__groovyScriptLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_GROOVY_SCRIPT)))) {
                    return false
                }
                if (__groovyScriptLoaded && this.__groovyScriptValue != __other.groovyScript) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VUE_MAIN)) != __other.__isVisible(PropId.byIndex(SLOT_VUE_MAIN))) {
                    return false
                }
                val __vueMainLoaded = 
                    this.__vueMainLoaded
                if (__vueMainLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VUE_MAIN)))) {
                    return false
                }
                if (__vueMainLoaded && this.__vueMainValue != __other.vueMain) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VUE_DEFS)) != __other.__isVisible(PropId.byIndex(SLOT_VUE_DEFS))) {
                    return false
                }
                val __vueDefsLoaded = 
                    this.__vueDefsLoaded
                if (__vueDefsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VUE_DEFS)))) {
                    return false
                }
                if (__vueDefsLoaded && this.__vueDefsValue != __other.vueDefs) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CONFIG_SCHEMA)) != __other.__isVisible(PropId.byIndex(SLOT_CONFIG_SCHEMA))) {
                    return false
                }
                val __configSchemaLoaded = 
                    this.__configSchemaLoaded
                if (__configSchemaLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CONFIG_SCHEMA)))) {
                    return false
                }
                if (__configSchemaLoaded && this.__configSchemaValue != __other.configSchema) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPENDENCIES)) != __other.__isVisible(PropId.byIndex(SLOT_DEPENDENCIES))) {
                    return false
                }
                val __dependenciesLoaded = 
                    this.__dependenciesValue !== null
                if (__dependenciesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPENDENCIES)))) {
                    return false
                }
                if (__dependenciesLoaded && this.__dependenciesValue != __other.dependencies) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSIONS)) != __other.__isVisible(PropId.byIndex(SLOT_VERSIONS))) {
                    return false
                }
                val __versionsLoaded = 
                    this.__versionsValue !== null
                if (__versionsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSIONS)))) {
                    return false
                }
                if (__versionsLoaded && this.__versionsValue != __other.versions) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = Component::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: Component?,
        ) : Implementor,
            ComponentDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: Component? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var componentCode: String
                get() = (__modified ?: __base!!).componentCode
                set(componentCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentCodeValue = componentCode
                }

            override var componentName: String
                get() = (__modified ?: __base!!).componentName
                set(componentName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentNameValue = componentName
                }

            override var componentType: String
                get() = (__modified ?: __base!!).componentType
                set(componentType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentTypeValue = componentType
                }

            override var componentCategory: String?
                get() = (__modified ?: __base!!).componentCategory
                set(componentCategory) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentCategoryValue = componentCategory
                    __tmpModified.__componentCategoryLoaded = true
                }

            override var componentVersion: String
                get() = (__modified ?: __base!!).componentVersion
                set(componentVersion) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentVersionValue = componentVersion
                }

            override var componentDesc: String?
                get() = (__modified ?: __base!!).componentDesc
                set(componentDesc) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentDescValue = componentDesc
                    __tmpModified.__componentDescLoaded = true
                }

            override var componentIcon: String?
                get() = (__modified ?: __base!!).componentIcon
                set(componentIcon) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__componentIconValue = componentIcon
                    __tmpModified.__componentIconLoaded = true
                }

            override var systemBuiltIn: Boolean
                get() = (__modified ?: __base!!).systemBuiltIn
                set(systemBuiltIn) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__systemBuiltInValue = systemBuiltIn
                    __tmpModified.__systemBuiltInLoaded = true
                }

            override var enabled: Boolean
                get() = (__modified ?: __base!!).enabled
                set(enabled) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__enabledValue = enabled
                    __tmpModified.__enabledLoaded = true
                }

            override var groovyScript: String?
                get() = (__modified ?: __base!!).groovyScript
                set(groovyScript) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__groovyScriptValue = groovyScript
                    __tmpModified.__groovyScriptLoaded = true
                }

            override var vueMain: String?
                get() = (__modified ?: __base!!).vueMain
                set(vueMain) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__vueMainValue = vueMain
                    __tmpModified.__vueMainLoaded = true
                }

            override var vueDefs: String?
                get() = (__modified ?: __base!!).vueDefs
                set(vueDefs) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__vueDefsValue = vueDefs
                    __tmpModified.__vueDefsLoaded = true
                }

            override var configSchema: String?
                get() = (__modified ?: __base!!).configSchema
                set(configSchema) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__configSchemaValue = configSchema
                    __tmpModified.__configSchemaLoaded = true
                }

            override var dependencies: List<ComponentDependency>
                get() = __ctx().toDraftList((__modified ?: __base!!).dependencies, ComponentDependency::class.java, true)
                set(dependencies) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__dependenciesValue = NonSharedList.of(__tmpModified.__dependenciesValue, dependencies)
                }

            override var versions: List<ComponentVersion>
                get() = __ctx().toDraftList((__modified ?: __base!!).versions, ComponentVersion::class.java, true)
                set(versions) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionsValue = NonSharedList.of(__tmpModified.__versionsValue, versions)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun dependencies(): MutableList<ComponentDependencyDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_DEPENDENCIES))) {
                    dependencies = emptyList()
                }
                return dependencies as MutableList<ComponentDependencyDraft>
            }

            override fun versions(): MutableList<ComponentVersionDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_VERSIONS))) {
                    versions = emptyList()
                }
                return versions as MutableList<ComponentVersionDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_COMPONENT_CODE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentCodeValue = null
                    SLOT_COMPONENT_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentNameValue = null
                    SLOT_COMPONENT_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentTypeValue = null
                    SLOT_COMPONENT_CATEGORY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentCategoryValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentCategoryLoaded = false
                        }
                    SLOT_COMPONENT_VERSION ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentVersionValue = null
                    SLOT_COMPONENT_DESC ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentDescLoaded = false
                        }
                    SLOT_COMPONENT_ICON ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentIconValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentIconLoaded = false
                        }
                    SLOT_SYSTEM_BUILT_IN ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    SLOT_ENABLED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    SLOT_GROOVY_SCRIPT ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__groovyScriptValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__groovyScriptLoaded = false
                        }
                    SLOT_VUE_MAIN ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueMainValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueMainLoaded = false
                        }
                    SLOT_VUE_DEFS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueDefsValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueDefsLoaded = false
                        }
                    SLOT_CONFIG_SCHEMA ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__configSchemaValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__configSchemaLoaded = false
                        }
                    SLOT_DEPENDENCIES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__dependenciesValue = null
                    SLOT_VERSIONS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__versionsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "componentCode" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentCodeValue = null
                    "componentName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentNameValue = null
                    "componentType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentTypeValue = null
                    "componentCategory" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentCategoryValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentCategoryLoaded = false
                        }
                    "componentVersion" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__componentVersionValue = null
                    "componentDesc" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentDescValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentDescLoaded = false
                        }
                    "componentIcon" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentIconValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__componentIconLoaded = false
                        }
                    "systemBuiltIn" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__systemBuiltInLoaded = false
                        }
                    "enabled" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__enabledLoaded = false
                        }
                    "groovyScript" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__groovyScriptValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__groovyScriptLoaded = false
                        }
                    "vueMain" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueMainValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueMainLoaded = false
                        }
                    "vueDefs" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueDefsValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__vueDefsLoaded = false
                        }
                    "configSchema" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__configSchemaValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__configSchemaLoaded = false
                        }
                    "dependencies" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__dependenciesValue = null
                    "versions" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__versionsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_COMPONENT_CODE ->
                    	this.componentCode = value as String?
                    	?: throw IllegalArgumentException("'componentCode cannot be null")
                    SLOT_COMPONENT_NAME ->
                    	this.componentName = value as String?
                    	?: throw IllegalArgumentException("'componentName cannot be null")
                    SLOT_COMPONENT_TYPE ->
                    	this.componentType = value as String?
                    	?: throw IllegalArgumentException("'componentType cannot be null")
                    SLOT_COMPONENT_CATEGORY ->
                    	this.componentCategory = value as String?
                    SLOT_COMPONENT_VERSION ->
                    	this.componentVersion = value as String?
                    	?: throw IllegalArgumentException("'componentVersion cannot be null")
                    SLOT_COMPONENT_DESC ->
                    	this.componentDesc = value as String?
                    SLOT_COMPONENT_ICON ->
                    	this.componentIcon = value as String?
                    SLOT_SYSTEM_BUILT_IN ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    SLOT_ENABLED ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    SLOT_GROOVY_SCRIPT ->
                    	this.groovyScript = value as String?
                    SLOT_VUE_MAIN ->
                    	this.vueMain = value as String?
                    SLOT_VUE_DEFS ->
                    	this.vueDefs = value as String?
                    SLOT_CONFIG_SCHEMA ->
                    	this.configSchema = value as String?
                    SLOT_DEPENDENCIES ->
                    	this.dependencies = value as List<ComponentDependency>?
                    	?: throw IllegalArgumentException("'dependencies cannot be null")
                    SLOT_VERSIONS ->
                    	this.versions = value as List<ComponentVersion>?
                    	?: throw IllegalArgumentException("'versions cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "componentCode" ->
                    	this.componentCode = value as String?
                    	?: throw IllegalArgumentException("'componentCode cannot be null")
                    "componentName" ->
                    	this.componentName = value as String?
                    	?: throw IllegalArgumentException("'componentName cannot be null")
                    "componentType" ->
                    	this.componentType = value as String?
                    	?: throw IllegalArgumentException("'componentType cannot be null")
                    "componentCategory" ->
                    	this.componentCategory = value as String?
                    "componentVersion" ->
                    	this.componentVersion = value as String?
                    	?: throw IllegalArgumentException("'componentVersion cannot be null")
                    "componentDesc" ->
                    	this.componentDesc = value as String?
                    "componentIcon" ->
                    	this.componentIcon = value as String?
                    "systemBuiltIn" ->
                    	this.systemBuiltIn = value as Boolean?
                    	?: throw IllegalArgumentException("'systemBuiltIn cannot be null")
                    "enabled" ->
                    	this.enabled = value as Boolean?
                    	?: throw IllegalArgumentException("'enabled cannot be null")
                    "groovyScript" ->
                    	this.groovyScript = value as String?
                    "vueMain" ->
                    	this.vueMain = value as String?
                    "vueDefs" ->
                    	this.vueDefs = value as String?
                    "configSchema" ->
                    	this.configSchema = value as String?
                    "dependencies" ->
                    	this.dependencies = value as List<ComponentDependency>?
                    	?: throw IllegalArgumentException("'dependencies cannot be null")
                    "versions" ->
                    	this.versions = value as List<ComponentVersion>?
                    	?: throw IllegalArgumentException("'versions cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.Component\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(29).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_COMPONENT_CODE ->
                    	__visibility.show(SLOT_COMPONENT_CODE, visible)
                    SLOT_COMPONENT_NAME ->
                    	__visibility.show(SLOT_COMPONENT_NAME, visible)
                    SLOT_COMPONENT_TYPE ->
                    	__visibility.show(SLOT_COMPONENT_TYPE, visible)
                    SLOT_COMPONENT_CATEGORY ->
                    	__visibility.show(SLOT_COMPONENT_CATEGORY, visible)
                    SLOT_COMPONENT_VERSION ->
                    	__visibility.show(SLOT_COMPONENT_VERSION, visible)
                    SLOT_COMPONENT_DESC ->
                    	__visibility.show(SLOT_COMPONENT_DESC, visible)
                    SLOT_COMPONENT_ICON ->
                    	__visibility.show(SLOT_COMPONENT_ICON, visible)
                    SLOT_SYSTEM_BUILT_IN ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    SLOT_ENABLED ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    SLOT_GROOVY_SCRIPT ->
                    	__visibility.show(SLOT_GROOVY_SCRIPT, visible)
                    SLOT_VUE_MAIN ->
                    	__visibility.show(SLOT_VUE_MAIN, visible)
                    SLOT_VUE_DEFS ->
                    	__visibility.show(SLOT_VUE_DEFS, visible)
                    SLOT_CONFIG_SCHEMA ->
                    	__visibility.show(SLOT_CONFIG_SCHEMA, visible)
                    SLOT_DEPENDENCIES ->
                    	__visibility.show(SLOT_DEPENDENCIES, visible)
                    SLOT_VERSIONS ->
                    	__visibility.show(SLOT_VERSIONS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(29).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "componentCode" ->
                    	__visibility.show(SLOT_COMPONENT_CODE, visible)
                    "componentName" ->
                    	__visibility.show(SLOT_COMPONENT_NAME, visible)
                    "componentType" ->
                    	__visibility.show(SLOT_COMPONENT_TYPE, visible)
                    "componentCategory" ->
                    	__visibility.show(SLOT_COMPONENT_CATEGORY, visible)
                    "componentVersion" ->
                    	__visibility.show(SLOT_COMPONENT_VERSION, visible)
                    "componentDesc" ->
                    	__visibility.show(SLOT_COMPONENT_DESC, visible)
                    "componentIcon" ->
                    	__visibility.show(SLOT_COMPONENT_ICON, visible)
                    "systemBuiltIn" ->
                    	__visibility.show(SLOT_SYSTEM_BUILT_IN, visible)
                    "enabled" ->
                    	__visibility.show(SLOT_ENABLED, visible)
                    "groovyScript" ->
                    	__visibility.show(SLOT_GROOVY_SCRIPT, visible)
                    "vueMain" ->
                    	__visibility.show(SLOT_VUE_MAIN, visible)
                    "vueDefs" ->
                    	__visibility.show(SLOT_VUE_DEFS, visible)
                    "configSchema" ->
                    	__visibility.show(SLOT_CONFIG_SCHEMA, visible)
                    "dependencies" ->
                    	__visibility.show(SLOT_DEPENDENCIES, visible)
                    "versions" ->
                    	__visibility.show(SLOT_VERSIONS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_DEPENDENCIES))) {
                            val oldValue = base!!.dependencies
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_VERSIONS))) {
                            val oldValue = base!!.versions
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__dependenciesValue = NonSharedList.of(__tmpModified.__dependenciesValue, __ctx.resolveList(__tmpModified.__dependenciesValue))
                        __tmpModified.__versionsValue = NonSharedList.of(__tmpModified.__versionsValue, __ctx.resolveList(__tmpModified.__versionsValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = Component::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: Component?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun componentCode(componentCode: String?): Builder {
            if (componentCode !== null) {
                __draft.componentCode = componentCode
                __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_CODE), true)
            }
            return this
        }

        public fun componentName(componentName: String?): Builder {
            if (componentName !== null) {
                __draft.componentName = componentName
                __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_NAME), true)
            }
            return this
        }

        public fun componentType(componentType: String?): Builder {
            if (componentType !== null) {
                __draft.componentType = componentType
                __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_TYPE), true)
            }
            return this
        }

        public fun componentCategory(componentCategory: String?): Builder {
            __draft.componentCategory = componentCategory
            __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_CATEGORY), true)
            return this
        }

        public fun componentVersion(componentVersion: String?): Builder {
            if (componentVersion !== null) {
                __draft.componentVersion = componentVersion
                __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_VERSION), true)
            }
            return this
        }

        public fun componentDesc(componentDesc: String?): Builder {
            __draft.componentDesc = componentDesc
            __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_DESC), true)
            return this
        }

        public fun componentIcon(componentIcon: String?): Builder {
            __draft.componentIcon = componentIcon
            __draft.__show(PropId.byIndex(`$`.SLOT_COMPONENT_ICON), true)
            return this
        }

        public fun systemBuiltIn(systemBuiltIn: Boolean?): Builder {
            if (systemBuiltIn !== null) {
                __draft.systemBuiltIn = systemBuiltIn
                __draft.__show(PropId.byIndex(`$`.SLOT_SYSTEM_BUILT_IN), true)
            }
            return this
        }

        public fun enabled(enabled: Boolean?): Builder {
            if (enabled !== null) {
                __draft.enabled = enabled
                __draft.__show(PropId.byIndex(`$`.SLOT_ENABLED), true)
            }
            return this
        }

        public fun groovyScript(groovyScript: String?): Builder {
            __draft.groovyScript = groovyScript
            __draft.__show(PropId.byIndex(`$`.SLOT_GROOVY_SCRIPT), true)
            return this
        }

        public fun vueMain(vueMain: String?): Builder {
            __draft.vueMain = vueMain
            __draft.__show(PropId.byIndex(`$`.SLOT_VUE_MAIN), true)
            return this
        }

        public fun vueDefs(vueDefs: String?): Builder {
            __draft.vueDefs = vueDefs
            __draft.__show(PropId.byIndex(`$`.SLOT_VUE_DEFS), true)
            return this
        }

        public fun configSchema(configSchema: String?): Builder {
            __draft.configSchema = configSchema
            __draft.__show(PropId.byIndex(`$`.SLOT_CONFIG_SCHEMA), true)
            return this
        }

        public fun dependencies(dependencies: List<ComponentDependency>?): Builder {
            if (dependencies !== null) {
                __draft.dependencies = dependencies
                __draft.__show(PropId.byIndex(`$`.SLOT_DEPENDENCIES), true)
            }
            return this
        }

        public fun versions(versions: List<ComponentVersion>?): Builder {
            if (versions !== null) {
                __draft.versions = versions
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSIONS), true)
            }
            return this
        }

        public fun build(): Component = __draft.__unwrap() as Component
    }
}

@GeneratedBy(type = Component::class)
public fun ImmutableCreator<Component>.`by`(resolveImmediately: Boolean = false, block: ComponentDraft.() -> Unit): Component = ComponentDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = Component::class)
public fun ImmutableCreator<Component>.`by`(base: Component?, resolveImmediately: Boolean = false): Component = ComponentDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = Component::class)
public fun ImmutableCreator<Component>.`by`(
    base: Component?,
    resolveImmediately: Boolean = false,
    block: ComponentDraft.() -> Unit,
): Component = ComponentDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = Component::class)
public fun Component(resolveImmediately: Boolean = false, block: ComponentDraft.() -> Unit): Component = ComponentDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = Component::class)
public fun Component(
    base: Component?,
    resolveImmediately: Boolean = false,
    block: ComponentDraft.() -> Unit,
): Component = ComponentDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = Component::class)
public fun MutableList<ComponentDraft>.addBy(resolveImmediately: Boolean = false, block: ComponentDraft.() -> Unit): MutableList<ComponentDraft> {
    add(ComponentDraft.`$`.produce(null, resolveImmediately, block) as ComponentDraft)
    return this
}

@GeneratedBy(type = Component::class)
public fun MutableList<ComponentDraft>.addBy(base: Component?, resolveImmediately: Boolean = false): MutableList<ComponentDraft> {
    add(ComponentDraft.`$`.produce(base, resolveImmediately) as ComponentDraft)
    return this
}

@GeneratedBy(type = Component::class)
public fun MutableList<ComponentDraft>.addBy(
    base: Component?,
    resolveImmediately: Boolean = false,
    block: ComponentDraft.() -> Unit,
): MutableList<ComponentDraft> {
    add(ComponentDraft.`$`.produce(base, resolveImmediately, block) as ComponentDraft)
    return this
}

@GeneratedBy(type = Component::class)
public fun Component.copy(resolveImmediately: Boolean = false, block: ComponentDraft.() -> Unit): Component = ComponentDraft.`$`.produce(this, resolveImmediately, block)
