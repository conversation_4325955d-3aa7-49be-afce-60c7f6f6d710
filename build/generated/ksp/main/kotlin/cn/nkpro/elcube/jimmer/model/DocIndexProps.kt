@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocIndex::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<DocIndex>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocIndex::class)
    get() = get<LocalDateTime>(DocIndexProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocIndex>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocIndex::class)
    get() = get<LocalDateTime>(DocIndexProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<DocIndex>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocIndex::class)
    get() = get<LocalDateTime>(DocIndexProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocIndex>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocIndex::class)
    get() = get<LocalDateTime>(DocIndexProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<DocIndex>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Int>(DocIndexProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<DocIndex>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Int>(DocIndexProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocIndex>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocIndex>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<DocIndex>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Int>(DocIndexProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocIndex>.indexCode: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_CODE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.indexCode: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_CODE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.indexName: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.indexName: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_NAME.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.indexType: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.indexType: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.INDEX_TYPE.unwrap()) as KNullablePropExpression<String>

public val KProps<DocIndex>.docType: KNonNullTable<DocType>
    @GeneratedBy(type = DocIndex::class)
    get() = join(DocIndexProps.DOC_TYPE.unwrap())

public val KProps<DocIndex>.`docType?`: KNullableTable<DocType>
    @GeneratedBy(type = DocIndex::class)
    get() = outerJoin(DocIndexProps.DOC_TYPE.unwrap())

public val KTableEx<DocIndex>.docType: KNonNullTableEx<DocType>
    @GeneratedBy(type = DocIndex::class)
    get() = join(DocIndexProps.DOC_TYPE.unwrap())

public val KTableEx<DocIndex>.`docType?`: KNullableTableEx<DocType>
    @GeneratedBy(type = DocIndex::class)
    get() = outerJoin(DocIndexProps.DOC_TYPE.unwrap())

public val KNonNullTable<DocIndex>.docTypeId: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = getAssociatedId<String>(DocIndexProps.DOC_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.docTypeId: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = getAssociatedId<String>(DocIndexProps.DOC_TYPE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.extractExpr: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.EXTRACT_EXPR.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocIndex>.extractExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = get<String>(DocIndexProps.EXTRACT_EXPR.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocIndex>.searchable: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.SEARCHABLE.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocIndex>.searchable: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.SEARCHABLE.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocIndex>.sortable: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.SORTABLE.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocIndex>.sortable: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.SORTABLE.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocIndex>.aggregatable: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.AGGREGATABLE.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocIndex>.aggregatable: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocIndex::class)
    get() = get<Boolean>(DocIndexProps.AGGREGATABLE.unwrap()) as KNullablePropExpression<Boolean>

public fun KProps<DocIndex>.indexData(block: KImplicitSubQueryTable<DocumentIndexData>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocIndexProps.INDEX_DATA.unwrap(), block)

public val KTableEx<DocIndex>.indexData: KNonNullTableEx<DocumentIndexData>
    @GeneratedBy(type = DocIndex::class)
    get() = join(DocIndexProps.INDEX_DATA.unwrap())

public val KTableEx<DocIndex>.`indexData?`: KNullableTableEx<DocumentIndexData>
    @GeneratedBy(type = DocIndex::class)
    get() = outerJoin(DocIndexProps.INDEX_DATA.unwrap())

public val KRemoteRef.NonNull<DocIndex>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<DocIndex>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocIndex::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = DocIndex::class)
public fun KNonNullTable<DocIndex>.fetchBy(block: DocIndexFetcherDsl.() -> Unit): Selection<DocIndex> = fetch(newFetcher(DocIndex::class).`by`(block))

@GeneratedBy(type = DocIndex::class)
public fun KNullableTable<DocIndex>.fetchBy(block: DocIndexFetcherDsl.() -> Unit): Selection<DocIndex?> = fetch(newFetcher(DocIndex::class).`by`(block))

@GeneratedBy(type = DocIndex::class)
public object DocIndexProps {
    public val ID: TypedProp.Scalar<DocIndex, String> =
            TypedProp.scalar(DocIndex::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<DocIndex, LocalDateTime> =
            TypedProp.scalar(DocIndex::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<DocIndex, LocalDateTime> =
            TypedProp.scalar(DocIndex::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<DocIndex, Int> =
            TypedProp.scalar(DocIndex::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<DocIndex, Boolean> =
            TypedProp.scalar(DocIndex::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<DocIndex, String?> =
            TypedProp.scalar(DocIndex::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<DocIndex, Int?> =
            TypedProp.scalar(DocIndex::sortOrder.toImmutableProp())

    public val INDEX_CODE: TypedProp.Scalar<DocIndex, String> =
            TypedProp.scalar(DocIndex::indexCode.toImmutableProp())

    public val INDEX_NAME: TypedProp.Scalar<DocIndex, String> =
            TypedProp.scalar(DocIndex::indexName.toImmutableProp())

    public val INDEX_TYPE: TypedProp.Scalar<DocIndex, String> =
            TypedProp.scalar(DocIndex::indexType.toImmutableProp())

    public val DOC_TYPE: TypedProp.Reference<DocIndex, DocType> =
            TypedProp.reference(DocIndex::docType.toImmutableProp())

    public val EXTRACT_EXPR: TypedProp.Scalar<DocIndex, String> =
            TypedProp.scalar(DocIndex::extractExpr.toImmutableProp())

    public val SEARCHABLE: TypedProp.Scalar<DocIndex, Boolean> =
            TypedProp.scalar(DocIndex::searchable.toImmutableProp())

    public val SORTABLE: TypedProp.Scalar<DocIndex, Boolean> =
            TypedProp.scalar(DocIndex::sortable.toImmutableProp())

    public val AGGREGATABLE: TypedProp.Scalar<DocIndex, Boolean> =
            TypedProp.scalar(DocIndex::aggregatable.toImmutableProp())

    public val INDEX_DATA: TypedProp.ReferenceList<DocIndex, DocumentIndexData> =
            TypedProp.referenceList(DocIndex::indexData.toImmutableProp())
}
