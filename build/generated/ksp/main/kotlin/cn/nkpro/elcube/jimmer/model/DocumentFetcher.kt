@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.Document::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.IdOnlyFetchType
import org.babyfish.jimmer.sql.fetcher.ReferenceFetchType
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.KRecursiveListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.KRecursiveReferenceFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.KReferenceFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = Document::class)
public fun FetcherCreator<Document>.`by`(block: DocumentFetcherDsl.() -> Unit): Fetcher<Document> {
    val dsl = DocumentFetcherDsl(emptyDocumentFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = Document::class)
public fun FetcherCreator<Document>.`by`(base: Fetcher<Document>?, block: DocumentFetcherDsl.() -> Unit): Fetcher<Document> {
    val dsl = DocumentFetcherDsl(base ?: emptyDocumentFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = Document::class)
public class DocumentFetcherDsl(
    fetcher: Fetcher<Document> = emptyDocumentFetcher,
) {
    private var _fetcher: Fetcher<Document> = fetcher

    public fun internallyGetFetcher(): Fetcher<Document> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun docId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docId")
        } else {
            _fetcher.remove("docId")
        }
    }

    public fun docNo(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docNo")
        } else {
            _fetcher.remove("docNo")
        }
    }

    public fun docName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docName")
        } else {
            _fetcher.remove("docName")
        }
    }

    public fun docType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docType")
        } else {
            _fetcher.remove("docType")
        }
    }

    public fun docType(idOnlyFetchType: IdOnlyFetchType) {
        _fetcher = _fetcher.add("docType", idOnlyFetchType)
    }

    public fun docType(childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher
        )
    }

    public fun docType(childFetcher: Fetcher<DocType>, cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun docType(cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun docType(enabled: Boolean, childFetcher: Fetcher<DocType>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher)
        }
    }

    public fun docType(
        enabled: Boolean,
        childFetcher: Fetcher<DocType>,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childFetcher, cfgBlock)
        }
    }

    public fun docType(enabled: Boolean, childBlock: DocTypeFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(childBlock)
        }
    }

    public fun docType(
        enabled: Boolean,
        cfgBlock: (KReferenceFieldDsl<DocType>.() -> Unit)?,
        childBlock: DocTypeFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("docType")
        } else {
            docType(cfgBlock, childBlock)
        }
    }

    public fun docType(fetchType: ReferenceFetchType, childFetcher: Fetcher<DocType>) {
        _fetcher = _fetcher.add(
            "docType",
            childFetcher,
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun docType(fetchType: ReferenceFetchType, childBlock: DocTypeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "docType",
            DocTypeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference<DocType>(fetchType)
        )
    }

    public fun currentState(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("currentState")
        } else {
            _fetcher.remove("currentState")
        }
    }

    public fun currentState(idOnlyFetchType: IdOnlyFetchType) {
        _fetcher = _fetcher.add("currentState", idOnlyFetchType)
    }

    public fun currentState(childFetcher: Fetcher<DocState>) {
        _fetcher = _fetcher.add(
            "currentState",
            childFetcher
        )
    }

    public fun currentState(childFetcher: Fetcher<DocState>, cfgBlock: (KReferenceFieldDsl<DocState>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "currentState",
            childFetcher,
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun currentState(childBlock: DocStateFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "currentState",
            DocStateFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun currentState(cfgBlock: (KReferenceFieldDsl<DocState>.() -> Unit)?, childBlock: DocStateFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "currentState",
            DocStateFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun currentState(enabled: Boolean, childFetcher: Fetcher<DocState>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("currentState")
        } else {
            currentState(childFetcher)
        }
    }

    public fun currentState(
        enabled: Boolean,
        childFetcher: Fetcher<DocState>,
        cfgBlock: (KReferenceFieldDsl<DocState>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("currentState")
        } else {
            currentState(childFetcher, cfgBlock)
        }
    }

    public fun currentState(enabled: Boolean, childBlock: DocStateFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("currentState")
        } else {
            currentState(childBlock)
        }
    }

    public fun currentState(
        enabled: Boolean,
        cfgBlock: (KReferenceFieldDsl<DocState>.() -> Unit)?,
        childBlock: DocStateFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("currentState")
        } else {
            currentState(cfgBlock, childBlock)
        }
    }

    public fun currentState(fetchType: ReferenceFetchType, childFetcher: Fetcher<DocState>) {
        _fetcher = _fetcher.add(
            "currentState",
            childFetcher,
            JavaFieldConfigUtils.reference<DocState>(fetchType)
        )
    }

    public fun currentState(fetchType: ReferenceFetchType, childBlock: DocStateFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "currentState",
            DocStateFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference<DocState>(fetchType)
        )
    }

    public fun preDocId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("preDocId")
        } else {
            _fetcher.remove("preDocId")
        }
    }

    public fun preDocument(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("preDocument")
        } else {
            _fetcher.remove("preDocument")
        }
    }

    public fun preDocument(idOnlyFetchType: IdOnlyFetchType) {
        _fetcher = _fetcher.add("preDocument", idOnlyFetchType)
    }

    public fun preDocument(childFetcher: Fetcher<Document>) {
        _fetcher = _fetcher.add(
            "preDocument",
            childFetcher
        )
    }

    public fun preDocument(childFetcher: Fetcher<Document>, cfgBlock: (KReferenceFieldDsl<Document>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "preDocument",
            childFetcher,
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun preDocument(childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "preDocument",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun preDocument(cfgBlock: (KReferenceFieldDsl<Document>.() -> Unit)?, childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "preDocument",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference(cfgBlock)
        )
    }

    public fun preDocument(enabled: Boolean, childFetcher: Fetcher<Document>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("preDocument")
        } else {
            preDocument(childFetcher)
        }
    }

    public fun preDocument(
        enabled: Boolean,
        childFetcher: Fetcher<Document>,
        cfgBlock: (KReferenceFieldDsl<Document>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("preDocument")
        } else {
            preDocument(childFetcher, cfgBlock)
        }
    }

    public fun preDocument(enabled: Boolean, childBlock: DocumentFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("preDocument")
        } else {
            preDocument(childBlock)
        }
    }

    public fun preDocument(
        enabled: Boolean,
        cfgBlock: (KReferenceFieldDsl<Document>.() -> Unit)?,
        childBlock: DocumentFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("preDocument")
        } else {
            preDocument(cfgBlock, childBlock)
        }
    }

    public fun preDocument(fetchType: ReferenceFetchType, childFetcher: Fetcher<Document>) {
        _fetcher = _fetcher.add(
            "preDocument",
            childFetcher,
            JavaFieldConfigUtils.reference<Document>(fetchType)
        )
    }

    public fun preDocument(fetchType: ReferenceFetchType, childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "preDocument",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.reference<Document>(fetchType)
        )
    }

    public fun `preDocument*`() {
        _fetcher = _fetcher.addRecursion(
            "preDocument",
            null
        )
    }

    public fun `preDocument*`(cfgBlock: (KRecursiveReferenceFieldDsl<Document>.() -> Unit)?) {
        _fetcher = _fetcher.addRecursion(
            "preDocument",
            JavaFieldConfigUtils.recursiveReference(cfgBlock)
        )
    }

    public fun nextDocuments(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("nextDocuments")
        } else {
            _fetcher.remove("nextDocuments")
        }
    }

    public fun nextDocuments(childFetcher: Fetcher<Document>) {
        _fetcher = _fetcher.add(
            "nextDocuments",
            childFetcher
        )
    }

    public fun nextDocuments(childFetcher: Fetcher<Document>, cfgBlock: (KListFieldDsl<Document>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "nextDocuments",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun nextDocuments(childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "nextDocuments",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun nextDocuments(cfgBlock: (KListFieldDsl<Document>.() -> Unit)?, childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "nextDocuments",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun nextDocuments(enabled: Boolean, childFetcher: Fetcher<Document>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nextDocuments")
        } else {
            nextDocuments(childFetcher)
        }
    }

    public fun nextDocuments(
        enabled: Boolean,
        childFetcher: Fetcher<Document>,
        cfgBlock: (KListFieldDsl<Document>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nextDocuments")
        } else {
            nextDocuments(childFetcher, cfgBlock)
        }
    }

    public fun nextDocuments(enabled: Boolean, childBlock: DocumentFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nextDocuments")
        } else {
            nextDocuments(childBlock)
        }
    }

    public fun nextDocuments(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<Document>.() -> Unit)?,
        childBlock: DocumentFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nextDocuments")
        } else {
            nextDocuments(cfgBlock, childBlock)
        }
    }

    public fun `nextDocuments*`() {
        _fetcher = _fetcher.addRecursion(
            "nextDocuments",
            null
        )
    }

    public fun `nextDocuments*`(cfgBlock: (KRecursiveListFieldDsl<Document>.() -> Unit)?) {
        _fetcher = _fetcher.addRecursion(
            "nextDocuments",
            JavaFieldConfigUtils.recursiveList(cfgBlock)
        )
    }

    public fun partnerId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("partnerId")
        } else {
            _fetcher.remove("partnerId")
        }
    }

    public fun partnerName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("partnerName")
        } else {
            _fetcher.remove("partnerName")
        }
    }

    public fun docAmount(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docAmount")
        } else {
            _fetcher.remove("docAmount")
        }
    }

    public fun currency(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("currency")
        } else {
            _fetcher.remove("currency")
        }
    }

    public fun docDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docDate")
        } else {
            _fetcher.remove("docDate")
        }
    }

    public fun effectiveDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("effectiveDate")
        } else {
            _fetcher.remove("effectiveDate")
        }
    }

    public fun expireDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("expireDate")
        } else {
            _fetcher.remove("expireDate")
        }
    }

    public fun docData(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docData")
        } else {
            _fetcher.remove("docData")
        }
    }

    public fun extProps(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("extProps")
        } else {
            _fetcher.remove("extProps")
        }
    }

    public fun cardData(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cardData")
        } else {
            _fetcher.remove("cardData")
        }
    }

    public fun cardData(childFetcher: Fetcher<DocumentCardData>) {
        _fetcher = _fetcher.add(
            "cardData",
            childFetcher
        )
    }

    public fun cardData(childFetcher: Fetcher<DocumentCardData>, cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "cardData",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cardData(childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cardData",
            DocumentCardDataFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun cardData(cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?, childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cardData",
            DocumentCardDataFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cardData(enabled: Boolean, childFetcher: Fetcher<DocumentCardData>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childFetcher)
        }
    }

    public fun cardData(
        enabled: Boolean,
        childFetcher: Fetcher<DocumentCardData>,
        cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childFetcher, cfgBlock)
        }
    }

    public fun cardData(enabled: Boolean, childBlock: DocumentCardDataFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(childBlock)
        }
    }

    public fun cardData(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocumentCardData>.() -> Unit)?,
        childBlock: DocumentCardDataFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cardData")
        } else {
            cardData(cfgBlock, childBlock)
        }
    }

    public fun histories(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("histories")
        } else {
            _fetcher.remove("histories")
        }
    }

    public fun histories(childFetcher: Fetcher<DocumentHistory>) {
        _fetcher = _fetcher.add(
            "histories",
            childFetcher
        )
    }

    public fun histories(childFetcher: Fetcher<DocumentHistory>, cfgBlock: (KListFieldDsl<DocumentHistory>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "histories",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun histories(childBlock: DocumentHistoryFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "histories",
            DocumentHistoryFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun histories(cfgBlock: (KListFieldDsl<DocumentHistory>.() -> Unit)?, childBlock: DocumentHistoryFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "histories",
            DocumentHistoryFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun histories(enabled: Boolean, childFetcher: Fetcher<DocumentHistory>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("histories")
        } else {
            histories(childFetcher)
        }
    }

    public fun histories(
        enabled: Boolean,
        childFetcher: Fetcher<DocumentHistory>,
        cfgBlock: (KListFieldDsl<DocumentHistory>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("histories")
        } else {
            histories(childFetcher, cfgBlock)
        }
    }

    public fun histories(enabled: Boolean, childBlock: DocumentHistoryFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("histories")
        } else {
            histories(childBlock)
        }
    }

    public fun histories(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocumentHistory>.() -> Unit)?,
        childBlock: DocumentHistoryFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("histories")
        } else {
            histories(cfgBlock, childBlock)
        }
    }

    public fun indexData(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexData")
        } else {
            _fetcher.remove("indexData")
        }
    }

    public fun indexData(childFetcher: Fetcher<DocumentIndexData>) {
        _fetcher = _fetcher.add(
            "indexData",
            childFetcher
        )
    }

    public fun indexData(childFetcher: Fetcher<DocumentIndexData>, cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "indexData",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexData(childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexData",
            DocumentIndexDataFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun indexData(cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?, childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexData",
            DocumentIndexDataFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexData(enabled: Boolean, childFetcher: Fetcher<DocumentIndexData>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childFetcher)
        }
    }

    public fun indexData(
        enabled: Boolean,
        childFetcher: Fetcher<DocumentIndexData>,
        cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childFetcher, cfgBlock)
        }
    }

    public fun indexData(enabled: Boolean, childBlock: DocumentIndexDataFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(childBlock)
        }
    }

    public fun indexData(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocumentIndexData>.() -> Unit)?,
        childBlock: DocumentIndexDataFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexData")
        } else {
            indexData(cfgBlock, childBlock)
        }
    }
}

private val emptyDocumentFetcher: Fetcher<Document> = FetcherImpl(Document::class.java)
