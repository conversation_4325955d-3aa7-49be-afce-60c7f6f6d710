@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.enums.InvoiceType
import cn.nkpro.elcube.jimmer.enums.PaymentStatus
import cn.nkpro.elcube.jimmer.enums.Priority
import cn.nkpro.elcube.jimmer.enums.SalesOrderStatus
import cn.nkpro.elcube.jimmer.model.BusinessEntityDraft
import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = SalesOrderAnnotated::class)
public interface SalesOrderAnnotatedDraft : SalesOrderAnnotated, BusinessEntityDraft {
    override var orderNo: String

    override var customerName: String

    override var totalAmount: BigDecimal

    override var orderDate: LocalDateTime

    override var deliveryDate: LocalDateTime

    override var status: SalesOrderStatus

    override var priority: Priority

    override var paymentStatus: PaymentStatus

    override var invoiceType: InvoiceType?

    override var remark: String?

    override var orderItems: List<SalesOrderItemAnnotated>

    override var shipments: List<ShipmentAnnotated>

    override var invoices: List<InvoiceAnnotated>

    override var payments: List<PaymentAnnotated>

    public fun orderItems(): MutableList<SalesOrderItemAnnotatedDraft>

    public fun shipments(): MutableList<ShipmentAnnotatedDraft>

    public fun invoices(): MutableList<InvoiceAnnotatedDraft>

    public fun payments(): MutableList<PaymentAnnotatedDraft>

    @GeneratedBy(type = SalesOrderAnnotated::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_ORDER_NO: Int = 14

        public const val SLOT_CUSTOMER_NAME: Int = 15

        public const val SLOT_TOTAL_AMOUNT: Int = 16

        public const val SLOT_ORDER_DATE: Int = 17

        public const val SLOT_DELIVERY_DATE: Int = 18

        public const val SLOT_STATUS: Int = 19

        public const val SLOT_PRIORITY: Int = 20

        public const val SLOT_PAYMENT_STATUS: Int = 21

        public const val SLOT_INVOICE_TYPE: Int = 22

        public const val SLOT_REMARK: Int = 23

        public const val SLOT_ORDER_ITEMS: Int = 24

        public const val SLOT_SHIPMENTS: Int = 25

        public const val SLOT_INVOICES: Int = 26

        public const val SLOT_PAYMENTS: Int = 27

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                SalesOrderAnnotated::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as SalesOrderAnnotated?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_ORDER_NO, "orderNo", String::class.java, false)
            .add(SLOT_CUSTOMER_NAME, "customerName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_TOTAL_AMOUNT, "totalAmount", ImmutablePropCategory.SCALAR, BigDecimal::class.java, false)
            .add(SLOT_ORDER_DATE, "orderDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
            .add(SLOT_DELIVERY_DATE, "deliveryDate", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
            .add(SLOT_STATUS, "status", ImmutablePropCategory.SCALAR, SalesOrderStatus::class.java, false)
            .add(SLOT_PRIORITY, "priority", ImmutablePropCategory.SCALAR, Priority::class.java, false)
            .add(SLOT_PAYMENT_STATUS, "paymentStatus", ImmutablePropCategory.SCALAR, PaymentStatus::class.java, false)
            .add(SLOT_INVOICE_TYPE, "invoiceType", ImmutablePropCategory.SCALAR, InvoiceType::class.java, true)
            .add(SLOT_REMARK, "remark", ImmutablePropCategory.SCALAR, String::class.java, true)
            .add(SLOT_ORDER_ITEMS, "orderItems", OneToMany::class.java, SalesOrderItemAnnotated::class.java, false)
            .add(SLOT_SHIPMENTS, "shipments", OneToMany::class.java, ShipmentAnnotated::class.java, false)
            .add(SLOT_INVOICES, "invoices", OneToMany::class.java, InvoiceAnnotated::class.java, false)
            .add(SLOT_PAYMENTS, "payments", OneToMany::class.java, PaymentAnnotated::class.java, false)
            .build()

        public fun produce(base: SalesOrderAnnotated? = null, resolveImmediately: Boolean = false): SalesOrderAnnotated {
            val consumer = DraftConsumer<SalesOrderAnnotatedDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as SalesOrderAnnotated
        }

        public fun produce(
            base: SalesOrderAnnotated? = null,
            resolveImmediately: Boolean = false,
            block: SalesOrderAnnotatedDraft.() -> Unit,
        ): SalesOrderAnnotated {
            val consumer = DraftConsumer<SalesOrderAnnotatedDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as SalesOrderAnnotated
        }

        @GeneratedBy(type = SalesOrderAnnotated::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "orderNo", "customerName", "totalAmount", "orderDate", "deliveryDate", "status", "priority", "paymentStatus", "invoiceType", "remark", "orderItems", "shipments", "invoices", "payments")
        private abstract interface Implementor : SalesOrderAnnotated, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_ORDER_NO ->
                	orderNo
                SLOT_CUSTOMER_NAME ->
                	customerName
                SLOT_TOTAL_AMOUNT ->
                	totalAmount
                SLOT_ORDER_DATE ->
                	orderDate
                SLOT_DELIVERY_DATE ->
                	deliveryDate
                SLOT_STATUS ->
                	status
                SLOT_PRIORITY ->
                	priority
                SLOT_PAYMENT_STATUS ->
                	paymentStatus
                SLOT_INVOICE_TYPE ->
                	invoiceType
                SLOT_REMARK ->
                	remark
                SLOT_ORDER_ITEMS ->
                	orderItems
                SLOT_SHIPMENTS ->
                	shipments
                SLOT_INVOICES ->
                	invoices
                SLOT_PAYMENTS ->
                	payments
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "orderNo" ->
                	orderNo
                "customerName" ->
                	customerName
                "totalAmount" ->
                	totalAmount
                "orderDate" ->
                	orderDate
                "deliveryDate" ->
                	deliveryDate
                "status" ->
                	status
                "priority" ->
                	priority
                "paymentStatus" ->
                	paymentStatus
                "invoiceType" ->
                	invoiceType
                "remark" ->
                	remark
                "orderItems" ->
                	orderItems
                "shipments" ->
                	shipments
                "invoices" ->
                	invoices
                "payments" ->
                	payments
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = SalesOrderAnnotated::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orderNoValue: String? = null

            @get:JsonIgnore
            internal var __customerNameValue: String? = null

            @get:JsonIgnore
            internal var __totalAmountValue: BigDecimal? = null

            @get:JsonIgnore
            internal var __orderDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __deliveryDateValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __statusValue: SalesOrderStatus? = null

            @get:JsonIgnore
            internal var __priorityValue: Priority? = null

            @get:JsonIgnore
            internal var __paymentStatusValue: PaymentStatus? = null

            @get:JsonIgnore
            internal var __invoiceTypeValue: InvoiceType? = null

            @get:JsonIgnore
            internal var __invoiceTypeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __remarkValue: String? = null

            @get:JsonIgnore
            internal var __remarkLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orderItemsValue: NonSharedList<SalesOrderItemAnnotated>? = null

            @get:JsonIgnore
            internal var __shipmentsValue: NonSharedList<ShipmentAnnotated>? = null

            @get:JsonIgnore
            internal var __invoicesValue: NonSharedList<InvoiceAnnotated>? = null

            @get:JsonIgnore
            internal var __paymentsValue: NonSharedList<PaymentAnnotated>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val orderNo: String
                get() {
                    val __orderNoValue = this.__orderNoValue
                    if (__orderNoValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "orderNo")
                    }
                    return __orderNoValue
                }

            override val customerName: String
                get() {
                    val __customerNameValue = this.__customerNameValue
                    if (__customerNameValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "customerName")
                    }
                    return __customerNameValue
                }

            override val totalAmount: BigDecimal
                get() {
                    val __totalAmountValue = this.__totalAmountValue
                    if (__totalAmountValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "totalAmount")
                    }
                    return __totalAmountValue
                }

            override val orderDate: LocalDateTime
                get() {
                    val __orderDateValue = this.__orderDateValue
                    if (__orderDateValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "orderDate")
                    }
                    return __orderDateValue
                }

            override val deliveryDate: LocalDateTime
                get() {
                    val __deliveryDateValue = this.__deliveryDateValue
                    if (__deliveryDateValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "deliveryDate")
                    }
                    return __deliveryDateValue
                }

            override val status: SalesOrderStatus
                get() {
                    val __statusValue = this.__statusValue
                    if (__statusValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "status")
                    }
                    return __statusValue
                }

            override val priority: Priority
                get() {
                    val __priorityValue = this.__priorityValue
                    if (__priorityValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "priority")
                    }
                    return __priorityValue
                }

            override val paymentStatus: PaymentStatus
                get() {
                    val __paymentStatusValue = this.__paymentStatusValue
                    if (__paymentStatusValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "paymentStatus")
                    }
                    return __paymentStatusValue
                }

            override val invoiceType: InvoiceType?
                get() {
                    if (!__invoiceTypeLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "invoiceType")
                    }
                    return __invoiceTypeValue
                }

            override val remark: String?
                get() {
                    if (!__remarkLoaded) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "remark")
                    }
                    return __remarkValue
                }

            override val orderItems: List<SalesOrderItemAnnotated>
                get() {
                    val __orderItemsValue = this.__orderItemsValue
                    if (__orderItemsValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "orderItems")
                    }
                    return __orderItemsValue
                }

            override val shipments: List<ShipmentAnnotated>
                get() {
                    val __shipmentsValue = this.__shipmentsValue
                    if (__shipmentsValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "shipments")
                    }
                    return __shipmentsValue
                }

            override val invoices: List<InvoiceAnnotated>
                get() {
                    val __invoicesValue = this.__invoicesValue
                    if (__invoicesValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "invoices")
                    }
                    return __invoicesValue
                }

            override val payments: List<PaymentAnnotated>
                get() {
                    val __paymentsValue = this.__paymentsValue
                    if (__paymentsValue === null) {
                        throw UnloadedException(SalesOrderAnnotated::class.java, "payments")
                    }
                    return __paymentsValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_ORDER_NO ->
                	__orderNoValue !== null
                SLOT_CUSTOMER_NAME ->
                	__customerNameValue !== null
                SLOT_TOTAL_AMOUNT ->
                	__totalAmountValue !== null
                SLOT_ORDER_DATE ->
                	__orderDateValue !== null
                SLOT_DELIVERY_DATE ->
                	__deliveryDateValue !== null
                SLOT_STATUS ->
                	__statusValue !== null
                SLOT_PRIORITY ->
                	__priorityValue !== null
                SLOT_PAYMENT_STATUS ->
                	__paymentStatusValue !== null
                SLOT_INVOICE_TYPE ->
                	__invoiceTypeLoaded
                SLOT_REMARK ->
                	__remarkLoaded
                SLOT_ORDER_ITEMS ->
                	__orderItemsValue !== null
                SLOT_SHIPMENTS ->
                	__shipmentsValue !== null
                SLOT_INVOICES ->
                	__invoicesValue !== null
                SLOT_PAYMENTS ->
                	__paymentsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "orderNo" ->
                	__orderNoValue !== null
                "customerName" ->
                	__customerNameValue !== null
                "totalAmount" ->
                	__totalAmountValue !== null
                "orderDate" ->
                	__orderDateValue !== null
                "deliveryDate" ->
                	__deliveryDateValue !== null
                "status" ->
                	__statusValue !== null
                "priority" ->
                	__priorityValue !== null
                "paymentStatus" ->
                	__paymentStatusValue !== null
                "invoiceType" ->
                	__invoiceTypeLoaded
                "remark" ->
                	__remarkLoaded
                "orderItems" ->
                	__orderItemsValue !== null
                "shipments" ->
                	__shipmentsValue !== null
                "invoices" ->
                	__invoicesValue !== null
                "payments" ->
                	__paymentsValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_ORDER_NO ->
                    	__visibility.visible(SLOT_ORDER_NO)
                    SLOT_CUSTOMER_NAME ->
                    	__visibility.visible(SLOT_CUSTOMER_NAME)
                    SLOT_TOTAL_AMOUNT ->
                    	__visibility.visible(SLOT_TOTAL_AMOUNT)
                    SLOT_ORDER_DATE ->
                    	__visibility.visible(SLOT_ORDER_DATE)
                    SLOT_DELIVERY_DATE ->
                    	__visibility.visible(SLOT_DELIVERY_DATE)
                    SLOT_STATUS ->
                    	__visibility.visible(SLOT_STATUS)
                    SLOT_PRIORITY ->
                    	__visibility.visible(SLOT_PRIORITY)
                    SLOT_PAYMENT_STATUS ->
                    	__visibility.visible(SLOT_PAYMENT_STATUS)
                    SLOT_INVOICE_TYPE ->
                    	__visibility.visible(SLOT_INVOICE_TYPE)
                    SLOT_REMARK ->
                    	__visibility.visible(SLOT_REMARK)
                    SLOT_ORDER_ITEMS ->
                    	__visibility.visible(SLOT_ORDER_ITEMS)
                    SLOT_SHIPMENTS ->
                    	__visibility.visible(SLOT_SHIPMENTS)
                    SLOT_INVOICES ->
                    	__visibility.visible(SLOT_INVOICES)
                    SLOT_PAYMENTS ->
                    	__visibility.visible(SLOT_PAYMENTS)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "orderNo" ->
                    	__visibility.visible(SLOT_ORDER_NO)
                    "customerName" ->
                    	__visibility.visible(SLOT_CUSTOMER_NAME)
                    "totalAmount" ->
                    	__visibility.visible(SLOT_TOTAL_AMOUNT)
                    "orderDate" ->
                    	__visibility.visible(SLOT_ORDER_DATE)
                    "deliveryDate" ->
                    	__visibility.visible(SLOT_DELIVERY_DATE)
                    "status" ->
                    	__visibility.visible(SLOT_STATUS)
                    "priority" ->
                    	__visibility.visible(SLOT_PRIORITY)
                    "paymentStatus" ->
                    	__visibility.visible(SLOT_PAYMENT_STATUS)
                    "invoiceType" ->
                    	__visibility.visible(SLOT_INVOICE_TYPE)
                    "remark" ->
                    	__visibility.visible(SLOT_REMARK)
                    "orderItems" ->
                    	__visibility.visible(SLOT_ORDER_ITEMS)
                    "shipments" ->
                    	__visibility.visible(SLOT_SHIPMENTS)
                    "invoices" ->
                    	__visibility.visible(SLOT_INVOICES)
                    "payments" ->
                    	__visibility.visible(SLOT_PAYMENTS)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__orderNoValue !== null) {
                    hash = 31 * hash + __orderNoValue.hashCode()
                }
                if (__customerNameValue !== null) {
                    hash = 31 * hash + __customerNameValue.hashCode()
                }
                if (__totalAmountValue !== null) {
                    hash = 31 * hash + __totalAmountValue.hashCode()
                }
                if (__orderDateValue !== null) {
                    hash = 31 * hash + __orderDateValue.hashCode()
                }
                if (__deliveryDateValue !== null) {
                    hash = 31 * hash + __deliveryDateValue.hashCode()
                }
                if (__statusValue !== null) {
                    hash = 31 * hash + __statusValue.hashCode()
                }
                if (__priorityValue !== null) {
                    hash = 31 * hash + __priorityValue.hashCode()
                }
                if (__paymentStatusValue !== null) {
                    hash = 31 * hash + __paymentStatusValue.hashCode()
                }
                if (__invoiceTypeLoaded) {
                    hash = 31 * hash + (__invoiceTypeValue?.hashCode() ?: 0)
                }
                if (__remarkLoaded) {
                    hash = 31 * hash + (__remarkValue?.hashCode() ?: 0)
                }
                if (__orderItemsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__orderItemsValue)
                }
                if (__shipmentsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__shipmentsValue)
                }
                if (__invoicesValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__invoicesValue)
                }
                if (__paymentsValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__paymentsValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__orderNoValue !== null) {
                    hash = 31 * hash + __orderNoValue.hashCode()
                }
                if (__customerNameValue !== null) {
                    hash = 31 * hash + __customerNameValue.hashCode()
                }
                if (__totalAmountValue !== null) {
                    hash = 31 * hash + __totalAmountValue.hashCode()
                }
                if (__orderDateValue !== null) {
                    hash = 31 * hash + __orderDateValue.hashCode()
                }
                if (__deliveryDateValue !== null) {
                    hash = 31 * hash + __deliveryDateValue.hashCode()
                }
                if (__statusValue !== null) {
                    hash = 31 * hash + __statusValue.hashCode()
                }
                if (__priorityValue !== null) {
                    hash = 31 * hash + __priorityValue.hashCode()
                }
                if (__paymentStatusValue !== null) {
                    hash = 31 * hash + __paymentStatusValue.hashCode()
                }
                if (__invoiceTypeLoaded) {
                    hash = 31 * hash + (__invoiceTypeValue?.hashCode() ?: 0)
                }
                if (__remarkLoaded) {
                    hash = 31 * hash + (__remarkValue?.hashCode() ?: 0)
                }
                if (__orderItemsValue !== null) {
                    hash = 31 * hash + __orderItemsValue.hashCode()
                }
                if (__shipmentsValue !== null) {
                    hash = 31 * hash + __shipmentsValue.hashCode()
                }
                if (__invoicesValue !== null) {
                    hash = 31 * hash + __invoicesValue.hashCode()
                }
                if (__paymentsValue !== null) {
                    hash = 31 * hash + __paymentsValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_NO)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_NO))) {
                    return false
                }
                val __orderNoLoaded = 
                    this.__orderNoValue !== null
                if (__orderNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_NO)))) {
                    return false
                }
                if (__orderNoLoaded && this.__orderNoValue != __other.orderNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME))) {
                    return false
                }
                val __customerNameLoaded = 
                    this.__customerNameValue !== null
                if (__customerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CUSTOMER_NAME)))) {
                    return false
                }
                if (__customerNameLoaded && this.__customerNameValue != __other.customerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT))) {
                    return false
                }
                val __totalAmountLoaded = 
                    this.__totalAmountValue !== null
                if (__totalAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TOTAL_AMOUNT)))) {
                    return false
                }
                if (__totalAmountLoaded && this.__totalAmountValue != __other.totalAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_DATE))) {
                    return false
                }
                val __orderDateLoaded = 
                    this.__orderDateValue !== null
                if (__orderDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_DATE)))) {
                    return false
                }
                if (__orderDateLoaded && this.__orderDateValue != __other.orderDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE))) {
                    return false
                }
                val __deliveryDateLoaded = 
                    this.__deliveryDateValue !== null
                if (__deliveryDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELIVERY_DATE)))) {
                    return false
                }
                if (__deliveryDateLoaded && this.__deliveryDateValue != __other.deliveryDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false
                }
                val __statusLoaded = 
                    this.__statusValue !== null
                if (__statusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATUS)))) {
                    return false
                }
                if (__statusLoaded && this.__statusValue != __other.status) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRIORITY)) != __other.__isVisible(PropId.byIndex(SLOT_PRIORITY))) {
                    return false
                }
                val __priorityLoaded = 
                    this.__priorityValue !== null
                if (__priorityLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRIORITY)))) {
                    return false
                }
                if (__priorityLoaded && this.__priorityValue != __other.priority) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS))) {
                    return false
                }
                val __paymentStatusLoaded = 
                    this.__paymentStatusValue !== null
                if (__paymentStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENT_STATUS)))) {
                    return false
                }
                if (__paymentStatusLoaded && this.__paymentStatusValue != __other.paymentStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE))) {
                    return false
                }
                val __invoiceTypeLoaded = 
                    this.__invoiceTypeLoaded
                if (__invoiceTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICE_TYPE)))) {
                    return false
                }
                if (__invoiceTypeLoaded && this.__invoiceTypeValue != __other.invoiceType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REMARK)) != __other.__isVisible(PropId.byIndex(SLOT_REMARK))) {
                    return false
                }
                val __remarkLoaded = 
                    this.__remarkLoaded
                if (__remarkLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REMARK)))) {
                    return false
                }
                if (__remarkLoaded && this.__remarkValue != __other.remark) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_ITEMS)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_ITEMS))) {
                    return false
                }
                val __orderItemsLoaded = 
                    this.__orderItemsValue !== null
                if (__orderItemsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_ITEMS)))) {
                    return false
                }
                if (__orderItemsLoaded && this.__orderItemsValue !== __other.orderItems) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SHIPMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_SHIPMENTS))) {
                    return false
                }
                val __shipmentsLoaded = 
                    this.__shipmentsValue !== null
                if (__shipmentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SHIPMENTS)))) {
                    return false
                }
                if (__shipmentsLoaded && this.__shipmentsValue !== __other.shipments) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICES)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICES))) {
                    return false
                }
                val __invoicesLoaded = 
                    this.__invoicesValue !== null
                if (__invoicesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICES)))) {
                    return false
                }
                if (__invoicesLoaded && this.__invoicesValue !== __other.invoices) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENTS))) {
                    return false
                }
                val __paymentsLoaded = 
                    this.__paymentsValue !== null
                if (__paymentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENTS)))) {
                    return false
                }
                if (__paymentsLoaded && this.__paymentsValue !== __other.payments) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_NO)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_NO))) {
                    return false
                }
                val __orderNoLoaded = 
                    this.__orderNoValue !== null
                if (__orderNoLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_NO)))) {
                    return false
                }
                if (__orderNoLoaded && this.__orderNoValue != __other.orderNo) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_CUSTOMER_NAME))) {
                    return false
                }
                val __customerNameLoaded = 
                    this.__customerNameValue !== null
                if (__customerNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CUSTOMER_NAME)))) {
                    return false
                }
                if (__customerNameLoaded && this.__customerNameValue != __other.customerName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT)) != __other.__isVisible(PropId.byIndex(SLOT_TOTAL_AMOUNT))) {
                    return false
                }
                val __totalAmountLoaded = 
                    this.__totalAmountValue !== null
                if (__totalAmountLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TOTAL_AMOUNT)))) {
                    return false
                }
                if (__totalAmountLoaded && this.__totalAmountValue != __other.totalAmount) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_DATE))) {
                    return false
                }
                val __orderDateLoaded = 
                    this.__orderDateValue !== null
                if (__orderDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_DATE)))) {
                    return false
                }
                if (__orderDateLoaded && this.__orderDateValue != __other.orderDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE)) != __other.__isVisible(PropId.byIndex(SLOT_DELIVERY_DATE))) {
                    return false
                }
                val __deliveryDateLoaded = 
                    this.__deliveryDateValue !== null
                if (__deliveryDateLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELIVERY_DATE)))) {
                    return false
                }
                if (__deliveryDateLoaded && this.__deliveryDateValue != __other.deliveryDate) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_STATUS))) {
                    return false
                }
                val __statusLoaded = 
                    this.__statusValue !== null
                if (__statusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_STATUS)))) {
                    return false
                }
                if (__statusLoaded && this.__statusValue != __other.status) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PRIORITY)) != __other.__isVisible(PropId.byIndex(SLOT_PRIORITY))) {
                    return false
                }
                val __priorityLoaded = 
                    this.__priorityValue !== null
                if (__priorityLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PRIORITY)))) {
                    return false
                }
                if (__priorityLoaded && this.__priorityValue != __other.priority) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENT_STATUS))) {
                    return false
                }
                val __paymentStatusLoaded = 
                    this.__paymentStatusValue !== null
                if (__paymentStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENT_STATUS)))) {
                    return false
                }
                if (__paymentStatusLoaded && this.__paymentStatusValue != __other.paymentStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICE_TYPE))) {
                    return false
                }
                val __invoiceTypeLoaded = 
                    this.__invoiceTypeLoaded
                if (__invoiceTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICE_TYPE)))) {
                    return false
                }
                if (__invoiceTypeLoaded && this.__invoiceTypeValue != __other.invoiceType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_REMARK)) != __other.__isVisible(PropId.byIndex(SLOT_REMARK))) {
                    return false
                }
                val __remarkLoaded = 
                    this.__remarkLoaded
                if (__remarkLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_REMARK)))) {
                    return false
                }
                if (__remarkLoaded && this.__remarkValue != __other.remark) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORDER_ITEMS)) != __other.__isVisible(PropId.byIndex(SLOT_ORDER_ITEMS))) {
                    return false
                }
                val __orderItemsLoaded = 
                    this.__orderItemsValue !== null
                if (__orderItemsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORDER_ITEMS)))) {
                    return false
                }
                if (__orderItemsLoaded && this.__orderItemsValue != __other.orderItems) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SHIPMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_SHIPMENTS))) {
                    return false
                }
                val __shipmentsLoaded = 
                    this.__shipmentsValue !== null
                if (__shipmentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SHIPMENTS)))) {
                    return false
                }
                if (__shipmentsLoaded && this.__shipmentsValue != __other.shipments) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INVOICES)) != __other.__isVisible(PropId.byIndex(SLOT_INVOICES))) {
                    return false
                }
                val __invoicesLoaded = 
                    this.__invoicesValue !== null
                if (__invoicesLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INVOICES)))) {
                    return false
                }
                if (__invoicesLoaded && this.__invoicesValue != __other.invoices) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_PAYMENTS)) != __other.__isVisible(PropId.byIndex(SLOT_PAYMENTS))) {
                    return false
                }
                val __paymentsLoaded = 
                    this.__paymentsValue !== null
                if (__paymentsLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_PAYMENTS)))) {
                    return false
                }
                if (__paymentsLoaded && this.__paymentsValue != __other.payments) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = SalesOrderAnnotated::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: SalesOrderAnnotated?,
        ) : Implementor,
            SalesOrderAnnotatedDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: SalesOrderAnnotated? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var orderNo: String
                get() = (__modified ?: __base!!).orderNo
                set(orderNo) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orderNoValue = orderNo
                }

            override var customerName: String
                get() = (__modified ?: __base!!).customerName
                set(customerName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__customerNameValue = customerName
                }

            override var totalAmount: BigDecimal
                get() = (__modified ?: __base!!).totalAmount
                set(totalAmount) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__totalAmountValue = totalAmount
                }

            override var orderDate: LocalDateTime
                get() = (__modified ?: __base!!).orderDate
                set(orderDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orderDateValue = orderDate
                }

            override var deliveryDate: LocalDateTime
                get() = (__modified ?: __base!!).deliveryDate
                set(deliveryDate) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deliveryDateValue = deliveryDate
                }

            override var status: SalesOrderStatus
                get() = (__modified ?: __base!!).status
                set(status) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__statusValue = status
                }

            override var priority: Priority
                get() = (__modified ?: __base!!).priority
                set(priority) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__priorityValue = priority
                }

            override var paymentStatus: PaymentStatus
                get() = (__modified ?: __base!!).paymentStatus
                set(paymentStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__paymentStatusValue = paymentStatus
                }

            override var invoiceType: InvoiceType?
                get() = (__modified ?: __base!!).invoiceType
                set(invoiceType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__invoiceTypeValue = invoiceType
                    __tmpModified.__invoiceTypeLoaded = true
                }

            override var remark: String?
                get() = (__modified ?: __base!!).remark
                set(remark) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__remarkValue = remark
                    __tmpModified.__remarkLoaded = true
                }

            override var orderItems: List<SalesOrderItemAnnotated>
                get() = __ctx().toDraftList((__modified ?: __base!!).orderItems, SalesOrderItemAnnotated::class.java, true)
                set(orderItems) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orderItemsValue = NonSharedList.of(__tmpModified.__orderItemsValue, orderItems)
                }

            override var shipments: List<ShipmentAnnotated>
                get() = __ctx().toDraftList((__modified ?: __base!!).shipments, ShipmentAnnotated::class.java, true)
                set(shipments) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__shipmentsValue = NonSharedList.of(__tmpModified.__shipmentsValue, shipments)
                }

            override var invoices: List<InvoiceAnnotated>
                get() = __ctx().toDraftList((__modified ?: __base!!).invoices, InvoiceAnnotated::class.java, true)
                set(invoices) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__invoicesValue = NonSharedList.of(__tmpModified.__invoicesValue, invoices)
                }

            override var payments: List<PaymentAnnotated>
                get() = __ctx().toDraftList((__modified ?: __base!!).payments, PaymentAnnotated::class.java, true)
                set(payments) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__paymentsValue = NonSharedList.of(__tmpModified.__paymentsValue, payments)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun orderItems(): MutableList<SalesOrderItemAnnotatedDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_ORDER_ITEMS))) {
                    orderItems = emptyList()
                }
                return orderItems as MutableList<SalesOrderItemAnnotatedDraft>
            }

            override fun shipments(): MutableList<ShipmentAnnotatedDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_SHIPMENTS))) {
                    shipments = emptyList()
                }
                return shipments as MutableList<ShipmentAnnotatedDraft>
            }

            override fun invoices(): MutableList<InvoiceAnnotatedDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_INVOICES))) {
                    invoices = emptyList()
                }
                return invoices as MutableList<InvoiceAnnotatedDraft>
            }

            override fun payments(): MutableList<PaymentAnnotatedDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_PAYMENTS))) {
                    payments = emptyList()
                }
                return payments as MutableList<PaymentAnnotatedDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_ORDER_NO ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderNoValue = null
                    SLOT_CUSTOMER_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__customerNameValue = null
                    SLOT_TOTAL_AMOUNT ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__totalAmountValue = null
                    SLOT_ORDER_DATE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderDateValue = null
                    SLOT_DELIVERY_DATE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__deliveryDateValue = null
                    SLOT_STATUS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statusValue = null
                    SLOT_PRIORITY ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__priorityValue = null
                    SLOT_PAYMENT_STATUS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentStatusValue = null
                    SLOT_INVOICE_TYPE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeLoaded = false
                        }
                    SLOT_REMARK ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkLoaded = false
                        }
                    SLOT_ORDER_ITEMS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderItemsValue = null
                    SLOT_SHIPMENTS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__shipmentsValue = null
                    SLOT_INVOICES ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__invoicesValue = null
                    SLOT_PAYMENTS ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "orderNo" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderNoValue = null
                    "customerName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__customerNameValue = null
                    "totalAmount" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__totalAmountValue = null
                    "orderDate" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderDateValue = null
                    "deliveryDate" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__deliveryDateValue = null
                    "status" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__statusValue = null
                    "priority" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__priorityValue = null
                    "paymentStatus" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentStatusValue = null
                    "invoiceType" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__invoiceTypeLoaded = false
                        }
                    "remark" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__remarkLoaded = false
                        }
                    "orderItems" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__orderItemsValue = null
                    "shipments" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__shipmentsValue = null
                    "invoices" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__invoicesValue = null
                    "payments" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__paymentsValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_ORDER_NO ->
                    	this.orderNo = value as String?
                    	?: throw IllegalArgumentException("'orderNo cannot be null")
                    SLOT_CUSTOMER_NAME ->
                    	this.customerName = value as String?
                    	?: throw IllegalArgumentException("'customerName cannot be null")
                    SLOT_TOTAL_AMOUNT ->
                    	this.totalAmount = value as BigDecimal?
                    	?: throw IllegalArgumentException("'totalAmount cannot be null")
                    SLOT_ORDER_DATE ->
                    	this.orderDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'orderDate cannot be null")
                    SLOT_DELIVERY_DATE ->
                    	this.deliveryDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'deliveryDate cannot be null")
                    SLOT_STATUS ->
                    	this.status = value as SalesOrderStatus?
                    	?: throw IllegalArgumentException("'status cannot be null")
                    SLOT_PRIORITY ->
                    	this.priority = value as Priority?
                    	?: throw IllegalArgumentException("'priority cannot be null")
                    SLOT_PAYMENT_STATUS ->
                    	this.paymentStatus = value as PaymentStatus?
                    	?: throw IllegalArgumentException("'paymentStatus cannot be null")
                    SLOT_INVOICE_TYPE ->
                    	this.invoiceType = value as InvoiceType?
                    SLOT_REMARK ->
                    	this.remark = value as String?
                    SLOT_ORDER_ITEMS ->
                    	this.orderItems = value as List<SalesOrderItemAnnotated>?
                    	?: throw IllegalArgumentException("'orderItems cannot be null")
                    SLOT_SHIPMENTS ->
                    	this.shipments = value as List<ShipmentAnnotated>?
                    	?: throw IllegalArgumentException("'shipments cannot be null")
                    SLOT_INVOICES ->
                    	this.invoices = value as List<InvoiceAnnotated>?
                    	?: throw IllegalArgumentException("'invoices cannot be null")
                    SLOT_PAYMENTS ->
                    	this.payments = value as List<PaymentAnnotated>?
                    	?: throw IllegalArgumentException("'payments cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "orderNo" ->
                    	this.orderNo = value as String?
                    	?: throw IllegalArgumentException("'orderNo cannot be null")
                    "customerName" ->
                    	this.customerName = value as String?
                    	?: throw IllegalArgumentException("'customerName cannot be null")
                    "totalAmount" ->
                    	this.totalAmount = value as BigDecimal?
                    	?: throw IllegalArgumentException("'totalAmount cannot be null")
                    "orderDate" ->
                    	this.orderDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'orderDate cannot be null")
                    "deliveryDate" ->
                    	this.deliveryDate = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'deliveryDate cannot be null")
                    "status" ->
                    	this.status = value as SalesOrderStatus?
                    	?: throw IllegalArgumentException("'status cannot be null")
                    "priority" ->
                    	this.priority = value as Priority?
                    	?: throw IllegalArgumentException("'priority cannot be null")
                    "paymentStatus" ->
                    	this.paymentStatus = value as PaymentStatus?
                    	?: throw IllegalArgumentException("'paymentStatus cannot be null")
                    "invoiceType" ->
                    	this.invoiceType = value as InvoiceType?
                    "remark" ->
                    	this.remark = value as String?
                    "orderItems" ->
                    	this.orderItems = value as List<SalesOrderItemAnnotated>?
                    	?: throw IllegalArgumentException("'orderItems cannot be null")
                    "shipments" ->
                    	this.shipments = value as List<ShipmentAnnotated>?
                    	?: throw IllegalArgumentException("'shipments cannot be null")
                    "invoices" ->
                    	this.invoices = value as List<InvoiceAnnotated>?
                    	?: throw IllegalArgumentException("'invoices cannot be null")
                    "payments" ->
                    	this.payments = value as List<PaymentAnnotated>?
                    	?: throw IllegalArgumentException("'payments cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(28).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_ORDER_NO ->
                    	__visibility.show(SLOT_ORDER_NO, visible)
                    SLOT_CUSTOMER_NAME ->
                    	__visibility.show(SLOT_CUSTOMER_NAME, visible)
                    SLOT_TOTAL_AMOUNT ->
                    	__visibility.show(SLOT_TOTAL_AMOUNT, visible)
                    SLOT_ORDER_DATE ->
                    	__visibility.show(SLOT_ORDER_DATE, visible)
                    SLOT_DELIVERY_DATE ->
                    	__visibility.show(SLOT_DELIVERY_DATE, visible)
                    SLOT_STATUS ->
                    	__visibility.show(SLOT_STATUS, visible)
                    SLOT_PRIORITY ->
                    	__visibility.show(SLOT_PRIORITY, visible)
                    SLOT_PAYMENT_STATUS ->
                    	__visibility.show(SLOT_PAYMENT_STATUS, visible)
                    SLOT_INVOICE_TYPE ->
                    	__visibility.show(SLOT_INVOICE_TYPE, visible)
                    SLOT_REMARK ->
                    	__visibility.show(SLOT_REMARK, visible)
                    SLOT_ORDER_ITEMS ->
                    	__visibility.show(SLOT_ORDER_ITEMS, visible)
                    SLOT_SHIPMENTS ->
                    	__visibility.show(SLOT_SHIPMENTS, visible)
                    SLOT_INVOICES ->
                    	__visibility.show(SLOT_INVOICES, visible)
                    SLOT_PAYMENTS ->
                    	__visibility.show(SLOT_PAYMENTS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(28).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "orderNo" ->
                    	__visibility.show(SLOT_ORDER_NO, visible)
                    "customerName" ->
                    	__visibility.show(SLOT_CUSTOMER_NAME, visible)
                    "totalAmount" ->
                    	__visibility.show(SLOT_TOTAL_AMOUNT, visible)
                    "orderDate" ->
                    	__visibility.show(SLOT_ORDER_DATE, visible)
                    "deliveryDate" ->
                    	__visibility.show(SLOT_DELIVERY_DATE, visible)
                    "status" ->
                    	__visibility.show(SLOT_STATUS, visible)
                    "priority" ->
                    	__visibility.show(SLOT_PRIORITY, visible)
                    "paymentStatus" ->
                    	__visibility.show(SLOT_PAYMENT_STATUS, visible)
                    "invoiceType" ->
                    	__visibility.show(SLOT_INVOICE_TYPE, visible)
                    "remark" ->
                    	__visibility.show(SLOT_REMARK, visible)
                    "orderItems" ->
                    	__visibility.show(SLOT_ORDER_ITEMS, visible)
                    "shipments" ->
                    	__visibility.show(SLOT_SHIPMENTS, visible)
                    "invoices" ->
                    	__visibility.show(SLOT_INVOICES, visible)
                    "payments" ->
                    	__visibility.show(SLOT_PAYMENTS, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_ORDER_ITEMS))) {
                            val oldValue = base!!.orderItems
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_SHIPMENTS))) {
                            val oldValue = base!!.shipments
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_INVOICES))) {
                            val oldValue = base!!.invoices
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_PAYMENTS))) {
                            val oldValue = base!!.payments
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__orderItemsValue = NonSharedList.of(__tmpModified.__orderItemsValue, __ctx.resolveList(__tmpModified.__orderItemsValue))
                        __tmpModified.__shipmentsValue = NonSharedList.of(__tmpModified.__shipmentsValue, __ctx.resolveList(__tmpModified.__shipmentsValue))
                        __tmpModified.__invoicesValue = NonSharedList.of(__tmpModified.__invoicesValue, __ctx.resolveList(__tmpModified.__invoicesValue))
                        __tmpModified.__paymentsValue = NonSharedList.of(__tmpModified.__paymentsValue, __ctx.resolveList(__tmpModified.__paymentsValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = SalesOrderAnnotated::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: SalesOrderAnnotated?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun orderNo(orderNo: String?): Builder {
            if (orderNo !== null) {
                __draft.orderNo = orderNo
                __draft.__show(PropId.byIndex(`$`.SLOT_ORDER_NO), true)
            }
            return this
        }

        public fun customerName(customerName: String?): Builder {
            if (customerName !== null) {
                __draft.customerName = customerName
                __draft.__show(PropId.byIndex(`$`.SLOT_CUSTOMER_NAME), true)
            }
            return this
        }

        public fun totalAmount(totalAmount: BigDecimal?): Builder {
            if (totalAmount !== null) {
                __draft.totalAmount = totalAmount
                __draft.__show(PropId.byIndex(`$`.SLOT_TOTAL_AMOUNT), true)
            }
            return this
        }

        public fun orderDate(orderDate: LocalDateTime?): Builder {
            if (orderDate !== null) {
                __draft.orderDate = orderDate
                __draft.__show(PropId.byIndex(`$`.SLOT_ORDER_DATE), true)
            }
            return this
        }

        public fun deliveryDate(deliveryDate: LocalDateTime?): Builder {
            if (deliveryDate !== null) {
                __draft.deliveryDate = deliveryDate
                __draft.__show(PropId.byIndex(`$`.SLOT_DELIVERY_DATE), true)
            }
            return this
        }

        public fun status(status: SalesOrderStatus?): Builder {
            if (status !== null) {
                __draft.status = status
                __draft.__show(PropId.byIndex(`$`.SLOT_STATUS), true)
            }
            return this
        }

        public fun priority(priority: Priority?): Builder {
            if (priority !== null) {
                __draft.priority = priority
                __draft.__show(PropId.byIndex(`$`.SLOT_PRIORITY), true)
            }
            return this
        }

        public fun paymentStatus(paymentStatus: PaymentStatus?): Builder {
            if (paymentStatus !== null) {
                __draft.paymentStatus = paymentStatus
                __draft.__show(PropId.byIndex(`$`.SLOT_PAYMENT_STATUS), true)
            }
            return this
        }

        public fun invoiceType(invoiceType: InvoiceType?): Builder {
            __draft.invoiceType = invoiceType
            __draft.__show(PropId.byIndex(`$`.SLOT_INVOICE_TYPE), true)
            return this
        }

        public fun remark(remark: String?): Builder {
            __draft.remark = remark
            __draft.__show(PropId.byIndex(`$`.SLOT_REMARK), true)
            return this
        }

        public fun orderItems(orderItems: List<SalesOrderItemAnnotated>?): Builder {
            if (orderItems !== null) {
                __draft.orderItems = orderItems
                __draft.__show(PropId.byIndex(`$`.SLOT_ORDER_ITEMS), true)
            }
            return this
        }

        public fun shipments(shipments: List<ShipmentAnnotated>?): Builder {
            if (shipments !== null) {
                __draft.shipments = shipments
                __draft.__show(PropId.byIndex(`$`.SLOT_SHIPMENTS), true)
            }
            return this
        }

        public fun invoices(invoices: List<InvoiceAnnotated>?): Builder {
            if (invoices !== null) {
                __draft.invoices = invoices
                __draft.__show(PropId.byIndex(`$`.SLOT_INVOICES), true)
            }
            return this
        }

        public fun payments(payments: List<PaymentAnnotated>?): Builder {
            if (payments !== null) {
                __draft.payments = payments
                __draft.__show(PropId.byIndex(`$`.SLOT_PAYMENTS), true)
            }
            return this
        }

        public fun build(): SalesOrderAnnotated = __draft.__unwrap() as SalesOrderAnnotated
    }
}

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun ImmutableCreator<SalesOrderAnnotated>.`by`(resolveImmediately: Boolean = false, block: SalesOrderAnnotatedDraft.() -> Unit): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun ImmutableCreator<SalesOrderAnnotated>.`by`(base: SalesOrderAnnotated?, resolveImmediately: Boolean = false): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun ImmutableCreator<SalesOrderAnnotated>.`by`(
    base: SalesOrderAnnotated?,
    resolveImmediately: Boolean = false,
    block: SalesOrderAnnotatedDraft.() -> Unit,
): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun SalesOrderAnnotated(resolveImmediately: Boolean = false, block: SalesOrderAnnotatedDraft.() -> Unit): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun SalesOrderAnnotated(
    base: SalesOrderAnnotated?,
    resolveImmediately: Boolean = false,
    block: SalesOrderAnnotatedDraft.() -> Unit,
): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun MutableList<SalesOrderAnnotatedDraft>.addBy(resolveImmediately: Boolean = false, block: SalesOrderAnnotatedDraft.() -> Unit): MutableList<SalesOrderAnnotatedDraft> {
    add(SalesOrderAnnotatedDraft.`$`.produce(null, resolveImmediately, block) as SalesOrderAnnotatedDraft)
    return this
}

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun MutableList<SalesOrderAnnotatedDraft>.addBy(base: SalesOrderAnnotated?, resolveImmediately: Boolean = false): MutableList<SalesOrderAnnotatedDraft> {
    add(SalesOrderAnnotatedDraft.`$`.produce(base, resolveImmediately) as SalesOrderAnnotatedDraft)
    return this
}

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun MutableList<SalesOrderAnnotatedDraft>.addBy(
    base: SalesOrderAnnotated?,
    resolveImmediately: Boolean = false,
    block: SalesOrderAnnotatedDraft.() -> Unit,
): MutableList<SalesOrderAnnotatedDraft> {
    add(SalesOrderAnnotatedDraft.`$`.produce(base, resolveImmediately, block) as SalesOrderAnnotatedDraft)
    return this
}

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun SalesOrderAnnotated.copy(resolveImmediately: Boolean = false, block: SalesOrderAnnotatedDraft.() -> Unit): SalesOrderAnnotated = SalesOrderAnnotatedDraft.`$`.produce(this, resolveImmediately, block)
