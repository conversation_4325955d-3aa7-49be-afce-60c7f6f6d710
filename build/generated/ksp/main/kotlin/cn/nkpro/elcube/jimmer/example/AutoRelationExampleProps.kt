@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderAuto::class)

package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.enums.InvoiceType
import cn.nkpro.elcube.jimmer.enums.PaymentStatus
import cn.nkpro.elcube.jimmer.enums.Priority
import cn.nkpro.elcube.jimmer.enums.SalesOrderStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<SalesOrderAuto>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAuto>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAuto>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAuto>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAuto>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAuto>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<SalesOrderAuto>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAuto>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Int>(SalesOrderAutoProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<SalesOrderAuto>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Int>(SalesOrderAutoProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<SalesOrderAuto>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Boolean>(SalesOrderAutoProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<SalesOrderAuto>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Boolean>(SalesOrderAutoProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<SalesOrderAuto>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAuto>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Int>(SalesOrderAutoProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<SalesOrderAuto>.orderNo: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.ORDER_NO.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAuto>.orderNo: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.ORDER_NO.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAuto>.customerName: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.CUSTOMER_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAuto>.customerName: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.CUSTOMER_NAME.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAuto>.totalAmount: KNonNullPropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<BigDecimal>(SalesOrderAutoProps.TOTAL_AMOUNT.unwrap()) as KNonNullPropExpression<BigDecimal>

public val KNullableProps<SalesOrderAuto>.totalAmount: KNullablePropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<BigDecimal>(SalesOrderAutoProps.TOTAL_AMOUNT.unwrap()) as KNullablePropExpression<BigDecimal>

public val KNonNullProps<SalesOrderAuto>.orderDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.ORDER_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAuto>.orderDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.ORDER_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAuto>.deliveryDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.DELIVERY_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAuto>.deliveryDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<LocalDateTime>(SalesOrderAutoProps.DELIVERY_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAuto>.status: KNonNullPropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<SalesOrderStatus>(SalesOrderAutoProps.STATUS.unwrap()) as KNonNullPropExpression<SalesOrderStatus>

public val KNullableProps<SalesOrderAuto>.status: KNullablePropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<SalesOrderStatus>(SalesOrderAutoProps.STATUS.unwrap()) as KNullablePropExpression<SalesOrderStatus>

public val KNonNullProps<SalesOrderAuto>.priority: KNonNullPropExpression<Priority>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Priority>(SalesOrderAutoProps.PRIORITY.unwrap()) as KNonNullPropExpression<Priority>

public val KNullableProps<SalesOrderAuto>.priority: KNullablePropExpression<Priority>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<Priority>(SalesOrderAutoProps.PRIORITY.unwrap()) as KNullablePropExpression<Priority>

public val KNonNullProps<SalesOrderAuto>.paymentStatus: KNonNullPropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<PaymentStatus>(SalesOrderAutoProps.PAYMENT_STATUS.unwrap()) as KNonNullPropExpression<PaymentStatus>

public val KNullableProps<SalesOrderAuto>.paymentStatus: KNullablePropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<PaymentStatus>(SalesOrderAutoProps.PAYMENT_STATUS.unwrap()) as KNullablePropExpression<PaymentStatus>

public val KProps<SalesOrderAuto>.invoiceType: KNullablePropExpression<InvoiceType>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<InvoiceType>(SalesOrderAutoProps.INVOICE_TYPE.unwrap()) as KNullablePropExpression<InvoiceType>

public val KProps<SalesOrderAuto>.remark: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = get<String>(SalesOrderAutoProps.REMARK.unwrap()) as KNullablePropExpression<String>

public fun KProps<SalesOrderAuto>.orderItems(block: KImplicitSubQueryTable<SalesOrderItemAuto>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAutoProps.ORDER_ITEMS.unwrap(), block)

public val KTableEx<SalesOrderAuto>.orderItems: KNonNullTableEx<SalesOrderItemAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = join(SalesOrderAutoProps.ORDER_ITEMS.unwrap())

public val KTableEx<SalesOrderAuto>.`orderItems?`: KNullableTableEx<SalesOrderItemAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = outerJoin(SalesOrderAutoProps.ORDER_ITEMS.unwrap())

public fun KProps<SalesOrderAuto>.shipments(block: KImplicitSubQueryTable<ShipmentAuto>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAutoProps.SHIPMENTS.unwrap(), block)

public val KTableEx<SalesOrderAuto>.shipments: KNonNullTableEx<ShipmentAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = join(SalesOrderAutoProps.SHIPMENTS.unwrap())

public val KTableEx<SalesOrderAuto>.`shipments?`: KNullableTableEx<ShipmentAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = outerJoin(SalesOrderAutoProps.SHIPMENTS.unwrap())

public fun KProps<SalesOrderAuto>.invoices(block: KImplicitSubQueryTable<InvoiceAuto>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAutoProps.INVOICES.unwrap(), block)

public val KTableEx<SalesOrderAuto>.invoices: KNonNullTableEx<InvoiceAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = join(SalesOrderAutoProps.INVOICES.unwrap())

public val KTableEx<SalesOrderAuto>.`invoices?`: KNullableTableEx<InvoiceAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = outerJoin(SalesOrderAutoProps.INVOICES.unwrap())

public fun KProps<SalesOrderAuto>.payments(block: KImplicitSubQueryTable<PaymentAuto>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAutoProps.PAYMENTS.unwrap(), block)

public val KTableEx<SalesOrderAuto>.payments: KNonNullTableEx<PaymentAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = join(SalesOrderAutoProps.PAYMENTS.unwrap())

public val KTableEx<SalesOrderAuto>.`payments?`: KNullableTableEx<PaymentAuto>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = outerJoin(SalesOrderAutoProps.PAYMENTS.unwrap())

public val KRemoteRef.NonNull<SalesOrderAuto>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<SalesOrderAuto>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAuto::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = SalesOrderAuto::class)
public fun KNonNullTable<SalesOrderAuto>.fetchBy(block: SalesOrderAutoFetcherDsl.() -> Unit): Selection<SalesOrderAuto> = fetch(newFetcher(SalesOrderAuto::class).`by`(block))

@GeneratedBy(type = SalesOrderAuto::class)
public fun KNullableTable<SalesOrderAuto>.fetchBy(block: SalesOrderAutoFetcherDsl.() -> Unit): Selection<SalesOrderAuto?> = fetch(newFetcher(SalesOrderAuto::class).`by`(block))

@GeneratedBy(type = SalesOrderAuto::class)
public object SalesOrderAutoProps {
    public val ID: TypedProp.Scalar<SalesOrderAuto, String> =
            TypedProp.scalar(SalesOrderAuto::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<SalesOrderAuto, LocalDateTime> =
            TypedProp.scalar(SalesOrderAuto::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<SalesOrderAuto, LocalDateTime> =
            TypedProp.scalar(SalesOrderAuto::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<SalesOrderAuto, Int> =
            TypedProp.scalar(SalesOrderAuto::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<SalesOrderAuto, Boolean> =
            TypedProp.scalar(SalesOrderAuto::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<SalesOrderAuto, Int?> =
            TypedProp.scalar(SalesOrderAuto::sortOrder.toImmutableProp())

    public val ORDER_NO: TypedProp.Scalar<SalesOrderAuto, String> =
            TypedProp.scalar(SalesOrderAuto::orderNo.toImmutableProp())

    public val CUSTOMER_NAME: TypedProp.Scalar<SalesOrderAuto, String> =
            TypedProp.scalar(SalesOrderAuto::customerName.toImmutableProp())

    public val TOTAL_AMOUNT: TypedProp.Scalar<SalesOrderAuto, BigDecimal> =
            TypedProp.scalar(SalesOrderAuto::totalAmount.toImmutableProp())

    public val ORDER_DATE: TypedProp.Scalar<SalesOrderAuto, LocalDateTime> =
            TypedProp.scalar(SalesOrderAuto::orderDate.toImmutableProp())

    public val DELIVERY_DATE: TypedProp.Scalar<SalesOrderAuto, LocalDateTime> =
            TypedProp.scalar(SalesOrderAuto::deliveryDate.toImmutableProp())

    public val STATUS: TypedProp.Scalar<SalesOrderAuto, SalesOrderStatus> =
            TypedProp.scalar(SalesOrderAuto::status.toImmutableProp())

    public val PRIORITY: TypedProp.Scalar<SalesOrderAuto, Priority> =
            TypedProp.scalar(SalesOrderAuto::priority.toImmutableProp())

    public val PAYMENT_STATUS: TypedProp.Scalar<SalesOrderAuto, PaymentStatus> =
            TypedProp.scalar(SalesOrderAuto::paymentStatus.toImmutableProp())

    public val INVOICE_TYPE: TypedProp.Scalar<SalesOrderAuto, InvoiceType?> =
            TypedProp.scalar(SalesOrderAuto::invoiceType.toImmutableProp())

    public val REMARK: TypedProp.Scalar<SalesOrderAuto, String?> =
            TypedProp.scalar(SalesOrderAuto::remark.toImmutableProp())

    public val ORDER_ITEMS: TypedProp.ReferenceList<SalesOrderAuto, SalesOrderItemAuto> =
            TypedProp.referenceList(SalesOrderAuto::orderItems.toImmutableProp())

    public val SHIPMENTS: TypedProp.ReferenceList<SalesOrderAuto, ShipmentAuto> =
            TypedProp.referenceList(SalesOrderAuto::shipments.toImmutableProp())

    public val INVOICES: TypedProp.ReferenceList<SalesOrderAuto, InvoiceAuto> =
            TypedProp.referenceList(SalesOrderAuto::invoices.toImmutableProp())

    public val PAYMENTS: TypedProp.ReferenceList<SalesOrderAuto, PaymentAuto> =
            TypedProp.referenceList(SalesOrderAuto::payments.toImmutableProp())
}
