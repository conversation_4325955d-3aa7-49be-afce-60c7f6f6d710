@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import com.fasterxml.jackson.`annotation`.JsonIgnore
import com.fasterxml.jackson.`annotation`.JsonPropertyOrder
import java.io.Serializable
import java.lang.IllegalStateException
import java.lang.System
import java.time.LocalDateTime
import kotlin.Any
import kotlin.Boolean
import kotlin.Cloneable
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import org.babyfish.jimmer.CircularReferenceException
import org.babyfish.jimmer.DraftConsumer
import org.babyfish.jimmer.ImmutableObjects
import org.babyfish.jimmer.UnloadedException
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.jackson.ImmutableModuleRequiredException
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.kt.ImmutableCreator
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType
import org.babyfish.jimmer.meta.PropId
import org.babyfish.jimmer.runtime.DraftContext
import org.babyfish.jimmer.runtime.DraftSpi
import org.babyfish.jimmer.runtime.ImmutableSpi
import org.babyfish.jimmer.runtime.Internal
import org.babyfish.jimmer.runtime.NonSharedList
import org.babyfish.jimmer.runtime.Visibility
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany

@DslScope
@GeneratedBy(type = DocIndex::class)
public interface DocIndexDraft : DocIndex, BusinessEntityDraft {
    override var indexCode: String

    override var indexName: String

    override var indexType: String

    override var docType: DocType

    @get:JsonIgnore
    public var docTypeId: String

    override var extractExpr: String

    override var searchable: Boolean

    override var sortable: Boolean

    override var aggregatable: Boolean

    override var indexData: List<DocumentIndexData>

    public fun docType(): DocTypeDraft

    public fun docType(block: DocTypeDraft.() -> Unit)

    public fun indexData(): MutableList<DocumentIndexDataDraft>

    @GeneratedBy(type = DocIndex::class)
    public object `$` {
        public const val SLOT_ID: Int = 0

        public const val SLOT_CREATED_TIME: Int = 1

        public const val SLOT_UPDATED_TIME: Int = 2

        public const val SLOT_CREATED_BY: Int = 3

        public const val SLOT_UPDATED_BY: Int = 4

        public const val SLOT_VERSION: Int = 5

        public const val SLOT_DELETED: Int = 6

        public const val SLOT_TENANT_ID: Int = 7

        public const val SLOT_ORG_ID: Int = 8

        public const val SLOT_DEPT_ID: Int = 9

        public const val SLOT_BUSINESS_CODE: Int = 10

        public const val SLOT_BUSINESS_NAME: Int = 11

        public const val SLOT_BUSINESS_STATUS: Int = 12

        public const val SLOT_SORT_ORDER: Int = 13

        public const val SLOT_INDEX_CODE: Int = 14

        public const val SLOT_INDEX_NAME: Int = 15

        public const val SLOT_INDEX_TYPE: Int = 16

        public const val SLOT_DOC_TYPE: Int = 17

        public const val SLOT_EXTRACT_EXPR: Int = 18

        public const val SLOT_SEARCHABLE: Int = 19

        public const val SLOT_SORTABLE: Int = 20

        public const val SLOT_AGGREGATABLE: Int = 21

        public const val SLOT_INDEX_DATA: Int = 22

        public val type: ImmutableType = ImmutableType
            .newBuilder(
                "0.9.101",
                DocIndex::class,
                listOf(
                    BusinessEntityDraft.`$`.type
                ),
            ) { ctx, base ->
                DraftImpl(ctx, base as DocIndex?)
            }
            .redefine("id", SLOT_ID)
            .redefine("createdTime", SLOT_CREATED_TIME)
            .redefine("updatedTime", SLOT_UPDATED_TIME)
            .redefine("createdBy", SLOT_CREATED_BY)
            .redefine("updatedBy", SLOT_UPDATED_BY)
            .redefine("version", SLOT_VERSION)
            .redefine("deleted", SLOT_DELETED)
            .redefine("tenantId", SLOT_TENANT_ID)
            .redefine("orgId", SLOT_ORG_ID)
            .redefine("deptId", SLOT_DEPT_ID)
            .redefine("businessCode", SLOT_BUSINESS_CODE)
            .redefine("businessName", SLOT_BUSINESS_NAME)
            .redefine("businessStatus", SLOT_BUSINESS_STATUS)
            .redefine("sortOrder", SLOT_SORT_ORDER)
            .key(SLOT_INDEX_CODE, "indexCode", String::class.java, false)
            .add(SLOT_INDEX_NAME, "indexName", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_INDEX_TYPE, "indexType", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_DOC_TYPE, "docType", ManyToOne::class.java, DocType::class.java, false)
            .add(SLOT_EXTRACT_EXPR, "extractExpr", ImmutablePropCategory.SCALAR, String::class.java, false)
            .add(SLOT_SEARCHABLE, "searchable", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_SORTABLE, "sortable", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_AGGREGATABLE, "aggregatable", ImmutablePropCategory.SCALAR, Boolean::class.java, false)
            .add(SLOT_INDEX_DATA, "indexData", OneToMany::class.java, DocumentIndexData::class.java, false)
            .build()

        public fun produce(base: DocIndex? = null, resolveImmediately: Boolean = false): DocIndex {
            val consumer = DraftConsumer<DocIndexDraft> {}
            return Internal.produce(type, base, resolveImmediately, consumer) as DocIndex
        }

        public fun produce(
            base: DocIndex? = null,
            resolveImmediately: Boolean = false,
            block: DocIndexDraft.() -> Unit,
        ): DocIndex {
            val consumer = DraftConsumer<DocIndexDraft> { block(it) }
            return Internal.produce(type, base, resolveImmediately, consumer) as DocIndex
        }

        @GeneratedBy(type = DocIndex::class)
        @JsonPropertyOrder("dummyPropForJacksonError__", "id", "createdTime", "updatedTime", "createdBy", "updatedBy", "version", "deleted", "tenantId", "orgId", "deptId", "businessCode", "businessName", "businessStatus", "sortOrder", "indexCode", "indexName", "indexType", "docType", "extractExpr", "searchable", "sortable", "aggregatable", "indexData")
        private abstract interface Implementor : DocIndex, ImmutableSpi {
            public val dummyPropForJacksonError__: Int
                get() = throw ImmutableModuleRequiredException()

            override fun __get(prop: PropId): Any? = when (prop.asIndex()) {
                -1 ->
                	__get(prop.asName())
                SLOT_ID ->
                	id
                SLOT_CREATED_TIME ->
                	createdTime
                SLOT_UPDATED_TIME ->
                	updatedTime
                SLOT_CREATED_BY ->
                	createdBy
                SLOT_UPDATED_BY ->
                	updatedBy
                SLOT_VERSION ->
                	version
                SLOT_DELETED ->
                	deleted
                SLOT_TENANT_ID ->
                	tenantId
                SLOT_ORG_ID ->
                	orgId
                SLOT_DEPT_ID ->
                	deptId
                SLOT_BUSINESS_CODE ->
                	businessCode
                SLOT_BUSINESS_NAME ->
                	businessName
                SLOT_BUSINESS_STATUS ->
                	businessStatus
                SLOT_SORT_ORDER ->
                	sortOrder
                SLOT_INDEX_CODE ->
                	indexCode
                SLOT_INDEX_NAME ->
                	indexName
                SLOT_INDEX_TYPE ->
                	indexType
                SLOT_DOC_TYPE ->
                	docType
                SLOT_EXTRACT_EXPR ->
                	extractExpr
                SLOT_SEARCHABLE ->
                	searchable
                SLOT_SORTABLE ->
                	sortable
                SLOT_AGGREGATABLE ->
                	aggregatable
                SLOT_INDEX_DATA ->
                	indexData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                    prop
                )

            }

            override fun __get(prop: String): Any? = when (prop) {
                "id" ->
                	id
                "createdTime" ->
                	createdTime
                "updatedTime" ->
                	updatedTime
                "createdBy" ->
                	createdBy
                "updatedBy" ->
                	updatedBy
                "version" ->
                	version
                "deleted" ->
                	deleted
                "tenantId" ->
                	tenantId
                "orgId" ->
                	orgId
                "deptId" ->
                	deptId
                "businessCode" ->
                	businessCode
                "businessName" ->
                	businessName
                "businessStatus" ->
                	businessStatus
                "sortOrder" ->
                	sortOrder
                "indexCode" ->
                	indexCode
                "indexName" ->
                	indexName
                "indexType" ->
                	indexType
                "docType" ->
                	docType
                "extractExpr" ->
                	extractExpr
                "searchable" ->
                	searchable
                "sortable" ->
                	sortable
                "aggregatable" ->
                	aggregatable
                "indexData" ->
                	indexData
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                    prop
                )

            }

            override fun __type(): ImmutableType = `$`.type
        }

        @GeneratedBy(type = DocIndex::class)
        private class Impl : Implementor, Cloneable, Serializable {
            @get:JsonIgnore
            internal var __visibility: Visibility? = null

            @get:JsonIgnore
            internal var __idValue: String? = null

            @get:JsonIgnore
            internal var __createdTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __updatedTimeValue: LocalDateTime? = null

            @get:JsonIgnore
            internal var __createdByValue: String? = null

            @get:JsonIgnore
            internal var __createdByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __updatedByValue: String? = null

            @get:JsonIgnore
            internal var __updatedByLoaded: Boolean = false

            @get:JsonIgnore
            internal var __versionValue: Int = 0

            @get:JsonIgnore
            internal var __versionLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deletedValue: Boolean = false

            @get:JsonIgnore
            internal var __deletedLoaded: Boolean = false

            @get:JsonIgnore
            internal var __tenantIdValue: String? = null

            @get:JsonIgnore
            internal var __tenantIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __orgIdValue: String? = null

            @get:JsonIgnore
            internal var __orgIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __deptIdValue: String? = null

            @get:JsonIgnore
            internal var __deptIdLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessCodeValue: String? = null

            @get:JsonIgnore
            internal var __businessCodeLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessNameValue: String? = null

            @get:JsonIgnore
            internal var __businessNameLoaded: Boolean = false

            @get:JsonIgnore
            internal var __businessStatusValue: String? = null

            @get:JsonIgnore
            internal var __businessStatusLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortOrderValue: Int? = null

            @get:JsonIgnore
            internal var __sortOrderLoaded: Boolean = false

            @get:JsonIgnore
            internal var __indexCodeValue: String? = null

            @get:JsonIgnore
            internal var __indexNameValue: String? = null

            @get:JsonIgnore
            internal var __indexTypeValue: String? = null

            @get:JsonIgnore
            internal var __docTypeValue: DocType? = null

            @get:JsonIgnore
            internal var __extractExprValue: String? = null

            @get:JsonIgnore
            internal var __searchableValue: Boolean = false

            @get:JsonIgnore
            internal var __searchableLoaded: Boolean = false

            @get:JsonIgnore
            internal var __sortableValue: Boolean = false

            @get:JsonIgnore
            internal var __sortableLoaded: Boolean = false

            @get:JsonIgnore
            internal var __aggregatableValue: Boolean = false

            @get:JsonIgnore
            internal var __aggregatableLoaded: Boolean = false

            @get:JsonIgnore
            internal var __indexDataValue: NonSharedList<DocumentIndexData>? = null

            override val id: String
                get() {
                    val __idValue = this.__idValue
                    if (__idValue === null) {
                        throw UnloadedException(DocIndex::class.java, "id")
                    }
                    return __idValue
                }

            override val createdTime: LocalDateTime
                get() {
                    val __createdTimeValue = this.__createdTimeValue
                    if (__createdTimeValue === null) {
                        throw UnloadedException(DocIndex::class.java, "createdTime")
                    }
                    return __createdTimeValue
                }

            override val updatedTime: LocalDateTime
                get() {
                    val __updatedTimeValue = this.__updatedTimeValue
                    if (__updatedTimeValue === null) {
                        throw UnloadedException(DocIndex::class.java, "updatedTime")
                    }
                    return __updatedTimeValue
                }

            override val createdBy: String?
                get() {
                    if (!__createdByLoaded) {
                        throw UnloadedException(DocIndex::class.java, "createdBy")
                    }
                    return __createdByValue
                }

            override val updatedBy: String?
                get() {
                    if (!__updatedByLoaded) {
                        throw UnloadedException(DocIndex::class.java, "updatedBy")
                    }
                    return __updatedByValue
                }

            override val version: Int
                get() {
                    if (!__versionLoaded) {
                        throw UnloadedException(DocIndex::class.java, "version")
                    }
                    return __versionValue
                }

            override val deleted: Boolean
                get() {
                    if (!__deletedLoaded) {
                        throw UnloadedException(DocIndex::class.java, "deleted")
                    }
                    return __deletedValue
                }

            override val tenantId: String?
                get() {
                    if (!__tenantIdLoaded) {
                        throw UnloadedException(DocIndex::class.java, "tenantId")
                    }
                    return __tenantIdValue
                }

            override val orgId: String?
                get() {
                    if (!__orgIdLoaded) {
                        throw UnloadedException(DocIndex::class.java, "orgId")
                    }
                    return __orgIdValue
                }

            override val deptId: String?
                get() {
                    if (!__deptIdLoaded) {
                        throw UnloadedException(DocIndex::class.java, "deptId")
                    }
                    return __deptIdValue
                }

            override val businessCode: String?
                get() {
                    if (!__businessCodeLoaded) {
                        throw UnloadedException(DocIndex::class.java, "businessCode")
                    }
                    return __businessCodeValue
                }

            override val businessName: String?
                get() {
                    if (!__businessNameLoaded) {
                        throw UnloadedException(DocIndex::class.java, "businessName")
                    }
                    return __businessNameValue
                }

            override val businessStatus: String?
                get() {
                    if (!__businessStatusLoaded) {
                        throw UnloadedException(DocIndex::class.java, "businessStatus")
                    }
                    return __businessStatusValue
                }

            override val sortOrder: Int?
                get() {
                    if (!__sortOrderLoaded) {
                        throw UnloadedException(DocIndex::class.java, "sortOrder")
                    }
                    return __sortOrderValue
                }

            override val indexCode: String
                get() {
                    val __indexCodeValue = this.__indexCodeValue
                    if (__indexCodeValue === null) {
                        throw UnloadedException(DocIndex::class.java, "indexCode")
                    }
                    return __indexCodeValue
                }

            override val indexName: String
                get() {
                    val __indexNameValue = this.__indexNameValue
                    if (__indexNameValue === null) {
                        throw UnloadedException(DocIndex::class.java, "indexName")
                    }
                    return __indexNameValue
                }

            override val indexType: String
                get() {
                    val __indexTypeValue = this.__indexTypeValue
                    if (__indexTypeValue === null) {
                        throw UnloadedException(DocIndex::class.java, "indexType")
                    }
                    return __indexTypeValue
                }

            override val docType: DocType
                get() {
                    val __docTypeValue = this.__docTypeValue
                    if (__docTypeValue === null) {
                        throw UnloadedException(DocIndex::class.java, "docType")
                    }
                    return __docTypeValue
                }

            override val extractExpr: String
                get() {
                    val __extractExprValue = this.__extractExprValue
                    if (__extractExprValue === null) {
                        throw UnloadedException(DocIndex::class.java, "extractExpr")
                    }
                    return __extractExprValue
                }

            override val searchable: Boolean
                get() {
                    if (!__searchableLoaded) {
                        throw UnloadedException(DocIndex::class.java, "searchable")
                    }
                    return __searchableValue
                }

            override val sortable: Boolean
                get() {
                    if (!__sortableLoaded) {
                        throw UnloadedException(DocIndex::class.java, "sortable")
                    }
                    return __sortableValue
                }

            override val aggregatable: Boolean
                get() {
                    if (!__aggregatableLoaded) {
                        throw UnloadedException(DocIndex::class.java, "aggregatable")
                    }
                    return __aggregatableValue
                }

            override val indexData: List<DocumentIndexData>
                get() {
                    val __indexDataValue = this.__indexDataValue
                    if (__indexDataValue === null) {
                        throw UnloadedException(DocIndex::class.java, "indexData")
                    }
                    return __indexDataValue
                }

            public override fun clone(): Impl = super.clone() as Impl

            override fun __isLoaded(prop: PropId): Boolean = when (prop.asIndex()) {
                -1 ->
                	__isLoaded(prop.asName())
                SLOT_ID ->
                	__idValue !== null
                SLOT_CREATED_TIME ->
                	__createdTimeValue !== null
                SLOT_UPDATED_TIME ->
                	__updatedTimeValue !== null
                SLOT_CREATED_BY ->
                	__createdByLoaded
                SLOT_UPDATED_BY ->
                	__updatedByLoaded
                SLOT_VERSION ->
                	__versionLoaded
                SLOT_DELETED ->
                	__deletedLoaded
                SLOT_TENANT_ID ->
                	__tenantIdLoaded
                SLOT_ORG_ID ->
                	__orgIdLoaded
                SLOT_DEPT_ID ->
                	__deptIdLoaded
                SLOT_BUSINESS_CODE ->
                	__businessCodeLoaded
                SLOT_BUSINESS_NAME ->
                	__businessNameLoaded
                SLOT_BUSINESS_STATUS ->
                	__businessStatusLoaded
                SLOT_SORT_ORDER ->
                	__sortOrderLoaded
                SLOT_INDEX_CODE ->
                	__indexCodeValue !== null
                SLOT_INDEX_NAME ->
                	__indexNameValue !== null
                SLOT_INDEX_TYPE ->
                	__indexTypeValue !== null
                SLOT_DOC_TYPE ->
                	__docTypeValue !== null
                SLOT_EXTRACT_EXPR ->
                	__extractExprValue !== null
                SLOT_SEARCHABLE ->
                	__searchableLoaded
                SLOT_SORTABLE ->
                	__sortableLoaded
                SLOT_AGGREGATABLE ->
                	__aggregatableLoaded
                SLOT_INDEX_DATA ->
                	__indexDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                    prop
                )

            }

            override fun __isLoaded(prop: String): Boolean = when (prop) {
                "id" ->
                	__idValue !== null
                "createdTime" ->
                	__createdTimeValue !== null
                "updatedTime" ->
                	__updatedTimeValue !== null
                "createdBy" ->
                	__createdByLoaded
                "updatedBy" ->
                	__updatedByLoaded
                "version" ->
                	__versionLoaded
                "deleted" ->
                	__deletedLoaded
                "tenantId" ->
                	__tenantIdLoaded
                "orgId" ->
                	__orgIdLoaded
                "deptId" ->
                	__deptIdLoaded
                "businessCode" ->
                	__businessCodeLoaded
                "businessName" ->
                	__businessNameLoaded
                "businessStatus" ->
                	__businessStatusLoaded
                "sortOrder" ->
                	__sortOrderLoaded
                "indexCode" ->
                	__indexCodeValue !== null
                "indexName" ->
                	__indexNameValue !== null
                "indexType" ->
                	__indexTypeValue !== null
                "docType" ->
                	__docTypeValue !== null
                "extractExpr" ->
                	__extractExprValue !== null
                "searchable" ->
                	__searchableLoaded
                "sortable" ->
                	__sortableLoaded
                "aggregatable" ->
                	__aggregatableLoaded
                "indexData" ->
                	__indexDataValue !== null
                else -> throw IllegalArgumentException(
                    "Illegal property name" + 
                    " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                    prop
                )

            }

            override fun __isVisible(prop: PropId): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop.asIndex()) {
                    -1 ->
                    	__isVisible(prop.asName())
                    SLOT_ID ->
                    	__visibility.visible(SLOT_ID)
                    SLOT_CREATED_TIME ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    SLOT_UPDATED_TIME ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    SLOT_CREATED_BY ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    SLOT_UPDATED_BY ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    SLOT_VERSION ->
                    	__visibility.visible(SLOT_VERSION)
                    SLOT_DELETED ->
                    	__visibility.visible(SLOT_DELETED)
                    SLOT_TENANT_ID ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    SLOT_ORG_ID ->
                    	__visibility.visible(SLOT_ORG_ID)
                    SLOT_DEPT_ID ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    SLOT_SORT_ORDER ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    SLOT_INDEX_CODE ->
                    	__visibility.visible(SLOT_INDEX_CODE)
                    SLOT_INDEX_NAME ->
                    	__visibility.visible(SLOT_INDEX_NAME)
                    SLOT_INDEX_TYPE ->
                    	__visibility.visible(SLOT_INDEX_TYPE)
                    SLOT_DOC_TYPE ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    SLOT_EXTRACT_EXPR ->
                    	__visibility.visible(SLOT_EXTRACT_EXPR)
                    SLOT_SEARCHABLE ->
                    	__visibility.visible(SLOT_SEARCHABLE)
                    SLOT_SORTABLE ->
                    	__visibility.visible(SLOT_SORTABLE)
                    SLOT_AGGREGATABLE ->
                    	__visibility.visible(SLOT_AGGREGATABLE)
                    SLOT_INDEX_DATA ->
                    	__visibility.visible(SLOT_INDEX_DATA)
                    else -> true
                }
            }

            override fun __isVisible(prop: String): Boolean {
                val __visibility = this.__visibility ?: return true
                return when (prop) {
                    "id" ->
                    	__visibility.visible(SLOT_ID)
                    "createdTime" ->
                    	__visibility.visible(SLOT_CREATED_TIME)
                    "updatedTime" ->
                    	__visibility.visible(SLOT_UPDATED_TIME)
                    "createdBy" ->
                    	__visibility.visible(SLOT_CREATED_BY)
                    "updatedBy" ->
                    	__visibility.visible(SLOT_UPDATED_BY)
                    "version" ->
                    	__visibility.visible(SLOT_VERSION)
                    "deleted" ->
                    	__visibility.visible(SLOT_DELETED)
                    "tenantId" ->
                    	__visibility.visible(SLOT_TENANT_ID)
                    "orgId" ->
                    	__visibility.visible(SLOT_ORG_ID)
                    "deptId" ->
                    	__visibility.visible(SLOT_DEPT_ID)
                    "businessCode" ->
                    	__visibility.visible(SLOT_BUSINESS_CODE)
                    "businessName" ->
                    	__visibility.visible(SLOT_BUSINESS_NAME)
                    "businessStatus" ->
                    	__visibility.visible(SLOT_BUSINESS_STATUS)
                    "sortOrder" ->
                    	__visibility.visible(SLOT_SORT_ORDER)
                    "indexCode" ->
                    	__visibility.visible(SLOT_INDEX_CODE)
                    "indexName" ->
                    	__visibility.visible(SLOT_INDEX_NAME)
                    "indexType" ->
                    	__visibility.visible(SLOT_INDEX_TYPE)
                    "docType" ->
                    	__visibility.visible(SLOT_DOC_TYPE)
                    "extractExpr" ->
                    	__visibility.visible(SLOT_EXTRACT_EXPR)
                    "searchable" ->
                    	__visibility.visible(SLOT_SEARCHABLE)
                    "sortable" ->
                    	__visibility.visible(SLOT_SORTABLE)
                    "aggregatable" ->
                    	__visibility.visible(SLOT_AGGREGATABLE)
                    "indexData" ->
                    	__visibility.visible(SLOT_INDEX_DATA)
                    else -> true
                }
            }

            public fun __shallowHashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__indexCodeValue !== null) {
                    hash = 31 * hash + __indexCodeValue.hashCode()
                }
                if (__indexNameValue !== null) {
                    hash = 31 * hash + __indexNameValue.hashCode()
                }
                if (__indexTypeValue !== null) {
                    hash = 31 * hash + __indexTypeValue.hashCode()
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__docTypeValue)
                }
                if (__extractExprValue !== null) {
                    hash = 31 * hash + __extractExprValue.hashCode()
                }
                if (__searchableLoaded) {
                    hash = 31 * hash + __searchableValue.hashCode()
                }
                if (__sortableLoaded) {
                    hash = 31 * hash + __sortableValue.hashCode()
                }
                if (__aggregatableLoaded) {
                    hash = 31 * hash + __aggregatableValue.hashCode()
                }
                if (__indexDataValue !== null) {
                    hash = 31 * hash + System.identityHashCode(__indexDataValue)
                }
                return hash
            }

            override fun hashCode(): Int {
                var hash = __visibility?.hashCode() ?: 0
                if (__idValue !== null) {
                    hash = 31 * hash + __idValue.hashCode()
                    return hash
                }
                if (__createdTimeValue !== null) {
                    hash = 31 * hash + __createdTimeValue.hashCode()
                }
                if (__updatedTimeValue !== null) {
                    hash = 31 * hash + __updatedTimeValue.hashCode()
                }
                if (__createdByLoaded) {
                    hash = 31 * hash + (__createdByValue?.hashCode() ?: 0)
                }
                if (__updatedByLoaded) {
                    hash = 31 * hash + (__updatedByValue?.hashCode() ?: 0)
                }
                if (__versionLoaded) {
                    hash = 31 * hash + __versionValue.hashCode()
                }
                if (__deletedLoaded) {
                    hash = 31 * hash + __deletedValue.hashCode()
                }
                if (__tenantIdLoaded) {
                    hash = 31 * hash + (__tenantIdValue?.hashCode() ?: 0)
                }
                if (__orgIdLoaded) {
                    hash = 31 * hash + (__orgIdValue?.hashCode() ?: 0)
                }
                if (__deptIdLoaded) {
                    hash = 31 * hash + (__deptIdValue?.hashCode() ?: 0)
                }
                if (__businessCodeLoaded) {
                    hash = 31 * hash + (__businessCodeValue?.hashCode() ?: 0)
                }
                if (__businessNameLoaded) {
                    hash = 31 * hash + (__businessNameValue?.hashCode() ?: 0)
                }
                if (__businessStatusLoaded) {
                    hash = 31 * hash + (__businessStatusValue?.hashCode() ?: 0)
                }
                if (__sortOrderLoaded) {
                    hash = 31 * hash + (__sortOrderValue?.hashCode() ?: 0)
                }
                if (__indexCodeValue !== null) {
                    hash = 31 * hash + __indexCodeValue.hashCode()
                }
                if (__indexNameValue !== null) {
                    hash = 31 * hash + __indexNameValue.hashCode()
                }
                if (__indexTypeValue !== null) {
                    hash = 31 * hash + __indexTypeValue.hashCode()
                }
                if (__docTypeValue !== null) {
                    hash = 31 * hash + __docTypeValue.hashCode()
                }
                if (__extractExprValue !== null) {
                    hash = 31 * hash + __extractExprValue.hashCode()
                }
                if (__searchableLoaded) {
                    hash = 31 * hash + __searchableValue.hashCode()
                }
                if (__sortableLoaded) {
                    hash = 31 * hash + __sortableValue.hashCode()
                }
                if (__aggregatableLoaded) {
                    hash = 31 * hash + __aggregatableValue.hashCode()
                }
                if (__indexDataValue !== null) {
                    hash = 31 * hash + __indexDataValue.hashCode()
                }
                return hash
            }

            override fun __hashCode(shallow: Boolean): Int = if (shallow) __shallowHashCode() else hashCode()

            public fun __shallowEquals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded && this.__idValue != __other.id) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_CODE))) {
                    return false
                }
                val __indexCodeLoaded = 
                    this.__indexCodeValue !== null
                if (__indexCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_CODE)))) {
                    return false
                }
                if (__indexCodeLoaded && this.__indexCodeValue != __other.indexCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_NAME))) {
                    return false
                }
                val __indexNameLoaded = 
                    this.__indexNameValue !== null
                if (__indexNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_NAME)))) {
                    return false
                }
                if (__indexNameLoaded && this.__indexNameValue != __other.indexName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_TYPE))) {
                    return false
                }
                val __indexTypeLoaded = 
                    this.__indexTypeValue !== null
                if (__indexTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_TYPE)))) {
                    return false
                }
                if (__indexTypeLoaded && this.__indexTypeValue != __other.indexType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue !== __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXTRACT_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_EXTRACT_EXPR))) {
                    return false
                }
                val __extractExprLoaded = 
                    this.__extractExprValue !== null
                if (__extractExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXTRACT_EXPR)))) {
                    return false
                }
                if (__extractExprLoaded && this.__extractExprValue != __other.extractExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SEARCHABLE)) != __other.__isVisible(PropId.byIndex(SLOT_SEARCHABLE))) {
                    return false
                }
                val __searchableLoaded = 
                    this.__searchableLoaded
                if (__searchableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SEARCHABLE)))) {
                    return false
                }
                if (__searchableLoaded && this.__searchableValue != __other.searchable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORTABLE)) != __other.__isVisible(PropId.byIndex(SLOT_SORTABLE))) {
                    return false
                }
                val __sortableLoaded = 
                    this.__sortableLoaded
                if (__sortableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORTABLE)))) {
                    return false
                }
                if (__sortableLoaded && this.__sortableValue != __other.sortable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_AGGREGATABLE)) != __other.__isVisible(PropId.byIndex(SLOT_AGGREGATABLE))) {
                    return false
                }
                val __aggregatableLoaded = 
                    this.__aggregatableLoaded
                if (__aggregatableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_AGGREGATABLE)))) {
                    return false
                }
                if (__aggregatableLoaded && this.__aggregatableValue != __other.aggregatable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_DATA))) {
                    return false
                }
                val __indexDataLoaded = 
                    this.__indexDataValue !== null
                if (__indexDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_DATA)))) {
                    return false
                }
                if (__indexDataLoaded && this.__indexDataValue !== __other.indexData) {
                    return false
                }
                return true
            }

            override fun equals(other: Any?): Boolean {
                val __other = other as? Implementor
                if (__other === null) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ID))) {
                    return false
                }
                val __idLoaded = 
                    this.__idValue !== null
                if (__idLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ID)))) {
                    return false
                }
                if (__idLoaded) {
                    return this.__idValue == __other.id
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_TIME))) {
                    return false
                }
                val __createdTimeLoaded = 
                    this.__createdTimeValue !== null
                if (__createdTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_TIME)))) {
                    return false
                }
                if (__createdTimeLoaded && this.__createdTimeValue != __other.createdTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_TIME)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_TIME))) {
                    return false
                }
                val __updatedTimeLoaded = 
                    this.__updatedTimeValue !== null
                if (__updatedTimeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_TIME)))) {
                    return false
                }
                if (__updatedTimeLoaded && this.__updatedTimeValue != __other.updatedTime) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_CREATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_CREATED_BY))) {
                    return false
                }
                val __createdByLoaded = 
                    this.__createdByLoaded
                if (__createdByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_CREATED_BY)))) {
                    return false
                }
                if (__createdByLoaded && this.__createdByValue != __other.createdBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_UPDATED_BY)) != __other.__isVisible(PropId.byIndex(SLOT_UPDATED_BY))) {
                    return false
                }
                val __updatedByLoaded = 
                    this.__updatedByLoaded
                if (__updatedByLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_UPDATED_BY)))) {
                    return false
                }
                if (__updatedByLoaded && this.__updatedByValue != __other.updatedBy) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_VERSION)) != __other.__isVisible(PropId.byIndex(SLOT_VERSION))) {
                    return false
                }
                val __versionLoaded = 
                    this.__versionLoaded
                if (__versionLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_VERSION)))) {
                    return false
                }
                if (__versionLoaded && this.__versionValue != __other.version) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DELETED)) != __other.__isVisible(PropId.byIndex(SLOT_DELETED))) {
                    return false
                }
                val __deletedLoaded = 
                    this.__deletedLoaded
                if (__deletedLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DELETED)))) {
                    return false
                }
                if (__deletedLoaded && this.__deletedValue != __other.deleted) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_TENANT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_TENANT_ID))) {
                    return false
                }
                val __tenantIdLoaded = 
                    this.__tenantIdLoaded
                if (__tenantIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_TENANT_ID)))) {
                    return false
                }
                if (__tenantIdLoaded && this.__tenantIdValue != __other.tenantId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_ORG_ID)) != __other.__isVisible(PropId.byIndex(SLOT_ORG_ID))) {
                    return false
                }
                val __orgIdLoaded = 
                    this.__orgIdLoaded
                if (__orgIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_ORG_ID)))) {
                    return false
                }
                if (__orgIdLoaded && this.__orgIdValue != __other.orgId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DEPT_ID)) != __other.__isVisible(PropId.byIndex(SLOT_DEPT_ID))) {
                    return false
                }
                val __deptIdLoaded = 
                    this.__deptIdLoaded
                if (__deptIdLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DEPT_ID)))) {
                    return false
                }
                if (__deptIdLoaded && this.__deptIdValue != __other.deptId) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_CODE))) {
                    return false
                }
                val __businessCodeLoaded = 
                    this.__businessCodeLoaded
                if (__businessCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_CODE)))) {
                    return false
                }
                if (__businessCodeLoaded && this.__businessCodeValue != __other.businessCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_NAME))) {
                    return false
                }
                val __businessNameLoaded = 
                    this.__businessNameLoaded
                if (__businessNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_NAME)))) {
                    return false
                }
                if (__businessNameLoaded && this.__businessNameValue != __other.businessName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS)) != __other.__isVisible(PropId.byIndex(SLOT_BUSINESS_STATUS))) {
                    return false
                }
                val __businessStatusLoaded = 
                    this.__businessStatusLoaded
                if (__businessStatusLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_BUSINESS_STATUS)))) {
                    return false
                }
                if (__businessStatusLoaded && this.__businessStatusValue != __other.businessStatus) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORT_ORDER)) != __other.__isVisible(PropId.byIndex(SLOT_SORT_ORDER))) {
                    return false
                }
                val __sortOrderLoaded = 
                    this.__sortOrderLoaded
                if (__sortOrderLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORT_ORDER)))) {
                    return false
                }
                if (__sortOrderLoaded && this.__sortOrderValue != __other.sortOrder) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_CODE)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_CODE))) {
                    return false
                }
                val __indexCodeLoaded = 
                    this.__indexCodeValue !== null
                if (__indexCodeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_CODE)))) {
                    return false
                }
                if (__indexCodeLoaded && this.__indexCodeValue != __other.indexCode) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_NAME)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_NAME))) {
                    return false
                }
                val __indexNameLoaded = 
                    this.__indexNameValue !== null
                if (__indexNameLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_NAME)))) {
                    return false
                }
                if (__indexNameLoaded && this.__indexNameValue != __other.indexName) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_TYPE))) {
                    return false
                }
                val __indexTypeLoaded = 
                    this.__indexTypeValue !== null
                if (__indexTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_TYPE)))) {
                    return false
                }
                if (__indexTypeLoaded && this.__indexTypeValue != __other.indexType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_DOC_TYPE)) != __other.__isVisible(PropId.byIndex(SLOT_DOC_TYPE))) {
                    return false
                }
                val __docTypeLoaded = 
                    this.__docTypeValue !== null
                if (__docTypeLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_DOC_TYPE)))) {
                    return false
                }
                if (__docTypeLoaded && this.__docTypeValue != __other.docType) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_EXTRACT_EXPR)) != __other.__isVisible(PropId.byIndex(SLOT_EXTRACT_EXPR))) {
                    return false
                }
                val __extractExprLoaded = 
                    this.__extractExprValue !== null
                if (__extractExprLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_EXTRACT_EXPR)))) {
                    return false
                }
                if (__extractExprLoaded && this.__extractExprValue != __other.extractExpr) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SEARCHABLE)) != __other.__isVisible(PropId.byIndex(SLOT_SEARCHABLE))) {
                    return false
                }
                val __searchableLoaded = 
                    this.__searchableLoaded
                if (__searchableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SEARCHABLE)))) {
                    return false
                }
                if (__searchableLoaded && this.__searchableValue != __other.searchable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_SORTABLE)) != __other.__isVisible(PropId.byIndex(SLOT_SORTABLE))) {
                    return false
                }
                val __sortableLoaded = 
                    this.__sortableLoaded
                if (__sortableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_SORTABLE)))) {
                    return false
                }
                if (__sortableLoaded && this.__sortableValue != __other.sortable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_AGGREGATABLE)) != __other.__isVisible(PropId.byIndex(SLOT_AGGREGATABLE))) {
                    return false
                }
                val __aggregatableLoaded = 
                    this.__aggregatableLoaded
                if (__aggregatableLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_AGGREGATABLE)))) {
                    return false
                }
                if (__aggregatableLoaded && this.__aggregatableValue != __other.aggregatable) {
                    return false
                }
                if (__isVisible(PropId.byIndex(SLOT_INDEX_DATA)) != __other.__isVisible(PropId.byIndex(SLOT_INDEX_DATA))) {
                    return false
                }
                val __indexDataLoaded = 
                    this.__indexDataValue !== null
                if (__indexDataLoaded != (__other.__isLoaded(PropId.byIndex(SLOT_INDEX_DATA)))) {
                    return false
                }
                if (__indexDataLoaded && this.__indexDataValue != __other.indexData) {
                    return false
                }
                return true
            }

            override fun __equals(obj: Any?, shallow: Boolean): Boolean = if (shallow) __shallowEquals(obj) else equals(obj)

            override fun toString(): String = ImmutableObjects.toString(this)
        }

        @GeneratedBy(type = DocIndex::class)
        internal class DraftImpl(
            ctx: DraftContext?,
            base: DocIndex?,
        ) : Implementor,
            DocIndexDraft,
            DraftSpi {
            private val __ctx: DraftContext? = ctx

            private val __base: Impl? = base as Impl?

            private var __modified: Impl? = if (base === null) Impl() else null

            private var __resolving: Boolean = false

            private var __resolved: DocIndex? = null

            override var id: String
                get() = (__modified ?: __base!!).id
                set(id) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__idValue = id
                }

            override var createdTime: LocalDateTime
                get() = (__modified ?: __base!!).createdTime
                set(createdTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdTimeValue = createdTime
                }

            override var updatedTime: LocalDateTime
                get() = (__modified ?: __base!!).updatedTime
                set(updatedTime) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedTimeValue = updatedTime
                }

            override var createdBy: String?
                get() = (__modified ?: __base!!).createdBy
                set(createdBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__createdByValue = createdBy
                    __tmpModified.__createdByLoaded = true
                }

            override var updatedBy: String?
                get() = (__modified ?: __base!!).updatedBy
                set(updatedBy) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__updatedByValue = updatedBy
                    __tmpModified.__updatedByLoaded = true
                }

            override var version: Int
                get() = (__modified ?: __base!!).version
                set(version) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__versionValue = version
                    __tmpModified.__versionLoaded = true
                }

            override var deleted: Boolean
                get() = (__modified ?: __base!!).deleted
                set(deleted) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deletedValue = deleted
                    __tmpModified.__deletedLoaded = true
                }

            override var tenantId: String?
                get() = (__modified ?: __base!!).tenantId
                set(tenantId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__tenantIdValue = tenantId
                    __tmpModified.__tenantIdLoaded = true
                }

            override var orgId: String?
                get() = (__modified ?: __base!!).orgId
                set(orgId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__orgIdValue = orgId
                    __tmpModified.__orgIdLoaded = true
                }

            override var deptId: String?
                get() = (__modified ?: __base!!).deptId
                set(deptId) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__deptIdValue = deptId
                    __tmpModified.__deptIdLoaded = true
                }

            override var businessCode: String?
                get() = (__modified ?: __base!!).businessCode
                set(businessCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessCodeValue = businessCode
                    __tmpModified.__businessCodeLoaded = true
                }

            override var businessName: String?
                get() = (__modified ?: __base!!).businessName
                set(businessName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessNameValue = businessName
                    __tmpModified.__businessNameLoaded = true
                }

            override var businessStatus: String?
                get() = (__modified ?: __base!!).businessStatus
                set(businessStatus) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__businessStatusValue = businessStatus
                    __tmpModified.__businessStatusLoaded = true
                }

            override var sortOrder: Int?
                get() = (__modified ?: __base!!).sortOrder
                set(sortOrder) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortOrderValue = sortOrder
                    __tmpModified.__sortOrderLoaded = true
                }

            override var indexCode: String
                get() = (__modified ?: __base!!).indexCode
                set(indexCode) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexCodeValue = indexCode
                }

            override var indexName: String
                get() = (__modified ?: __base!!).indexName
                set(indexName) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexNameValue = indexName
                }

            override var indexType: String
                get() = (__modified ?: __base!!).indexType
                set(indexType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexTypeValue = indexType
                }

            override var docType: DocType
                get() = __ctx().toDraftObject((__modified ?: __base!!).docType)
                set(docType) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__docTypeValue = docType
                }

            @get:JsonIgnore
            public override var docTypeId: String
                get() = docType.id
                set(docTypeId) {
                    docType().id = docTypeId
                }

            override var extractExpr: String
                get() = (__modified ?: __base!!).extractExpr
                set(extractExpr) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__extractExprValue = extractExpr
                }

            override var searchable: Boolean
                get() = (__modified ?: __base!!).searchable
                set(searchable) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__searchableValue = searchable
                    __tmpModified.__searchableLoaded = true
                }

            override var sortable: Boolean
                get() = (__modified ?: __base!!).sortable
                set(sortable) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__sortableValue = sortable
                    __tmpModified.__sortableLoaded = true
                }

            override var aggregatable: Boolean
                get() = (__modified ?: __base!!).aggregatable
                set(aggregatable) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__aggregatableValue = aggregatable
                    __tmpModified.__aggregatableLoaded = true
                }

            override var indexData: List<DocumentIndexData>
                get() = __ctx().toDraftList((__modified ?: __base!!).indexData, DocumentIndexData::class.java, true)
                set(indexData) {
                    if (__resolved != null) {
                        throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                    }
                    val __tmpModified = (__modified ?: __base!!.clone())
                            .also { __modified = it }
                    __tmpModified.__indexDataValue = NonSharedList.of(__tmpModified.__indexDataValue, indexData)
                }

            override fun __isLoaded(prop: PropId): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isLoaded(prop: String): Boolean = (__modified ?: __base!!).__isLoaded(prop)

            override fun __isVisible(prop: PropId): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun __isVisible(prop: String): Boolean = (__modified ?: __base!!).__isVisible(prop)

            override fun hashCode(): Int = (__modified ?: __base!!).hashCode()

            override fun __hashCode(shallow: Boolean): Int = (__modified ?: __base!!).__hashCode(shallow)

            override fun equals(other: Any?): Boolean = (__modified ?: __base!!).equals(other)

            override fun __equals(other: Any?, shallow: Boolean): Boolean = (__modified ?: __base!!).__equals(other, shallow)

            override fun toString(): String = ImmutableObjects.toString(this)

            override fun docType(): DocTypeDraft {
                if (!__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                    docType = DocTypeDraft.`$`.produce()
                }
                return docType as DocTypeDraft
            }

            override fun docType(block: DocTypeDraft.() -> Unit) {
                docType().apply(block)
            }

            override fun indexData(): MutableList<DocumentIndexDataDraft> {
                if (!__isLoaded(PropId.byIndex(SLOT_INDEX_DATA))) {
                    indexData = emptyList()
                }
                return indexData as MutableList<DocumentIndexDataDraft>
            }

            override fun __unload(prop: PropId) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop.asIndex()) {
                    -1 ->
                    	__unload(prop.asName())
                    SLOT_ID ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    SLOT_CREATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    SLOT_UPDATED_TIME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    SLOT_CREATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    SLOT_UPDATED_BY ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    SLOT_VERSION ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    SLOT_DELETED ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    SLOT_TENANT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    SLOT_ORG_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    SLOT_DEPT_ID ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    SLOT_BUSINESS_CODE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    SLOT_BUSINESS_NAME ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    SLOT_BUSINESS_STATUS ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    SLOT_SORT_ORDER ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    SLOT_INDEX_CODE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexCodeValue = null
                    SLOT_INDEX_NAME ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexNameValue = null
                    SLOT_INDEX_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexTypeValue = null
                    SLOT_DOC_TYPE ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    SLOT_EXTRACT_EXPR ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__extractExprValue = null
                    SLOT_SEARCHABLE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__searchableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__searchableLoaded = false
                        }
                    SLOT_SORTABLE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortableLoaded = false
                        }
                    SLOT_AGGREGATABLE ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__aggregatableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__aggregatableLoaded = false
                        }
                    SLOT_INDEX_DATA ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                        prop
                    )

                }
            }

            override fun __unload(prop: String) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                when (prop) {
                    "id" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__idValue = null
                    "createdTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__createdTimeValue = null
                    "updatedTime" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__updatedTimeValue = null
                    "createdBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__createdByLoaded = false
                        }
                    "updatedBy" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__updatedByLoaded = false
                        }
                    "version" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionValue = 0
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__versionLoaded = false
                        }
                    "deleted" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deletedLoaded = false
                        }
                    "tenantId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__tenantIdLoaded = false
                        }
                    "orgId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__orgIdLoaded = false
                        }
                    "deptId" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__deptIdLoaded = false
                        }
                    "businessCode" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessCodeLoaded = false
                        }
                    "businessName" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessNameLoaded = false
                        }
                    "businessStatus" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__businessStatusLoaded = false
                        }
                    "sortOrder" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderValue = null
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortOrderLoaded = false
                        }
                    "indexCode" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexCodeValue = null
                    "indexName" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexNameValue = null
                    "indexType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexTypeValue = null
                    "docType" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__docTypeValue = null
                    "extractExpr" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__extractExprValue = null
                    "searchable" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__searchableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__searchableLoaded = false
                        }
                    "sortable" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__sortableLoaded = false
                        }
                    "aggregatable" ->
                    	 {
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__aggregatableValue = false
                            (__modified ?: __base!!.clone())
                                    .also { __modified = it }
                                    .__aggregatableLoaded = false
                        }
                    "indexData" ->
                    	(__modified ?: __base!!.clone())
                                .also { __modified = it }
                                .__indexDataValue = null
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: PropId, `value`: Any?) {
                when (prop.asIndex()) {
                    -1 ->
                    	__set(prop.asName(), value)
                    SLOT_ID ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    SLOT_CREATED_TIME ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    SLOT_UPDATED_TIME ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    SLOT_CREATED_BY ->
                    	this.createdBy = value as String?
                    SLOT_UPDATED_BY ->
                    	this.updatedBy = value as String?
                    SLOT_VERSION ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    SLOT_DELETED ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    SLOT_TENANT_ID ->
                    	this.tenantId = value as String?
                    SLOT_ORG_ID ->
                    	this.orgId = value as String?
                    SLOT_DEPT_ID ->
                    	this.deptId = value as String?
                    SLOT_BUSINESS_CODE ->
                    	this.businessCode = value as String?
                    SLOT_BUSINESS_NAME ->
                    	this.businessName = value as String?
                    SLOT_BUSINESS_STATUS ->
                    	this.businessStatus = value as String?
                    SLOT_SORT_ORDER ->
                    	this.sortOrder = value as Int?
                    SLOT_INDEX_CODE ->
                    	this.indexCode = value as String?
                    	?: throw IllegalArgumentException("'indexCode cannot be null")
                    SLOT_INDEX_NAME ->
                    	this.indexName = value as String?
                    	?: throw IllegalArgumentException("'indexName cannot be null")
                    SLOT_INDEX_TYPE ->
                    	this.indexType = value as String?
                    	?: throw IllegalArgumentException("'indexType cannot be null")
                    SLOT_DOC_TYPE ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    SLOT_EXTRACT_EXPR ->
                    	this.extractExpr = value as String?
                    	?: throw IllegalArgumentException("'extractExpr cannot be null")
                    SLOT_SEARCHABLE ->
                    	this.searchable = value as Boolean?
                    	?: throw IllegalArgumentException("'searchable cannot be null")
                    SLOT_SORTABLE ->
                    	this.sortable = value as Boolean?
                    	?: throw IllegalArgumentException("'sortable cannot be null")
                    SLOT_AGGREGATABLE ->
                    	this.aggregatable = value as Boolean?
                    	?: throw IllegalArgumentException("'aggregatable cannot be null")
                    SLOT_INDEX_DATA ->
                    	this.indexData = value as List<DocumentIndexData>?
                    	?: throw IllegalArgumentException("'indexData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                        prop
                    )

                }
            }

            override fun __set(prop: String, `value`: Any?) {
                when (prop) {
                    "id" ->
                    	this.id = value as String?
                    	?: throw IllegalArgumentException("'id cannot be null")
                    "createdTime" ->
                    	this.createdTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'createdTime cannot be null")
                    "updatedTime" ->
                    	this.updatedTime = value as LocalDateTime?
                    	?: throw IllegalArgumentException("'updatedTime cannot be null")
                    "createdBy" ->
                    	this.createdBy = value as String?
                    "updatedBy" ->
                    	this.updatedBy = value as String?
                    "version" ->
                    	this.version = value as Int?
                    	?: throw IllegalArgumentException("'version cannot be null")
                    "deleted" ->
                    	this.deleted = value as Boolean?
                    	?: throw IllegalArgumentException("'deleted cannot be null")
                    "tenantId" ->
                    	this.tenantId = value as String?
                    "orgId" ->
                    	this.orgId = value as String?
                    "deptId" ->
                    	this.deptId = value as String?
                    "businessCode" ->
                    	this.businessCode = value as String?
                    "businessName" ->
                    	this.businessName = value as String?
                    "businessStatus" ->
                    	this.businessStatus = value as String?
                    "sortOrder" ->
                    	this.sortOrder = value as Int?
                    "indexCode" ->
                    	this.indexCode = value as String?
                    	?: throw IllegalArgumentException("'indexCode cannot be null")
                    "indexName" ->
                    	this.indexName = value as String?
                    	?: throw IllegalArgumentException("'indexName cannot be null")
                    "indexType" ->
                    	this.indexType = value as String?
                    	?: throw IllegalArgumentException("'indexType cannot be null")
                    "docType" ->
                    	this.docType = value as DocType?
                    	?: throw IllegalArgumentException("'docType cannot be null")
                    "extractExpr" ->
                    	this.extractExpr = value as String?
                    	?: throw IllegalArgumentException("'extractExpr cannot be null")
                    "searchable" ->
                    	this.searchable = value as Boolean?
                    	?: throw IllegalArgumentException("'searchable cannot be null")
                    "sortable" ->
                    	this.sortable = value as Boolean?
                    	?: throw IllegalArgumentException("'sortable cannot be null")
                    "aggregatable" ->
                    	this.aggregatable = value as Boolean?
                    	?: throw IllegalArgumentException("'aggregatable cannot be null")
                    "indexData" ->
                    	this.indexData = value as List<DocumentIndexData>?
                    	?: throw IllegalArgumentException("'indexData cannot be null")
                    else -> throw IllegalArgumentException(
                        "Illegal property name" + 
                        " for \"cn.nkpro.elcube.jimmer.model.DocIndex\": " + 
                        prop
                    )

                }
            }

            override fun __show(prop: PropId, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(23).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop.asIndex()) {
                    -1 ->
                    	__show(prop.asName(), visible)
                    SLOT_ID ->
                    	__visibility.show(SLOT_ID, visible)
                    SLOT_CREATED_TIME ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    SLOT_UPDATED_TIME ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    SLOT_CREATED_BY ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    SLOT_UPDATED_BY ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    SLOT_VERSION ->
                    	__visibility.show(SLOT_VERSION, visible)
                    SLOT_DELETED ->
                    	__visibility.show(SLOT_DELETED, visible)
                    SLOT_TENANT_ID ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    SLOT_ORG_ID ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    SLOT_DEPT_ID ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    SLOT_BUSINESS_CODE ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    SLOT_BUSINESS_NAME ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    SLOT_BUSINESS_STATUS ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    SLOT_SORT_ORDER ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    SLOT_INDEX_CODE ->
                    	__visibility.show(SLOT_INDEX_CODE, visible)
                    SLOT_INDEX_NAME ->
                    	__visibility.show(SLOT_INDEX_NAME, visible)
                    SLOT_INDEX_TYPE ->
                    	__visibility.show(SLOT_INDEX_TYPE, visible)
                    SLOT_DOC_TYPE ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    SLOT_EXTRACT_EXPR ->
                    	__visibility.show(SLOT_EXTRACT_EXPR, visible)
                    SLOT_SEARCHABLE ->
                    	__visibility.show(SLOT_SEARCHABLE, visible)
                    SLOT_SORTABLE ->
                    	__visibility.show(SLOT_SORTABLE, visible)
                    SLOT_AGGREGATABLE ->
                    	__visibility.show(SLOT_AGGREGATABLE, visible)
                    SLOT_INDEX_DATA ->
                    	__visibility.show(SLOT_INDEX_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property id: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __show(prop: String, visible: Boolean) {
                if (__resolved != null) {
                    throw IllegalStateException("The current draft has been resolved so it cannot be modified")
                }
                val __visibility = (__modified ?: __base!!).__visibility
                    ?: if (visible) {
                        null
                    } else {
                        Visibility.of(23).also{
                            (__modified ?: __base!!.clone())
                            .also { __modified = it }.__visibility = it}
                    }
                    ?: return
                when (prop) {
                    "id" ->
                    	__visibility.show(SLOT_ID, visible)
                    "createdTime" ->
                    	__visibility.show(SLOT_CREATED_TIME, visible)
                    "updatedTime" ->
                    	__visibility.show(SLOT_UPDATED_TIME, visible)
                    "createdBy" ->
                    	__visibility.show(SLOT_CREATED_BY, visible)
                    "updatedBy" ->
                    	__visibility.show(SLOT_UPDATED_BY, visible)
                    "version" ->
                    	__visibility.show(SLOT_VERSION, visible)
                    "deleted" ->
                    	__visibility.show(SLOT_DELETED, visible)
                    "tenantId" ->
                    	__visibility.show(SLOT_TENANT_ID, visible)
                    "orgId" ->
                    	__visibility.show(SLOT_ORG_ID, visible)
                    "deptId" ->
                    	__visibility.show(SLOT_DEPT_ID, visible)
                    "businessCode" ->
                    	__visibility.show(SLOT_BUSINESS_CODE, visible)
                    "businessName" ->
                    	__visibility.show(SLOT_BUSINESS_NAME, visible)
                    "businessStatus" ->
                    	__visibility.show(SLOT_BUSINESS_STATUS, visible)
                    "sortOrder" ->
                    	__visibility.show(SLOT_SORT_ORDER, visible)
                    "indexCode" ->
                    	__visibility.show(SLOT_INDEX_CODE, visible)
                    "indexName" ->
                    	__visibility.show(SLOT_INDEX_NAME, visible)
                    "indexType" ->
                    	__visibility.show(SLOT_INDEX_TYPE, visible)
                    "docType" ->
                    	__visibility.show(SLOT_DOC_TYPE, visible)
                    "extractExpr" ->
                    	__visibility.show(SLOT_EXTRACT_EXPR, visible)
                    "searchable" ->
                    	__visibility.show(SLOT_SEARCHABLE, visible)
                    "sortable" ->
                    	__visibility.show(SLOT_SORTABLE, visible)
                    "aggregatable" ->
                    	__visibility.show(SLOT_AGGREGATABLE, visible)
                    "indexData" ->
                    	__visibility.show(SLOT_INDEX_DATA, visible)
                    else -> throw IllegalArgumentException(
                        "Illegal property name: \"" + 
                        prop + 
                        "\",it does not exists"
                    )
                }
            }

            override fun __draftContext(): DraftContext = __ctx()

            override fun __resolve(): Any {
                val __resolved = this.__resolved
                if (__resolved != null) {
                    return __resolved
                }
                if (__resolving) {
                    throw CircularReferenceException()
                }
                __resolving = true
                val __ctx = __ctx()
                try {
                    val base = __base
                    var __tmpModified = __modified
                    if (__tmpModified === null) {
                        if (__isLoaded(PropId.byIndex(SLOT_DOC_TYPE))) {
                            val oldValue = base!!.docType
                            val newValue = __ctx.resolveObject(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        if (__isLoaded(PropId.byIndex(SLOT_INDEX_DATA))) {
                            val oldValue = base!!.indexData
                            val newValue = __ctx.resolveList(oldValue)
                            if (oldValue !== newValue) {
                                <EMAIL> = newValue
                            }
                        }
                        __tmpModified = __modified
                    } else {
                        __tmpModified.__docTypeValue = __ctx.resolveObject(__tmpModified.__docTypeValue)
                        __tmpModified.__indexDataValue = NonSharedList.of(__tmpModified.__indexDataValue, __ctx.resolveList(__tmpModified.__indexDataValue))
                    }
                    if (base !== null && __tmpModified === null) {
                        this.__resolved = base
                        return base
                    }
                    this.__resolved = __tmpModified
                    return __tmpModified!!
                } finally {
                    __resolving = false
                }
            }

            override fun __isResolved(): Boolean = __resolved != null

            private fun __ctx(): DraftContext = __ctx ?: error("The current draft object is simple draft which does not support converting nested object to nested draft")

            internal fun __unwrap(): Any = __modified ?: error("Internal bug, draft for builder must have `__modified`")
        }
    }

    @GeneratedBy(type = DocIndex::class)
    public class Builder {
        private val __draft: `$`.DraftImpl

        public constructor(base: DocIndex?) {
            __draft = `$`.DraftImpl(null, base)
        }

        public constructor() : this(null)

        public fun id(id: String?): Builder {
            if (id !== null) {
                __draft.id = id
                __draft.__show(PropId.byIndex(`$`.SLOT_ID), true)
            }
            return this
        }

        public fun createdTime(createdTime: LocalDateTime?): Builder {
            if (createdTime !== null) {
                __draft.createdTime = createdTime
                __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_TIME), true)
            }
            return this
        }

        public fun updatedTime(updatedTime: LocalDateTime?): Builder {
            if (updatedTime !== null) {
                __draft.updatedTime = updatedTime
                __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_TIME), true)
            }
            return this
        }

        public fun createdBy(createdBy: String?): Builder {
            __draft.createdBy = createdBy
            __draft.__show(PropId.byIndex(`$`.SLOT_CREATED_BY), true)
            return this
        }

        public fun updatedBy(updatedBy: String?): Builder {
            __draft.updatedBy = updatedBy
            __draft.__show(PropId.byIndex(`$`.SLOT_UPDATED_BY), true)
            return this
        }

        public fun version(version: Int?): Builder {
            if (version !== null) {
                __draft.version = version
                __draft.__show(PropId.byIndex(`$`.SLOT_VERSION), true)
            }
            return this
        }

        public fun deleted(deleted: Boolean?): Builder {
            if (deleted !== null) {
                __draft.deleted = deleted
                __draft.__show(PropId.byIndex(`$`.SLOT_DELETED), true)
            }
            return this
        }

        public fun tenantId(tenantId: String?): Builder {
            __draft.tenantId = tenantId
            __draft.__show(PropId.byIndex(`$`.SLOT_TENANT_ID), true)
            return this
        }

        public fun orgId(orgId: String?): Builder {
            __draft.orgId = orgId
            __draft.__show(PropId.byIndex(`$`.SLOT_ORG_ID), true)
            return this
        }

        public fun deptId(deptId: String?): Builder {
            __draft.deptId = deptId
            __draft.__show(PropId.byIndex(`$`.SLOT_DEPT_ID), true)
            return this
        }

        public fun businessCode(businessCode: String?): Builder {
            __draft.businessCode = businessCode
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_CODE), true)
            return this
        }

        public fun businessName(businessName: String?): Builder {
            __draft.businessName = businessName
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_NAME), true)
            return this
        }

        public fun businessStatus(businessStatus: String?): Builder {
            __draft.businessStatus = businessStatus
            __draft.__show(PropId.byIndex(`$`.SLOT_BUSINESS_STATUS), true)
            return this
        }

        public fun sortOrder(sortOrder: Int?): Builder {
            __draft.sortOrder = sortOrder
            __draft.__show(PropId.byIndex(`$`.SLOT_SORT_ORDER), true)
            return this
        }

        public fun indexCode(indexCode: String?): Builder {
            if (indexCode !== null) {
                __draft.indexCode = indexCode
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEX_CODE), true)
            }
            return this
        }

        public fun indexName(indexName: String?): Builder {
            if (indexName !== null) {
                __draft.indexName = indexName
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEX_NAME), true)
            }
            return this
        }

        public fun indexType(indexType: String?): Builder {
            if (indexType !== null) {
                __draft.indexType = indexType
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEX_TYPE), true)
            }
            return this
        }

        public fun docType(docType: DocType?): Builder {
            if (docType !== null) {
                __draft.docType = docType
                __draft.__show(PropId.byIndex(`$`.SLOT_DOC_TYPE), true)
            }
            return this
        }

        public fun extractExpr(extractExpr: String?): Builder {
            if (extractExpr !== null) {
                __draft.extractExpr = extractExpr
                __draft.__show(PropId.byIndex(`$`.SLOT_EXTRACT_EXPR), true)
            }
            return this
        }

        public fun searchable(searchable: Boolean?): Builder {
            if (searchable !== null) {
                __draft.searchable = searchable
                __draft.__show(PropId.byIndex(`$`.SLOT_SEARCHABLE), true)
            }
            return this
        }

        public fun sortable(sortable: Boolean?): Builder {
            if (sortable !== null) {
                __draft.sortable = sortable
                __draft.__show(PropId.byIndex(`$`.SLOT_SORTABLE), true)
            }
            return this
        }

        public fun aggregatable(aggregatable: Boolean?): Builder {
            if (aggregatable !== null) {
                __draft.aggregatable = aggregatable
                __draft.__show(PropId.byIndex(`$`.SLOT_AGGREGATABLE), true)
            }
            return this
        }

        public fun indexData(indexData: List<DocumentIndexData>?): Builder {
            if (indexData !== null) {
                __draft.indexData = indexData
                __draft.__show(PropId.byIndex(`$`.SLOT_INDEX_DATA), true)
            }
            return this
        }

        public fun build(): DocIndex = __draft.__unwrap() as DocIndex
    }
}

@GeneratedBy(type = DocIndex::class)
public fun ImmutableCreator<DocIndex>.`by`(resolveImmediately: Boolean = false, block: DocIndexDraft.() -> Unit): DocIndex = DocIndexDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocIndex::class)
public fun ImmutableCreator<DocIndex>.`by`(base: DocIndex?, resolveImmediately: Boolean = false): DocIndex = DocIndexDraft.`$`.produce(base, resolveImmediately)

@GeneratedBy(type = DocIndex::class)
public fun ImmutableCreator<DocIndex>.`by`(
    base: DocIndex?,
    resolveImmediately: Boolean = false,
    block: DocIndexDraft.() -> Unit,
): DocIndex = DocIndexDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocIndex::class)
public fun DocIndex(resolveImmediately: Boolean = false, block: DocIndexDraft.() -> Unit): DocIndex = DocIndexDraft.`$`.produce(null, resolveImmediately, block)

@GeneratedBy(type = DocIndex::class)
public fun DocIndex(
    base: DocIndex?,
    resolveImmediately: Boolean = false,
    block: DocIndexDraft.() -> Unit,
): DocIndex = DocIndexDraft.`$`.produce(base, resolveImmediately, block)

@GeneratedBy(type = DocIndex::class)
public fun MutableList<DocIndexDraft>.addBy(resolveImmediately: Boolean = false, block: DocIndexDraft.() -> Unit): MutableList<DocIndexDraft> {
    add(DocIndexDraft.`$`.produce(null, resolveImmediately, block) as DocIndexDraft)
    return this
}

@GeneratedBy(type = DocIndex::class)
public fun MutableList<DocIndexDraft>.addBy(base: DocIndex?, resolveImmediately: Boolean = false): MutableList<DocIndexDraft> {
    add(DocIndexDraft.`$`.produce(base, resolveImmediately) as DocIndexDraft)
    return this
}

@GeneratedBy(type = DocIndex::class)
public fun MutableList<DocIndexDraft>.addBy(
    base: DocIndex?,
    resolveImmediately: Boolean = false,
    block: DocIndexDraft.() -> Unit,
): MutableList<DocIndexDraft> {
    add(DocIndexDraft.`$`.produce(base, resolveImmediately, block) as DocIndexDraft)
    return this
}

@GeneratedBy(type = DocIndex::class)
public fun DocIndex.copy(resolveImmediately: Boolean = false, block: DocIndexDraft.() -> Unit): DocIndex = DocIndexDraft.`$`.produce(this, resolveImmediately, block)
