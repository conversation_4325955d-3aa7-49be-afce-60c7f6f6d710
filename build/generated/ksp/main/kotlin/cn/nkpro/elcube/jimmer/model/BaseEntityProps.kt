@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.BaseEntity::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KProps

public val KNonNullProps<BaseEntity>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<String>(BaseEntityProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<BaseEntity>.id: KNullablePropExpression<String>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<String>(BaseEntityProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BaseEntity>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<LocalDateTime>(BaseEntityProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<BaseEntity>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<LocalDateTime>(BaseEntityProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<BaseEntity>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<LocalDateTime>(BaseEntityProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<BaseEntity>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<LocalDateTime>(BaseEntityProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<BaseEntity>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<String>(BaseEntityProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<BaseEntity>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<String>(BaseEntityProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<BaseEntity>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<Int>(BaseEntityProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<BaseEntity>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<Int>(BaseEntityProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<BaseEntity>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<Boolean>(BaseEntityProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<BaseEntity>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = BaseEntity::class)
    get() = get<Boolean>(BaseEntityProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

@GeneratedBy(type = BaseEntity::class)
public object BaseEntityProps {
    public val ID: TypedProp.Scalar<BaseEntity, String> =
            TypedProp.scalar(BaseEntity::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<BaseEntity, LocalDateTime> =
            TypedProp.scalar(BaseEntity::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<BaseEntity, LocalDateTime> =
            TypedProp.scalar(BaseEntity::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<BaseEntity, String?> =
            TypedProp.scalar(BaseEntity::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<BaseEntity, String?> =
            TypedProp.scalar(BaseEntity::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<BaseEntity, Int> =
            TypedProp.scalar(BaseEntity::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<BaseEntity, Boolean> =
            TypedProp.scalar(BaseEntity::deleted.toImmutableProp())
}
