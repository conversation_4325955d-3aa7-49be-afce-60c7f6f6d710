@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocCard::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<DocCard>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocCard::class)
    get() = get<LocalDateTime>(DocCardProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocCard>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocCard::class)
    get() = get<LocalDateTime>(DocCardProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<DocCard>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocCard::class)
    get() = get<LocalDateTime>(DocCardProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocCard>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocCard::class)
    get() = get<LocalDateTime>(DocCardProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<DocCard>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = DocCard::class)
    get() = get<Int>(DocCardProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<DocCard>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = DocCard::class)
    get() = get<Int>(DocCardProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocCard>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocCard>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<DocCard>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = DocCard::class)
    get() = get<Int>(DocCardProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocCard>.cardCode: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_CODE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.cardCode: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_CODE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.cardName: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.cardName: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.cardDesc: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_DESC.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.cardType: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.cardType: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_TYPE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.cardPosition: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_POSITION.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.cardPosition: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_POSITION.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.docType: KNonNullTable<DocType>
    @GeneratedBy(type = DocCard::class)
    get() = join(DocCardProps.DOC_TYPE.unwrap())

public val KProps<DocCard>.`docType?`: KNullableTable<DocType>
    @GeneratedBy(type = DocCard::class)
    get() = outerJoin(DocCardProps.DOC_TYPE.unwrap())

public val KTableEx<DocCard>.docType: KNonNullTableEx<DocType>
    @GeneratedBy(type = DocCard::class)
    get() = join(DocCardProps.DOC_TYPE.unwrap())

public val KTableEx<DocCard>.`docType?`: KNullableTableEx<DocType>
    @GeneratedBy(type = DocCard::class)
    get() = outerJoin(DocCardProps.DOC_TYPE.unwrap())

public val KNonNullTable<DocCard>.docTypeId: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = getAssociatedId<String>(DocCardProps.DOC_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.docTypeId: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = getAssociatedId<String>(DocCardProps.DOC_TYPE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.componentName: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.COMPONENT_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocCard>.componentName: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.COMPONENT_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.cardConfig: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CARD_CONFIG.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocCard>.enabled: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.ENABLED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocCard>.enabled: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.ENABLED.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocCard>.required: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.REQUIRED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocCard>.required: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.REQUIRED.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocCard>.readonly: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.READONLY.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocCard>.readonly: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.READONLY.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocCard>.visible: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.VISIBLE.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocCard>.visible: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocCard::class)
    get() = get<Boolean>(DocCardProps.VISIBLE.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<DocCard>.visibleExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.VISIBLE_EXPR.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.readonlyExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.READONLY_EXPR.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.requiredExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.REQUIRED_EXPR.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.calcExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.CALC_EXPR.unwrap()) as KNullablePropExpression<String>

public val KProps<DocCard>.validateExpr: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = get<String>(DocCardProps.VALIDATE_EXPR.unwrap()) as KNullablePropExpression<String>

public fun KProps<DocCard>.fields(block: KImplicitSubQueryTable<DocCardField>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocCardProps.FIELDS.unwrap(), block)

public val KTableEx<DocCard>.fields: KNonNullTableEx<DocCardField>
    @GeneratedBy(type = DocCard::class)
    get() = join(DocCardProps.FIELDS.unwrap())

public val KTableEx<DocCard>.`fields?`: KNullableTableEx<DocCardField>
    @GeneratedBy(type = DocCard::class)
    get() = outerJoin(DocCardProps.FIELDS.unwrap())

public fun KProps<DocCard>.cardData(block: KImplicitSubQueryTable<DocumentCardData>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocCardProps.CARD_DATA.unwrap(), block)

public val KTableEx<DocCard>.cardData: KNonNullTableEx<DocumentCardData>
    @GeneratedBy(type = DocCard::class)
    get() = join(DocCardProps.CARD_DATA.unwrap())

public val KTableEx<DocCard>.`cardData?`: KNullableTableEx<DocumentCardData>
    @GeneratedBy(type = DocCard::class)
    get() = outerJoin(DocCardProps.CARD_DATA.unwrap())

public val KRemoteRef.NonNull<DocCard>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<DocCard>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocCard::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = DocCard::class)
public fun KNonNullTable<DocCard>.fetchBy(block: DocCardFetcherDsl.() -> Unit): Selection<DocCard> = fetch(newFetcher(DocCard::class).`by`(block))

@GeneratedBy(type = DocCard::class)
public fun KNullableTable<DocCard>.fetchBy(block: DocCardFetcherDsl.() -> Unit): Selection<DocCard?> = fetch(newFetcher(DocCard::class).`by`(block))

@GeneratedBy(type = DocCard::class)
public object DocCardProps {
    public val ID: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<DocCard, LocalDateTime> =
            TypedProp.scalar(DocCard::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<DocCard, LocalDateTime> =
            TypedProp.scalar(DocCard::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<DocCard, Int> =
            TypedProp.scalar(DocCard::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<DocCard, Boolean> =
            TypedProp.scalar(DocCard::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<DocCard, Int?> =
            TypedProp.scalar(DocCard::sortOrder.toImmutableProp())

    public val CARD_CODE: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::cardCode.toImmutableProp())

    public val CARD_NAME: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::cardName.toImmutableProp())

    public val CARD_DESC: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::cardDesc.toImmutableProp())

    public val CARD_TYPE: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::cardType.toImmutableProp())

    public val CARD_POSITION: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::cardPosition.toImmutableProp())

    public val DOC_TYPE: TypedProp.Reference<DocCard, DocType> =
            TypedProp.reference(DocCard::docType.toImmutableProp())

    public val COMPONENT_NAME: TypedProp.Scalar<DocCard, String> =
            TypedProp.scalar(DocCard::componentName.toImmutableProp())

    public val CARD_CONFIG: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::cardConfig.toImmutableProp())

    public val ENABLED: TypedProp.Scalar<DocCard, Boolean> =
            TypedProp.scalar(DocCard::enabled.toImmutableProp())

    public val REQUIRED: TypedProp.Scalar<DocCard, Boolean> =
            TypedProp.scalar(DocCard::required.toImmutableProp())

    public val READONLY: TypedProp.Scalar<DocCard, Boolean> =
            TypedProp.scalar(DocCard::readonly.toImmutableProp())

    public val VISIBLE: TypedProp.Scalar<DocCard, Boolean> =
            TypedProp.scalar(DocCard::visible.toImmutableProp())

    public val VISIBLE_EXPR: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::visibleExpr.toImmutableProp())

    public val READONLY_EXPR: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::readonlyExpr.toImmutableProp())

    public val REQUIRED_EXPR: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::requiredExpr.toImmutableProp())

    public val CALC_EXPR: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::calcExpr.toImmutableProp())

    public val VALIDATE_EXPR: TypedProp.Scalar<DocCard, String?> =
            TypedProp.scalar(DocCard::validateExpr.toImmutableProp())

    public val FIELDS: TypedProp.ReferenceList<DocCard, DocCardField> =
            TypedProp.referenceList(DocCard::fields.toImmutableProp())

    public val CARD_DATA: TypedProp.ReferenceList<DocCard, DocumentCardData> =
            TypedProp.referenceList(DocCard::cardData.toImmutableProp())
}
