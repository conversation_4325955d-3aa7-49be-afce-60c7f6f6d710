@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocType::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = DocType::class)
public fun FetcherCreator<DocType>.`by`(block: DocTypeFetcherDsl.() -> Unit): Fetcher<DocType> {
    val dsl = DocTypeFetcherDsl(emptyDocTypeFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = DocType::class)
public fun FetcherCreator<DocType>.`by`(base: Fetcher<DocType>?, block: DocTypeFetcherDsl.() -> Unit): Fetcher<DocType> {
    val dsl = DocTypeFetcherDsl(base ?: emptyDocTypeFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = DocType::class)
public class DocTypeFetcherDsl(
    fetcher: Fetcher<DocType> = emptyDocTypeFetcher,
) {
    private var _fetcher: Fetcher<DocType> = fetcher

    public fun internallyGetFetcher(): Fetcher<DocType> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun docType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docType")
        } else {
            _fetcher.remove("docType")
        }
    }

    public fun docName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docName")
        } else {
            _fetcher.remove("docName")
        }
    }

    public fun docDesc(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docDesc")
        } else {
            _fetcher.remove("docDesc")
        }
    }

    public fun docIcon(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docIcon")
        } else {
            _fetcher.remove("docIcon")
        }
    }

    public fun docColor(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docColor")
        } else {
            _fetcher.remove("docColor")
        }
    }

    public fun enabled(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("enabled")
        } else {
            _fetcher.remove("enabled")
        }
    }

    public fun systemBuiltIn(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("systemBuiltIn")
        } else {
            _fetcher.remove("systemBuiltIn")
        }
    }

    public fun docConfig(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("docConfig")
        } else {
            _fetcher.remove("docConfig")
        }
    }

    public fun states(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("states")
        } else {
            _fetcher.remove("states")
        }
    }

    public fun states(childFetcher: Fetcher<DocState>) {
        _fetcher = _fetcher.add(
            "states",
            childFetcher
        )
    }

    public fun states(childFetcher: Fetcher<DocState>, cfgBlock: (KListFieldDsl<DocState>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "states",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun states(childBlock: DocStateFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "states",
            DocStateFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun states(cfgBlock: (KListFieldDsl<DocState>.() -> Unit)?, childBlock: DocStateFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "states",
            DocStateFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun states(enabled: Boolean, childFetcher: Fetcher<DocState>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("states")
        } else {
            states(childFetcher)
        }
    }

    public fun states(
        enabled: Boolean,
        childFetcher: Fetcher<DocState>,
        cfgBlock: (KListFieldDsl<DocState>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("states")
        } else {
            states(childFetcher, cfgBlock)
        }
    }

    public fun states(enabled: Boolean, childBlock: DocStateFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("states")
        } else {
            states(childBlock)
        }
    }

    public fun states(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocState>.() -> Unit)?,
        childBlock: DocStateFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("states")
        } else {
            states(cfgBlock, childBlock)
        }
    }

    public fun cards(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("cards")
        } else {
            _fetcher.remove("cards")
        }
    }

    public fun cards(childFetcher: Fetcher<DocCard>) {
        _fetcher = _fetcher.add(
            "cards",
            childFetcher
        )
    }

    public fun cards(childFetcher: Fetcher<DocCard>, cfgBlock: (KListFieldDsl<DocCard>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "cards",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cards(childBlock: DocCardFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cards",
            DocCardFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun cards(cfgBlock: (KListFieldDsl<DocCard>.() -> Unit)?, childBlock: DocCardFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "cards",
            DocCardFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun cards(enabled: Boolean, childFetcher: Fetcher<DocCard>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cards")
        } else {
            cards(childFetcher)
        }
    }

    public fun cards(
        enabled: Boolean,
        childFetcher: Fetcher<DocCard>,
        cfgBlock: (KListFieldDsl<DocCard>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cards")
        } else {
            cards(childFetcher, cfgBlock)
        }
    }

    public fun cards(enabled: Boolean, childBlock: DocCardFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cards")
        } else {
            cards(childBlock)
        }
    }

    public fun cards(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocCard>.() -> Unit)?,
        childBlock: DocCardFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("cards")
        } else {
            cards(cfgBlock, childBlock)
        }
    }

    public fun indexes(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("indexes")
        } else {
            _fetcher.remove("indexes")
        }
    }

    public fun indexes(childFetcher: Fetcher<DocIndex>) {
        _fetcher = _fetcher.add(
            "indexes",
            childFetcher
        )
    }

    public fun indexes(childFetcher: Fetcher<DocIndex>, cfgBlock: (KListFieldDsl<DocIndex>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "indexes",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexes(childBlock: DocIndexFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexes",
            DocIndexFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun indexes(cfgBlock: (KListFieldDsl<DocIndex>.() -> Unit)?, childBlock: DocIndexFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "indexes",
            DocIndexFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun indexes(enabled: Boolean, childFetcher: Fetcher<DocIndex>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexes")
        } else {
            indexes(childFetcher)
        }
    }

    public fun indexes(
        enabled: Boolean,
        childFetcher: Fetcher<DocIndex>,
        cfgBlock: (KListFieldDsl<DocIndex>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexes")
        } else {
            indexes(childFetcher, cfgBlock)
        }
    }

    public fun indexes(enabled: Boolean, childBlock: DocIndexFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexes")
        } else {
            indexes(childBlock)
        }
    }

    public fun indexes(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<DocIndex>.() -> Unit)?,
        childBlock: DocIndexFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("indexes")
        } else {
            indexes(cfgBlock, childBlock)
        }
    }

    public fun documents(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("documents")
        } else {
            _fetcher.remove("documents")
        }
    }

    public fun documents(childFetcher: Fetcher<Document>) {
        _fetcher = _fetcher.add(
            "documents",
            childFetcher
        )
    }

    public fun documents(childFetcher: Fetcher<Document>, cfgBlock: (KListFieldDsl<Document>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "documents",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun documents(childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "documents",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun documents(cfgBlock: (KListFieldDsl<Document>.() -> Unit)?, childBlock: DocumentFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "documents",
            DocumentFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun documents(enabled: Boolean, childFetcher: Fetcher<Document>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("documents")
        } else {
            documents(childFetcher)
        }
    }

    public fun documents(
        enabled: Boolean,
        childFetcher: Fetcher<Document>,
        cfgBlock: (KListFieldDsl<Document>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("documents")
        } else {
            documents(childFetcher, cfgBlock)
        }
    }

    public fun documents(enabled: Boolean, childBlock: DocumentFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("documents")
        } else {
            documents(childBlock)
        }
    }

    public fun documents(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<Document>.() -> Unit)?,
        childBlock: DocumentFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("documents")
        } else {
            documents(cfgBlock, childBlock)
        }
    }
}

private val emptyDocTypeFetcher: Fetcher<DocType> = FetcherImpl(DocType::class.java)
