@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.Document::class)

package cn.nkpro.elcube.jimmer.model

import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<Document>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Document>.id: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Document>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<Document>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<Document>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<Document>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<Document>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<Document>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = Document::class)
    get() = get<Int>(DocumentProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<Document>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = Document::class)
    get() = get<Int>(DocumentProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<Document>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = Document::class)
    get() = get<Boolean>(DocumentProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<Document>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = Document::class)
    get() = get<Boolean>(DocumentProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<Document>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = Document::class)
    get() = get<Int>(DocumentProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<Document>.docId: KNonNullPropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DOC_ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Document>.docId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DOC_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.docNo: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DOC_NO.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.docName: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DOC_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.docType: KNonNullTable<DocType>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.DOC_TYPE.unwrap())

public val KProps<Document>.`docType?`: KNullableTable<DocType>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.DOC_TYPE.unwrap())

public val KTableEx<Document>.docType: KNonNullTableEx<DocType>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.DOC_TYPE.unwrap())

public val KTableEx<Document>.`docType?`: KNullableTableEx<DocType>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.DOC_TYPE.unwrap())

public val KNonNullTable<Document>.docTypeId: KNonNullPropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = getAssociatedId<String>(DocumentProps.DOC_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<Document>.docTypeId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = getAssociatedId<String>(DocumentProps.DOC_TYPE.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.currentState: KNonNullTable<DocState>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.CURRENT_STATE.unwrap())

public val KProps<Document>.`currentState?`: KNullableTable<DocState>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.CURRENT_STATE.unwrap())

public val KTableEx<Document>.currentState: KNonNullTableEx<DocState>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.CURRENT_STATE.unwrap())

public val KTableEx<Document>.`currentState?`: KNullableTableEx<DocState>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.CURRENT_STATE.unwrap())

public val KProps<Document>.currentStateId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = getAssociatedId<String>(DocumentProps.CURRENT_STATE.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.preDocId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.PRE_DOC_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.preDocument: KNonNullTable<Document>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.PRE_DOCUMENT.unwrap())

public val KProps<Document>.`preDocument?`: KNullableTable<Document>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.PRE_DOCUMENT.unwrap())

public val KTableEx<Document>.preDocument: KNonNullTableEx<Document>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.PRE_DOCUMENT.unwrap())

public val KTableEx<Document>.`preDocument?`: KNullableTableEx<Document>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.PRE_DOCUMENT.unwrap())

public val KProps<Document>.preDocumentId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = getAssociatedId<String>(DocumentProps.PRE_DOCUMENT.unwrap()) as KNullablePropExpression<String>

public fun KProps<Document>.nextDocuments(block: KImplicitSubQueryTable<Document>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocumentProps.NEXT_DOCUMENTS.unwrap(), block)

public val KTableEx<Document>.nextDocuments: KNonNullTableEx<Document>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.NEXT_DOCUMENTS.unwrap())

public val KTableEx<Document>.`nextDocuments?`: KNullableTableEx<Document>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.NEXT_DOCUMENTS.unwrap())

public val KProps<Document>.partnerId: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.PARTNER_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.partnerName: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.PARTNER_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.docAmount: KNullablePropExpression<BigDecimal>
    @GeneratedBy(type = Document::class)
    get() = get<BigDecimal>(DocumentProps.DOC_AMOUNT.unwrap()) as KNullablePropExpression<BigDecimal>

public val KProps<Document>.currency: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.CURRENCY.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.docDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.DOC_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<Document>.effectiveDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.EFFECTIVE_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<Document>.expireDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = Document::class)
    get() = get<LocalDateTime>(DocumentProps.EXPIRE_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<Document>.docData: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.DOC_DATA.unwrap()) as KNullablePropExpression<String>

public val KProps<Document>.extProps: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = get<String>(DocumentProps.EXT_PROPS.unwrap()) as KNullablePropExpression<String>

public fun KProps<Document>.cardData(block: KImplicitSubQueryTable<DocumentCardData>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocumentProps.CARD_DATA.unwrap(), block)

public val KTableEx<Document>.cardData: KNonNullTableEx<DocumentCardData>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.CARD_DATA.unwrap())

public val KTableEx<Document>.`cardData?`: KNullableTableEx<DocumentCardData>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.CARD_DATA.unwrap())

public fun KProps<Document>.histories(block: KImplicitSubQueryTable<DocumentHistory>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocumentProps.HISTORIES.unwrap(), block)

public val KTableEx<Document>.histories: KNonNullTableEx<DocumentHistory>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.HISTORIES.unwrap())

public val KTableEx<Document>.`histories?`: KNullableTableEx<DocumentHistory>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.HISTORIES.unwrap())

public fun KProps<Document>.indexData(block: KImplicitSubQueryTable<DocumentIndexData>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocumentProps.INDEX_DATA.unwrap(), block)

public val KTableEx<Document>.indexData: KNonNullTableEx<DocumentIndexData>
    @GeneratedBy(type = Document::class)
    get() = join(DocumentProps.INDEX_DATA.unwrap())

public val KTableEx<Document>.`indexData?`: KNullableTableEx<DocumentIndexData>
    @GeneratedBy(type = Document::class)
    get() = outerJoin(DocumentProps.INDEX_DATA.unwrap())

public val KRemoteRef.NonNull<Document>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<Document>.id: KNullablePropExpression<String>
    @GeneratedBy(type = Document::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = Document::class)
public fun KNonNullTable<Document>.fetchBy(block: DocumentFetcherDsl.() -> Unit): Selection<Document> = fetch(newFetcher(Document::class).`by`(block))

@GeneratedBy(type = Document::class)
public fun KNullableTable<Document>.fetchBy(block: DocumentFetcherDsl.() -> Unit): Selection<Document?> = fetch(newFetcher(Document::class).`by`(block))

@GeneratedBy(type = Document::class)
public object DocumentProps {
    public val ID: TypedProp.Scalar<Document, String> =
            TypedProp.scalar(Document::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<Document, LocalDateTime> =
            TypedProp.scalar(Document::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<Document, LocalDateTime> =
            TypedProp.scalar(Document::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<Document, Int> =
            TypedProp.scalar(Document::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<Document, Boolean> =
            TypedProp.scalar(Document::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<Document, Int?> =
            TypedProp.scalar(Document::sortOrder.toImmutableProp())

    public val DOC_ID: TypedProp.Scalar<Document, String> =
            TypedProp.scalar(Document::docId.toImmutableProp())

    public val DOC_NO: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::docNo.toImmutableProp())

    public val DOC_NAME: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::docName.toImmutableProp())

    public val DOC_TYPE: TypedProp.Reference<Document, DocType> =
            TypedProp.reference(Document::docType.toImmutableProp())

    public val CURRENT_STATE: TypedProp.Reference<Document, DocState?> =
            TypedProp.reference(Document::currentState.toImmutableProp())

    public val PRE_DOC_ID: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::preDocId.toImmutableProp())

    public val PRE_DOCUMENT: TypedProp.Reference<Document, Document?> =
            TypedProp.reference(Document::preDocument.toImmutableProp())

    public val NEXT_DOCUMENTS: TypedProp.ReferenceList<Document, Document> =
            TypedProp.referenceList(Document::nextDocuments.toImmutableProp())

    public val PARTNER_ID: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::partnerId.toImmutableProp())

    public val PARTNER_NAME: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::partnerName.toImmutableProp())

    public val DOC_AMOUNT: TypedProp.Scalar<Document, BigDecimal?> =
            TypedProp.scalar(Document::docAmount.toImmutableProp())

    public val CURRENCY: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::currency.toImmutableProp())

    public val DOC_DATE: TypedProp.Scalar<Document, LocalDateTime?> =
            TypedProp.scalar(Document::docDate.toImmutableProp())

    public val EFFECTIVE_DATE: TypedProp.Scalar<Document, LocalDateTime?> =
            TypedProp.scalar(Document::effectiveDate.toImmutableProp())

    public val EXPIRE_DATE: TypedProp.Scalar<Document, LocalDateTime?> =
            TypedProp.scalar(Document::expireDate.toImmutableProp())

    public val DOC_DATA: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::docData.toImmutableProp())

    public val EXT_PROPS: TypedProp.Scalar<Document, String?> =
            TypedProp.scalar(Document::extProps.toImmutableProp())

    public val CARD_DATA: TypedProp.ReferenceList<Document, DocumentCardData> =
            TypedProp.referenceList(Document::cardData.toImmutableProp())

    public val HISTORIES: TypedProp.ReferenceList<Document, DocumentHistory> =
            TypedProp.referenceList(Document::histories.toImmutableProp())

    public val INDEX_DATA: TypedProp.ReferenceList<Document, DocumentIndexData> =
            TypedProp.referenceList(Document::indexData.toImmutableProp())
}
