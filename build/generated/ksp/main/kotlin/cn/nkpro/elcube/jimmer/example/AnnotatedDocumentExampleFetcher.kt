@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated::class)

package cn.nkpro.elcube.jimmer.example

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun FetcherCreator<SalesOrderAnnotated>.`by`(block: SalesOrderAnnotatedFetcherDsl.() -> Unit): Fetcher<SalesOrderAnnotated> {
    val dsl = SalesOrderAnnotatedFetcherDsl(emptySalesOrderAnnotatedFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun FetcherCreator<SalesOrderAnnotated>.`by`(base: Fetcher<SalesOrderAnnotated>?, block: SalesOrderAnnotatedFetcherDsl.() -> Unit): Fetcher<SalesOrderAnnotated> {
    val dsl = SalesOrderAnnotatedFetcherDsl(base ?: emptySalesOrderAnnotatedFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = SalesOrderAnnotated::class)
public class SalesOrderAnnotatedFetcherDsl(
    fetcher: Fetcher<SalesOrderAnnotated> = emptySalesOrderAnnotatedFetcher,
) {
    private var _fetcher: Fetcher<SalesOrderAnnotated> = fetcher

    public fun internallyGetFetcher(): Fetcher<SalesOrderAnnotated> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun orderNo(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderNo")
        } else {
            _fetcher.remove("orderNo")
        }
    }

    public fun customerName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("customerName")
        } else {
            _fetcher.remove("customerName")
        }
    }

    public fun totalAmount(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("totalAmount")
        } else {
            _fetcher.remove("totalAmount")
        }
    }

    public fun orderDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderDate")
        } else {
            _fetcher.remove("orderDate")
        }
    }

    public fun deliveryDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deliveryDate")
        } else {
            _fetcher.remove("deliveryDate")
        }
    }

    public fun status(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("status")
        } else {
            _fetcher.remove("status")
        }
    }

    public fun priority(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("priority")
        } else {
            _fetcher.remove("priority")
        }
    }

    public fun paymentStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("paymentStatus")
        } else {
            _fetcher.remove("paymentStatus")
        }
    }

    public fun invoiceType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("invoiceType")
        } else {
            _fetcher.remove("invoiceType")
        }
    }

    public fun remark(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("remark")
        } else {
            _fetcher.remove("remark")
        }
    }

    public fun orderItems(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderItems")
        } else {
            _fetcher.remove("orderItems")
        }
    }

    public fun orderItems(childFetcher: Fetcher<SalesOrderItemAnnotated>) {
        _fetcher = _fetcher.add(
            "orderItems",
            childFetcher
        )
    }

    public fun orderItems(childFetcher: Fetcher<SalesOrderItemAnnotated>, cfgBlock: (KListFieldDsl<SalesOrderItemAnnotated>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "orderItems",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun orderItems(childBlock: SalesOrderItemAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "orderItems",
            SalesOrderItemAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun orderItems(cfgBlock: (KListFieldDsl<SalesOrderItemAnnotated>.() -> Unit)?, childBlock: SalesOrderItemAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "orderItems",
            SalesOrderItemAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun orderItems(enabled: Boolean, childFetcher: Fetcher<SalesOrderItemAnnotated>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childFetcher)
        }
    }

    public fun orderItems(
        enabled: Boolean,
        childFetcher: Fetcher<SalesOrderItemAnnotated>,
        cfgBlock: (KListFieldDsl<SalesOrderItemAnnotated>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childFetcher, cfgBlock)
        }
    }

    public fun orderItems(enabled: Boolean, childBlock: SalesOrderItemAnnotatedFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childBlock)
        }
    }

    public fun orderItems(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<SalesOrderItemAnnotated>.() -> Unit)?,
        childBlock: SalesOrderItemAnnotatedFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(cfgBlock, childBlock)
        }
    }

    public fun shipments(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("shipments")
        } else {
            _fetcher.remove("shipments")
        }
    }

    public fun shipments(childFetcher: Fetcher<ShipmentAnnotated>) {
        _fetcher = _fetcher.add(
            "shipments",
            childFetcher
        )
    }

    public fun shipments(childFetcher: Fetcher<ShipmentAnnotated>, cfgBlock: (KListFieldDsl<ShipmentAnnotated>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "shipments",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun shipments(childBlock: ShipmentAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "shipments",
            ShipmentAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun shipments(cfgBlock: (KListFieldDsl<ShipmentAnnotated>.() -> Unit)?, childBlock: ShipmentAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "shipments",
            ShipmentAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun shipments(enabled: Boolean, childFetcher: Fetcher<ShipmentAnnotated>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childFetcher)
        }
    }

    public fun shipments(
        enabled: Boolean,
        childFetcher: Fetcher<ShipmentAnnotated>,
        cfgBlock: (KListFieldDsl<ShipmentAnnotated>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childFetcher, cfgBlock)
        }
    }

    public fun shipments(enabled: Boolean, childBlock: ShipmentAnnotatedFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childBlock)
        }
    }

    public fun shipments(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<ShipmentAnnotated>.() -> Unit)?,
        childBlock: ShipmentAnnotatedFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(cfgBlock, childBlock)
        }
    }

    public fun invoices(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("invoices")
        } else {
            _fetcher.remove("invoices")
        }
    }

    public fun invoices(childFetcher: Fetcher<InvoiceAnnotated>) {
        _fetcher = _fetcher.add(
            "invoices",
            childFetcher
        )
    }

    public fun invoices(childFetcher: Fetcher<InvoiceAnnotated>, cfgBlock: (KListFieldDsl<InvoiceAnnotated>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "invoices",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun invoices(childBlock: InvoiceAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "invoices",
            InvoiceAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun invoices(cfgBlock: (KListFieldDsl<InvoiceAnnotated>.() -> Unit)?, childBlock: InvoiceAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "invoices",
            InvoiceAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun invoices(enabled: Boolean, childFetcher: Fetcher<InvoiceAnnotated>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childFetcher)
        }
    }

    public fun invoices(
        enabled: Boolean,
        childFetcher: Fetcher<InvoiceAnnotated>,
        cfgBlock: (KListFieldDsl<InvoiceAnnotated>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childFetcher, cfgBlock)
        }
    }

    public fun invoices(enabled: Boolean, childBlock: InvoiceAnnotatedFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childBlock)
        }
    }

    public fun invoices(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<InvoiceAnnotated>.() -> Unit)?,
        childBlock: InvoiceAnnotatedFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(cfgBlock, childBlock)
        }
    }

    public fun payments(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("payments")
        } else {
            _fetcher.remove("payments")
        }
    }

    public fun payments(childFetcher: Fetcher<PaymentAnnotated>) {
        _fetcher = _fetcher.add(
            "payments",
            childFetcher
        )
    }

    public fun payments(childFetcher: Fetcher<PaymentAnnotated>, cfgBlock: (KListFieldDsl<PaymentAnnotated>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "payments",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun payments(childBlock: PaymentAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "payments",
            PaymentAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun payments(cfgBlock: (KListFieldDsl<PaymentAnnotated>.() -> Unit)?, childBlock: PaymentAnnotatedFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "payments",
            PaymentAnnotatedFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun payments(enabled: Boolean, childFetcher: Fetcher<PaymentAnnotated>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childFetcher)
        }
    }

    public fun payments(
        enabled: Boolean,
        childFetcher: Fetcher<PaymentAnnotated>,
        cfgBlock: (KListFieldDsl<PaymentAnnotated>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childFetcher, cfgBlock)
        }
    }

    public fun payments(enabled: Boolean, childBlock: PaymentAnnotatedFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childBlock)
        }
    }

    public fun payments(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<PaymentAnnotated>.() -> Unit)?,
        childBlock: PaymentAnnotatedFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(cfgBlock, childBlock)
        }
    }
}

private val emptySalesOrderAnnotatedFetcher: Fetcher<SalesOrderAnnotated> =
        FetcherImpl(SalesOrderAnnotated::class.java)
