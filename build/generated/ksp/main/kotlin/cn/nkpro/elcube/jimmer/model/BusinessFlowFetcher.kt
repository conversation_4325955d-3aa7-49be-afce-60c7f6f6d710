@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.BusinessFlow::class)

package cn.nkpro.elcube.jimmer.model

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = BusinessFlow::class)
public fun FetcherCreator<BusinessFlow>.`by`(block: BusinessFlowFetcherDsl.() -> Unit): Fetcher<BusinessFlow> {
    val dsl = BusinessFlowFetcherDsl(emptyBusinessFlowFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = BusinessFlow::class)
public fun FetcherCreator<BusinessFlow>.`by`(base: Fetcher<BusinessFlow>?, block: BusinessFlowFetcherDsl.() -> Unit): Fetcher<BusinessFlow> {
    val dsl = BusinessFlowFetcherDsl(base ?: emptyBusinessFlowFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = BusinessFlow::class)
public class BusinessFlowFetcherDsl(
    fetcher: Fetcher<BusinessFlow> = emptyBusinessFlowFetcher,
) {
    private var _fetcher: Fetcher<BusinessFlow> = fetcher

    public fun internallyGetFetcher(): Fetcher<BusinessFlow> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun flowCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("flowCode")
        } else {
            _fetcher.remove("flowCode")
        }
    }

    public fun flowName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("flowName")
        } else {
            _fetcher.remove("flowName")
        }
    }

    public fun flowDesc(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("flowDesc")
        } else {
            _fetcher.remove("flowDesc")
        }
    }

    public fun flowType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("flowType")
        } else {
            _fetcher.remove("flowType")
        }
    }

    public fun enabled(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("enabled")
        } else {
            _fetcher.remove("enabled")
        }
    }

    public fun systemBuiltIn(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("systemBuiltIn")
        } else {
            _fetcher.remove("systemBuiltIn")
        }
    }

    public fun flowConfig(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("flowConfig")
        } else {
            _fetcher.remove("flowConfig")
        }
    }

    public fun nodes(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("nodes")
        } else {
            _fetcher.remove("nodes")
        }
    }

    public fun nodes(childFetcher: Fetcher<BusinessFlowNode>) {
        _fetcher = _fetcher.add(
            "nodes",
            childFetcher
        )
    }

    public fun nodes(childFetcher: Fetcher<BusinessFlowNode>, cfgBlock: (KListFieldDsl<BusinessFlowNode>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "nodes",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun nodes(childBlock: BusinessFlowNodeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "nodes",
            BusinessFlowNodeFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun nodes(cfgBlock: (KListFieldDsl<BusinessFlowNode>.() -> Unit)?, childBlock: BusinessFlowNodeFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "nodes",
            BusinessFlowNodeFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun nodes(enabled: Boolean, childFetcher: Fetcher<BusinessFlowNode>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nodes")
        } else {
            nodes(childFetcher)
        }
    }

    public fun nodes(
        enabled: Boolean,
        childFetcher: Fetcher<BusinessFlowNode>,
        cfgBlock: (KListFieldDsl<BusinessFlowNode>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nodes")
        } else {
            nodes(childFetcher, cfgBlock)
        }
    }

    public fun nodes(enabled: Boolean, childBlock: BusinessFlowNodeFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nodes")
        } else {
            nodes(childBlock)
        }
    }

    public fun nodes(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<BusinessFlowNode>.() -> Unit)?,
        childBlock: BusinessFlowNodeFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("nodes")
        } else {
            nodes(cfgBlock, childBlock)
        }
    }

    public fun connections(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("connections")
        } else {
            _fetcher.remove("connections")
        }
    }

    public fun connections(childFetcher: Fetcher<BusinessFlowConnection>) {
        _fetcher = _fetcher.add(
            "connections",
            childFetcher
        )
    }

    public fun connections(childFetcher: Fetcher<BusinessFlowConnection>, cfgBlock: (KListFieldDsl<BusinessFlowConnection>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "connections",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun connections(childBlock: BusinessFlowConnectionFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "connections",
            BusinessFlowConnectionFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun connections(cfgBlock: (KListFieldDsl<BusinessFlowConnection>.() -> Unit)?, childBlock: BusinessFlowConnectionFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "connections",
            BusinessFlowConnectionFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun connections(enabled: Boolean, childFetcher: Fetcher<BusinessFlowConnection>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("connections")
        } else {
            connections(childFetcher)
        }
    }

    public fun connections(
        enabled: Boolean,
        childFetcher: Fetcher<BusinessFlowConnection>,
        cfgBlock: (KListFieldDsl<BusinessFlowConnection>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("connections")
        } else {
            connections(childFetcher, cfgBlock)
        }
    }

    public fun connections(enabled: Boolean, childBlock: BusinessFlowConnectionFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("connections")
        } else {
            connections(childBlock)
        }
    }

    public fun connections(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<BusinessFlowConnection>.() -> Unit)?,
        childBlock: BusinessFlowConnectionFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("connections")
        } else {
            connections(cfgBlock, childBlock)
        }
    }

    public fun instances(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("instances")
        } else {
            _fetcher.remove("instances")
        }
    }

    public fun instances(childFetcher: Fetcher<BusinessFlowInstance>) {
        _fetcher = _fetcher.add(
            "instances",
            childFetcher
        )
    }

    public fun instances(childFetcher: Fetcher<BusinessFlowInstance>, cfgBlock: (KListFieldDsl<BusinessFlowInstance>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "instances",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun instances(childBlock: BusinessFlowInstanceFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "instances",
            BusinessFlowInstanceFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun instances(cfgBlock: (KListFieldDsl<BusinessFlowInstance>.() -> Unit)?, childBlock: BusinessFlowInstanceFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "instances",
            BusinessFlowInstanceFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun instances(enabled: Boolean, childFetcher: Fetcher<BusinessFlowInstance>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("instances")
        } else {
            instances(childFetcher)
        }
    }

    public fun instances(
        enabled: Boolean,
        childFetcher: Fetcher<BusinessFlowInstance>,
        cfgBlock: (KListFieldDsl<BusinessFlowInstance>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("instances")
        } else {
            instances(childFetcher, cfgBlock)
        }
    }

    public fun instances(enabled: Boolean, childBlock: BusinessFlowInstanceFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("instances")
        } else {
            instances(childBlock)
        }
    }

    public fun instances(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<BusinessFlowInstance>.() -> Unit)?,
        childBlock: BusinessFlowInstanceFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("instances")
        } else {
            instances(cfgBlock, childBlock)
        }
    }
}

private val emptyBusinessFlowFetcher: Fetcher<BusinessFlow> = FetcherImpl(BusinessFlow::class.java)
