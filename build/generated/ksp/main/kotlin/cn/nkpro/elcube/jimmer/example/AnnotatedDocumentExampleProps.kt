@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderAnnotated::class)

package cn.nkpro.elcube.jimmer.example

import cn.nkpro.elcube.jimmer.enums.InvoiceType
import cn.nkpro.elcube.jimmer.enums.PaymentStatus
import cn.nkpro.elcube.jimmer.enums.Priority
import cn.nkpro.elcube.jimmer.enums.SalesOrderStatus
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<SalesOrderAnnotated>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAnnotated>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAnnotated>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAnnotated>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAnnotated>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAnnotated>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<SalesOrderAnnotated>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAnnotated>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Int>(SalesOrderAnnotatedProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<SalesOrderAnnotated>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Int>(SalesOrderAnnotatedProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<SalesOrderAnnotated>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Boolean>(SalesOrderAnnotatedProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<SalesOrderAnnotated>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Boolean>(SalesOrderAnnotatedProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<SalesOrderAnnotated>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<SalesOrderAnnotated>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Int>(SalesOrderAnnotatedProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<SalesOrderAnnotated>.orderNo: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.ORDER_NO.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAnnotated>.orderNo: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.ORDER_NO.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAnnotated>.customerName: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.CUSTOMER_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<SalesOrderAnnotated>.customerName: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.CUSTOMER_NAME.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<SalesOrderAnnotated>.totalAmount: KNonNullPropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<BigDecimal>(SalesOrderAnnotatedProps.TOTAL_AMOUNT.unwrap()) as KNonNullPropExpression<BigDecimal>

public val KNullableProps<SalesOrderAnnotated>.totalAmount: KNullablePropExpression<BigDecimal>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<BigDecimal>(SalesOrderAnnotatedProps.TOTAL_AMOUNT.unwrap()) as KNullablePropExpression<BigDecimal>

public val KNonNullProps<SalesOrderAnnotated>.orderDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.ORDER_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAnnotated>.orderDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.ORDER_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAnnotated>.deliveryDate: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.DELIVERY_DATE.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<SalesOrderAnnotated>.deliveryDate: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<LocalDateTime>(SalesOrderAnnotatedProps.DELIVERY_DATE.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<SalesOrderAnnotated>.status: KNonNullPropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<SalesOrderStatus>(SalesOrderAnnotatedProps.STATUS.unwrap()) as KNonNullPropExpression<SalesOrderStatus>

public val KNullableProps<SalesOrderAnnotated>.status: KNullablePropExpression<SalesOrderStatus>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<SalesOrderStatus>(SalesOrderAnnotatedProps.STATUS.unwrap()) as KNullablePropExpression<SalesOrderStatus>

public val KNonNullProps<SalesOrderAnnotated>.priority: KNonNullPropExpression<Priority>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Priority>(SalesOrderAnnotatedProps.PRIORITY.unwrap()) as KNonNullPropExpression<Priority>

public val KNullableProps<SalesOrderAnnotated>.priority: KNullablePropExpression<Priority>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<Priority>(SalesOrderAnnotatedProps.PRIORITY.unwrap()) as KNullablePropExpression<Priority>

public val KNonNullProps<SalesOrderAnnotated>.paymentStatus: KNonNullPropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<PaymentStatus>(SalesOrderAnnotatedProps.PAYMENT_STATUS.unwrap()) as KNonNullPropExpression<PaymentStatus>

public val KNullableProps<SalesOrderAnnotated>.paymentStatus: KNullablePropExpression<PaymentStatus>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<PaymentStatus>(SalesOrderAnnotatedProps.PAYMENT_STATUS.unwrap()) as KNullablePropExpression<PaymentStatus>

public val KProps<SalesOrderAnnotated>.invoiceType: KNullablePropExpression<InvoiceType>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<InvoiceType>(SalesOrderAnnotatedProps.INVOICE_TYPE.unwrap()) as KNullablePropExpression<InvoiceType>

public val KProps<SalesOrderAnnotated>.remark: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = get<String>(SalesOrderAnnotatedProps.REMARK.unwrap()) as KNullablePropExpression<String>

public fun KProps<SalesOrderAnnotated>.orderItems(block: KImplicitSubQueryTable<SalesOrderItemAnnotated>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAnnotatedProps.ORDER_ITEMS.unwrap(), block)

public val KTableEx<SalesOrderAnnotated>.orderItems: KNonNullTableEx<SalesOrderItemAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = join(SalesOrderAnnotatedProps.ORDER_ITEMS.unwrap())

public val KTableEx<SalesOrderAnnotated>.`orderItems?`: KNullableTableEx<SalesOrderItemAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = outerJoin(SalesOrderAnnotatedProps.ORDER_ITEMS.unwrap())

public fun KProps<SalesOrderAnnotated>.shipments(block: KImplicitSubQueryTable<ShipmentAnnotated>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAnnotatedProps.SHIPMENTS.unwrap(), block)

public val KTableEx<SalesOrderAnnotated>.shipments: KNonNullTableEx<ShipmentAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = join(SalesOrderAnnotatedProps.SHIPMENTS.unwrap())

public val KTableEx<SalesOrderAnnotated>.`shipments?`: KNullableTableEx<ShipmentAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = outerJoin(SalesOrderAnnotatedProps.SHIPMENTS.unwrap())

public fun KProps<SalesOrderAnnotated>.invoices(block: KImplicitSubQueryTable<InvoiceAnnotated>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAnnotatedProps.INVOICES.unwrap(), block)

public val KTableEx<SalesOrderAnnotated>.invoices: KNonNullTableEx<InvoiceAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = join(SalesOrderAnnotatedProps.INVOICES.unwrap())

public val KTableEx<SalesOrderAnnotated>.`invoices?`: KNullableTableEx<InvoiceAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = outerJoin(SalesOrderAnnotatedProps.INVOICES.unwrap())

public fun KProps<SalesOrderAnnotated>.payments(block: KImplicitSubQueryTable<PaymentAnnotated>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(SalesOrderAnnotatedProps.PAYMENTS.unwrap(), block)

public val KTableEx<SalesOrderAnnotated>.payments: KNonNullTableEx<PaymentAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = join(SalesOrderAnnotatedProps.PAYMENTS.unwrap())

public val KTableEx<SalesOrderAnnotated>.`payments?`: KNullableTableEx<PaymentAnnotated>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = outerJoin(SalesOrderAnnotatedProps.PAYMENTS.unwrap())

public val KRemoteRef.NonNull<SalesOrderAnnotated>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<SalesOrderAnnotated>.id: KNullablePropExpression<String>
    @GeneratedBy(type = SalesOrderAnnotated::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun KNonNullTable<SalesOrderAnnotated>.fetchBy(block: SalesOrderAnnotatedFetcherDsl.() -> Unit): Selection<SalesOrderAnnotated> = fetch(newFetcher(SalesOrderAnnotated::class).`by`(block))

@GeneratedBy(type = SalesOrderAnnotated::class)
public fun KNullableTable<SalesOrderAnnotated>.fetchBy(block: SalesOrderAnnotatedFetcherDsl.() -> Unit): Selection<SalesOrderAnnotated?> = fetch(newFetcher(SalesOrderAnnotated::class).`by`(block))

@GeneratedBy(type = SalesOrderAnnotated::class)
public object SalesOrderAnnotatedProps {
    public val ID: TypedProp.Scalar<SalesOrderAnnotated, String> =
            TypedProp.scalar(SalesOrderAnnotated::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<SalesOrderAnnotated, LocalDateTime> =
            TypedProp.scalar(SalesOrderAnnotated::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<SalesOrderAnnotated, LocalDateTime> =
            TypedProp.scalar(SalesOrderAnnotated::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<SalesOrderAnnotated, Int> =
            TypedProp.scalar(SalesOrderAnnotated::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<SalesOrderAnnotated, Boolean> =
            TypedProp.scalar(SalesOrderAnnotated::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<SalesOrderAnnotated, Int?> =
            TypedProp.scalar(SalesOrderAnnotated::sortOrder.toImmutableProp())

    public val ORDER_NO: TypedProp.Scalar<SalesOrderAnnotated, String> =
            TypedProp.scalar(SalesOrderAnnotated::orderNo.toImmutableProp())

    public val CUSTOMER_NAME: TypedProp.Scalar<SalesOrderAnnotated, String> =
            TypedProp.scalar(SalesOrderAnnotated::customerName.toImmutableProp())

    public val TOTAL_AMOUNT: TypedProp.Scalar<SalesOrderAnnotated, BigDecimal> =
            TypedProp.scalar(SalesOrderAnnotated::totalAmount.toImmutableProp())

    public val ORDER_DATE: TypedProp.Scalar<SalesOrderAnnotated, LocalDateTime> =
            TypedProp.scalar(SalesOrderAnnotated::orderDate.toImmutableProp())

    public val DELIVERY_DATE: TypedProp.Scalar<SalesOrderAnnotated, LocalDateTime> =
            TypedProp.scalar(SalesOrderAnnotated::deliveryDate.toImmutableProp())

    public val STATUS: TypedProp.Scalar<SalesOrderAnnotated, SalesOrderStatus> =
            TypedProp.scalar(SalesOrderAnnotated::status.toImmutableProp())

    public val PRIORITY: TypedProp.Scalar<SalesOrderAnnotated, Priority> =
            TypedProp.scalar(SalesOrderAnnotated::priority.toImmutableProp())

    public val PAYMENT_STATUS: TypedProp.Scalar<SalesOrderAnnotated, PaymentStatus> =
            TypedProp.scalar(SalesOrderAnnotated::paymentStatus.toImmutableProp())

    public val INVOICE_TYPE: TypedProp.Scalar<SalesOrderAnnotated, InvoiceType?> =
            TypedProp.scalar(SalesOrderAnnotated::invoiceType.toImmutableProp())

    public val REMARK: TypedProp.Scalar<SalesOrderAnnotated, String?> =
            TypedProp.scalar(SalesOrderAnnotated::remark.toImmutableProp())

    public val ORDER_ITEMS: TypedProp.ReferenceList<SalesOrderAnnotated, SalesOrderItemAnnotated> =
            TypedProp.referenceList(SalesOrderAnnotated::orderItems.toImmutableProp())

    public val SHIPMENTS: TypedProp.ReferenceList<SalesOrderAnnotated, ShipmentAnnotated> =
            TypedProp.referenceList(SalesOrderAnnotated::shipments.toImmutableProp())

    public val INVOICES: TypedProp.ReferenceList<SalesOrderAnnotated, InvoiceAnnotated> =
            TypedProp.referenceList(SalesOrderAnnotated::invoices.toImmutableProp())

    public val PAYMENTS: TypedProp.ReferenceList<SalesOrderAnnotated, PaymentAnnotated> =
            TypedProp.referenceList(SalesOrderAnnotated::payments.toImmutableProp())
}
