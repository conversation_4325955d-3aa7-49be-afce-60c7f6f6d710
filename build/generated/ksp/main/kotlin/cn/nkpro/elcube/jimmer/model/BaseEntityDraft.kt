@file:Suppress("warnings")

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import org.babyfish.jimmer.Draft
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.meta.ImmutablePropCategory
import org.babyfish.jimmer.meta.ImmutableType

@DslScope
@GeneratedBy(type = BaseEntity::class)
public interface BaseEntityDraft : BaseEntity, Draft {
    override var id: String

    override var createdTime: LocalDateTime

    override var updatedTime: LocalDateTime

    override var createdBy: String?

    override var updatedBy: String?

    override var version: Int

    override var deleted: Boolean

    @GeneratedBy(type = BaseEntity::class)
    public object `$` {
        public val type: ImmutableType = ImmutableType
                    .newBuilder(
                        "0.9.101",
                        BaseEntity::class,
                        listOf(

                        ),
                        null
                    )
                    .id(-1, "id", String::class.java)
                    .add(-1, "createdTime", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
                    .add(-1, "updatedTime", ImmutablePropCategory.SCALAR, LocalDateTime::class.java, false)
                    .add(-1, "createdBy", ImmutablePropCategory.SCALAR, String::class.java, true)
                    .add(-1, "updatedBy", ImmutablePropCategory.SCALAR, String::class.java, true)
                    .version(-1, "version")
                    .logicalDeleted(-1, "deleted", Boolean::class.java, false)
                    .build()
    }
}
