@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.model.DocType::class)

package cn.nkpro.elcube.jimmer.model

import java.time.LocalDateTime
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.toImmutableProp
import org.babyfish.jimmer.meta.TypedProp
import org.babyfish.jimmer.sql.ast.Selection
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNonNullPropExpression
import org.babyfish.jimmer.sql.kt.ast.expression.KNullablePropExpression
import org.babyfish.jimmer.sql.kt.ast.table.KImplicitSubQueryTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullProps
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KNullableProps
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTable
import org.babyfish.jimmer.sql.kt.ast.table.KNullableTableEx
import org.babyfish.jimmer.sql.kt.ast.table.KProps
import org.babyfish.jimmer.sql.kt.ast.table.KRemoteRef
import org.babyfish.jimmer.sql.kt.ast.table.KTableEx
import org.babyfish.jimmer.sql.kt.ast.table.`impl`.KRemoteRefImplementor
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher

public val KNonNullProps<DocType>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.ID.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocType>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.ID.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocType>.createdTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocType::class)
    get() = get<LocalDateTime>(DocTypeProps.CREATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocType>.createdTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocType::class)
    get() = get<LocalDateTime>(DocTypeProps.CREATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KNonNullProps<DocType>.updatedTime: KNonNullPropExpression<LocalDateTime>
    @GeneratedBy(type = DocType::class)
    get() = get<LocalDateTime>(DocTypeProps.UPDATED_TIME.unwrap()) as KNonNullPropExpression<LocalDateTime>

public val KNullableProps<DocType>.updatedTime: KNullablePropExpression<LocalDateTime>
    @GeneratedBy(type = DocType::class)
    get() = get<LocalDateTime>(DocTypeProps.UPDATED_TIME.unwrap()) as KNullablePropExpression<LocalDateTime>

public val KProps<DocType>.createdBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.CREATED_BY.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.updatedBy: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.UPDATED_BY.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocType>.version: KNonNullPropExpression<Int>
    @GeneratedBy(type = DocType::class)
    get() = get<Int>(DocTypeProps.VERSION.unwrap()) as KNonNullPropExpression<Int>

public val KNullableProps<DocType>.version: KNullablePropExpression<Int>
    @GeneratedBy(type = DocType::class)
    get() = get<Int>(DocTypeProps.VERSION.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocType>.deleted: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.DELETED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocType>.deleted: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.DELETED.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<DocType>.tenantId: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.TENANT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.orgId: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.ORG_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.deptId: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DEPT_ID.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.businessCode: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.BUSINESS_CODE.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.businessName: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.BUSINESS_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.businessStatus: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.BUSINESS_STATUS.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.sortOrder: KNullablePropExpression<Int>
    @GeneratedBy(type = DocType::class)
    get() = get<Int>(DocTypeProps.SORT_ORDER.unwrap()) as KNullablePropExpression<Int>

public val KNonNullProps<DocType>.docType: KNonNullPropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_TYPE.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocType>.docType: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_TYPE.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocType>.docName: KNonNullPropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_NAME.unwrap()) as KNonNullPropExpression<String>

public val KNullableProps<DocType>.docName: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_NAME.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.docDesc: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_DESC.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.docIcon: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_ICON.unwrap()) as KNullablePropExpression<String>

public val KProps<DocType>.docColor: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_COLOR.unwrap()) as KNullablePropExpression<String>

public val KNonNullProps<DocType>.enabled: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.ENABLED.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocType>.enabled: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.ENABLED.unwrap()) as KNullablePropExpression<Boolean>

public val KNonNullProps<DocType>.systemBuiltIn: KNonNullPropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.SYSTEM_BUILT_IN.unwrap()) as KNonNullPropExpression<Boolean>

public val KNullableProps<DocType>.systemBuiltIn: KNullablePropExpression<Boolean>
    @GeneratedBy(type = DocType::class)
    get() = get<Boolean>(DocTypeProps.SYSTEM_BUILT_IN.unwrap()) as KNullablePropExpression<Boolean>

public val KProps<DocType>.docConfig: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = get<String>(DocTypeProps.DOC_CONFIG.unwrap()) as KNullablePropExpression<String>

public fun KProps<DocType>.states(block: KImplicitSubQueryTable<DocState>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocTypeProps.STATES.unwrap(), block)

public val KTableEx<DocType>.states: KNonNullTableEx<DocState>
    @GeneratedBy(type = DocType::class)
    get() = join(DocTypeProps.STATES.unwrap())

public val KTableEx<DocType>.`states?`: KNullableTableEx<DocState>
    @GeneratedBy(type = DocType::class)
    get() = outerJoin(DocTypeProps.STATES.unwrap())

public fun KProps<DocType>.cards(block: KImplicitSubQueryTable<DocCard>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocTypeProps.CARDS.unwrap(), block)

public val KTableEx<DocType>.cards: KNonNullTableEx<DocCard>
    @GeneratedBy(type = DocType::class)
    get() = join(DocTypeProps.CARDS.unwrap())

public val KTableEx<DocType>.`cards?`: KNullableTableEx<DocCard>
    @GeneratedBy(type = DocType::class)
    get() = outerJoin(DocTypeProps.CARDS.unwrap())

public fun KProps<DocType>.indexes(block: KImplicitSubQueryTable<DocIndex>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocTypeProps.INDEXES.unwrap(), block)

public val KTableEx<DocType>.indexes: KNonNullTableEx<DocIndex>
    @GeneratedBy(type = DocType::class)
    get() = join(DocTypeProps.INDEXES.unwrap())

public val KTableEx<DocType>.`indexes?`: KNullableTableEx<DocIndex>
    @GeneratedBy(type = DocType::class)
    get() = outerJoin(DocTypeProps.INDEXES.unwrap())

public fun KProps<DocType>.documents(block: KImplicitSubQueryTable<Document>.() -> KNonNullExpression<Boolean>?): KNonNullExpression<Boolean>? = exists(DocTypeProps.DOCUMENTS.unwrap(), block)

public val KTableEx<DocType>.documents: KNonNullTableEx<Document>
    @GeneratedBy(type = DocType::class)
    get() = join(DocTypeProps.DOCUMENTS.unwrap())

public val KTableEx<DocType>.`documents?`: KNullableTableEx<Document>
    @GeneratedBy(type = DocType::class)
    get() = outerJoin(DocTypeProps.DOCUMENTS.unwrap())

public val KRemoteRef.NonNull<DocType>.id: KNonNullPropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNonNullPropExpression<String>

public val KRemoteRef.Nullable<DocType>.id: KNullablePropExpression<String>
    @GeneratedBy(type = DocType::class)
    get() = (this as KRemoteRefImplementor<*>).id<String>() as KNullablePropExpression<String>

@GeneratedBy(type = DocType::class)
public fun KNonNullTable<DocType>.fetchBy(block: DocTypeFetcherDsl.() -> Unit): Selection<DocType> = fetch(newFetcher(DocType::class).`by`(block))

@GeneratedBy(type = DocType::class)
public fun KNullableTable<DocType>.fetchBy(block: DocTypeFetcherDsl.() -> Unit): Selection<DocType?> = fetch(newFetcher(DocType::class).`by`(block))

@GeneratedBy(type = DocType::class)
public object DocTypeProps {
    public val ID: TypedProp.Scalar<DocType, String> =
            TypedProp.scalar(DocType::id.toImmutableProp())

    public val CREATED_TIME: TypedProp.Scalar<DocType, LocalDateTime> =
            TypedProp.scalar(DocType::createdTime.toImmutableProp())

    public val UPDATED_TIME: TypedProp.Scalar<DocType, LocalDateTime> =
            TypedProp.scalar(DocType::updatedTime.toImmutableProp())

    public val CREATED_BY: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::createdBy.toImmutableProp())

    public val UPDATED_BY: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::updatedBy.toImmutableProp())

    public val VERSION: TypedProp.Scalar<DocType, Int> =
            TypedProp.scalar(DocType::version.toImmutableProp())

    public val DELETED: TypedProp.Scalar<DocType, Boolean> =
            TypedProp.scalar(DocType::deleted.toImmutableProp())

    public val TENANT_ID: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::tenantId.toImmutableProp())

    public val ORG_ID: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::orgId.toImmutableProp())

    public val DEPT_ID: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::deptId.toImmutableProp())

    public val BUSINESS_CODE: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::businessCode.toImmutableProp())

    public val BUSINESS_NAME: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::businessName.toImmutableProp())

    public val BUSINESS_STATUS: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::businessStatus.toImmutableProp())

    public val SORT_ORDER: TypedProp.Scalar<DocType, Int?> =
            TypedProp.scalar(DocType::sortOrder.toImmutableProp())

    public val DOC_TYPE: TypedProp.Scalar<DocType, String> =
            TypedProp.scalar(DocType::docType.toImmutableProp())

    public val DOC_NAME: TypedProp.Scalar<DocType, String> =
            TypedProp.scalar(DocType::docName.toImmutableProp())

    public val DOC_DESC: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::docDesc.toImmutableProp())

    public val DOC_ICON: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::docIcon.toImmutableProp())

    public val DOC_COLOR: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::docColor.toImmutableProp())

    public val ENABLED: TypedProp.Scalar<DocType, Boolean> =
            TypedProp.scalar(DocType::enabled.toImmutableProp())

    public val SYSTEM_BUILT_IN: TypedProp.Scalar<DocType, Boolean> =
            TypedProp.scalar(DocType::systemBuiltIn.toImmutableProp())

    public val DOC_CONFIG: TypedProp.Scalar<DocType, String?> =
            TypedProp.scalar(DocType::docConfig.toImmutableProp())

    public val STATES: TypedProp.ReferenceList<DocType, DocState> =
            TypedProp.referenceList(DocType::states.toImmutableProp())

    public val CARDS: TypedProp.ReferenceList<DocType, DocCard> =
            TypedProp.referenceList(DocType::cards.toImmutableProp())

    public val INDEXES: TypedProp.ReferenceList<DocType, DocIndex> =
            TypedProp.referenceList(DocType::indexes.toImmutableProp())

    public val DOCUMENTS: TypedProp.ReferenceList<DocType, Document> =
            TypedProp.referenceList(DocType::documents.toImmutableProp())
}
