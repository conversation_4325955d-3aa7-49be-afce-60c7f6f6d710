@file:Suppress("warnings")
@file:GeneratedBy(type = cn.nkpro.elcube.jimmer.example.SalesOrderAuto::class)

package cn.nkpro.elcube.jimmer.example

import kotlin.Boolean
import kotlin.Suppress
import kotlin.Unit
import org.babyfish.jimmer.`internal`.GeneratedBy
import org.babyfish.jimmer.kt.DslScope
import org.babyfish.jimmer.sql.fetcher.Fetcher
import org.babyfish.jimmer.sql.fetcher.`impl`.FetcherImpl
import org.babyfish.jimmer.sql.kt.fetcher.FetcherCreator
import org.babyfish.jimmer.sql.kt.fetcher.KListFieldDsl
import org.babyfish.jimmer.sql.kt.fetcher.`impl`.JavaFieldConfigUtils

@GeneratedBy(type = SalesOrderAuto::class)
public fun FetcherCreator<SalesOrderAuto>.`by`(block: SalesOrderAutoFetcherDsl.() -> Unit): Fetcher<SalesOrderAuto> {
    val dsl = SalesOrderAutoFetcherDsl(emptySalesOrderAutoFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@GeneratedBy(type = SalesOrderAuto::class)
public fun FetcherCreator<SalesOrderAuto>.`by`(base: Fetcher<SalesOrderAuto>?, block: SalesOrderAutoFetcherDsl.() -> Unit): Fetcher<SalesOrderAuto> {
    val dsl = SalesOrderAutoFetcherDsl(base ?: emptySalesOrderAutoFetcher)
    dsl.block()
    return dsl.internallyGetFetcher()
}

@DslScope
@GeneratedBy(type = SalesOrderAuto::class)
public class SalesOrderAutoFetcherDsl(
    fetcher: Fetcher<SalesOrderAuto> = emptySalesOrderAutoFetcher,
) {
    private var _fetcher: Fetcher<SalesOrderAuto> = fetcher

    public fun internallyGetFetcher(): Fetcher<SalesOrderAuto> = _fetcher

    public fun allScalarFields() {
        _fetcher = _fetcher.allScalarFields()
    }

    public fun allTableFields() {
        _fetcher = _fetcher.allTableFields()
    }

    public fun createdTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdTime")
        } else {
            _fetcher.remove("createdTime")
        }
    }

    public fun updatedTime(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedTime")
        } else {
            _fetcher.remove("updatedTime")
        }
    }

    public fun createdBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("createdBy")
        } else {
            _fetcher.remove("createdBy")
        }
    }

    public fun updatedBy(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("updatedBy")
        } else {
            _fetcher.remove("updatedBy")
        }
    }

    public fun version(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("version")
        } else {
            _fetcher.remove("version")
        }
    }

    public fun deleted(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deleted")
        } else {
            _fetcher.remove("deleted")
        }
    }

    public fun tenantId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("tenantId")
        } else {
            _fetcher.remove("tenantId")
        }
    }

    public fun orgId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orgId")
        } else {
            _fetcher.remove("orgId")
        }
    }

    public fun deptId(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deptId")
        } else {
            _fetcher.remove("deptId")
        }
    }

    public fun businessCode(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessCode")
        } else {
            _fetcher.remove("businessCode")
        }
    }

    public fun businessName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessName")
        } else {
            _fetcher.remove("businessName")
        }
    }

    public fun businessStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("businessStatus")
        } else {
            _fetcher.remove("businessStatus")
        }
    }

    public fun sortOrder(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("sortOrder")
        } else {
            _fetcher.remove("sortOrder")
        }
    }

    public fun orderNo(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderNo")
        } else {
            _fetcher.remove("orderNo")
        }
    }

    public fun customerName(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("customerName")
        } else {
            _fetcher.remove("customerName")
        }
    }

    public fun totalAmount(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("totalAmount")
        } else {
            _fetcher.remove("totalAmount")
        }
    }

    public fun orderDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderDate")
        } else {
            _fetcher.remove("orderDate")
        }
    }

    public fun deliveryDate(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("deliveryDate")
        } else {
            _fetcher.remove("deliveryDate")
        }
    }

    public fun status(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("status")
        } else {
            _fetcher.remove("status")
        }
    }

    public fun priority(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("priority")
        } else {
            _fetcher.remove("priority")
        }
    }

    public fun paymentStatus(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("paymentStatus")
        } else {
            _fetcher.remove("paymentStatus")
        }
    }

    public fun invoiceType(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("invoiceType")
        } else {
            _fetcher.remove("invoiceType")
        }
    }

    public fun remark(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("remark")
        } else {
            _fetcher.remove("remark")
        }
    }

    public fun orderItems(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("orderItems")
        } else {
            _fetcher.remove("orderItems")
        }
    }

    public fun orderItems(childFetcher: Fetcher<SalesOrderItemAuto>) {
        _fetcher = _fetcher.add(
            "orderItems",
            childFetcher
        )
    }

    public fun orderItems(childFetcher: Fetcher<SalesOrderItemAuto>, cfgBlock: (KListFieldDsl<SalesOrderItemAuto>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "orderItems",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun orderItems(childBlock: SalesOrderItemAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "orderItems",
            SalesOrderItemAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun orderItems(cfgBlock: (KListFieldDsl<SalesOrderItemAuto>.() -> Unit)?, childBlock: SalesOrderItemAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "orderItems",
            SalesOrderItemAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun orderItems(enabled: Boolean, childFetcher: Fetcher<SalesOrderItemAuto>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childFetcher)
        }
    }

    public fun orderItems(
        enabled: Boolean,
        childFetcher: Fetcher<SalesOrderItemAuto>,
        cfgBlock: (KListFieldDsl<SalesOrderItemAuto>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childFetcher, cfgBlock)
        }
    }

    public fun orderItems(enabled: Boolean, childBlock: SalesOrderItemAutoFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(childBlock)
        }
    }

    public fun orderItems(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<SalesOrderItemAuto>.() -> Unit)?,
        childBlock: SalesOrderItemAutoFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("orderItems")
        } else {
            orderItems(cfgBlock, childBlock)
        }
    }

    public fun shipments(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("shipments")
        } else {
            _fetcher.remove("shipments")
        }
    }

    public fun shipments(childFetcher: Fetcher<ShipmentAuto>) {
        _fetcher = _fetcher.add(
            "shipments",
            childFetcher
        )
    }

    public fun shipments(childFetcher: Fetcher<ShipmentAuto>, cfgBlock: (KListFieldDsl<ShipmentAuto>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "shipments",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun shipments(childBlock: ShipmentAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "shipments",
            ShipmentAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun shipments(cfgBlock: (KListFieldDsl<ShipmentAuto>.() -> Unit)?, childBlock: ShipmentAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "shipments",
            ShipmentAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun shipments(enabled: Boolean, childFetcher: Fetcher<ShipmentAuto>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childFetcher)
        }
    }

    public fun shipments(
        enabled: Boolean,
        childFetcher: Fetcher<ShipmentAuto>,
        cfgBlock: (KListFieldDsl<ShipmentAuto>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childFetcher, cfgBlock)
        }
    }

    public fun shipments(enabled: Boolean, childBlock: ShipmentAutoFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(childBlock)
        }
    }

    public fun shipments(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<ShipmentAuto>.() -> Unit)?,
        childBlock: ShipmentAutoFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("shipments")
        } else {
            shipments(cfgBlock, childBlock)
        }
    }

    public fun invoices(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("invoices")
        } else {
            _fetcher.remove("invoices")
        }
    }

    public fun invoices(childFetcher: Fetcher<InvoiceAuto>) {
        _fetcher = _fetcher.add(
            "invoices",
            childFetcher
        )
    }

    public fun invoices(childFetcher: Fetcher<InvoiceAuto>, cfgBlock: (KListFieldDsl<InvoiceAuto>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "invoices",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun invoices(childBlock: InvoiceAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "invoices",
            InvoiceAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun invoices(cfgBlock: (KListFieldDsl<InvoiceAuto>.() -> Unit)?, childBlock: InvoiceAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "invoices",
            InvoiceAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun invoices(enabled: Boolean, childFetcher: Fetcher<InvoiceAuto>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childFetcher)
        }
    }

    public fun invoices(
        enabled: Boolean,
        childFetcher: Fetcher<InvoiceAuto>,
        cfgBlock: (KListFieldDsl<InvoiceAuto>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childFetcher, cfgBlock)
        }
    }

    public fun invoices(enabled: Boolean, childBlock: InvoiceAutoFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(childBlock)
        }
    }

    public fun invoices(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<InvoiceAuto>.() -> Unit)?,
        childBlock: InvoiceAutoFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("invoices")
        } else {
            invoices(cfgBlock, childBlock)
        }
    }

    public fun payments(enabled: Boolean = true) {
        _fetcher = if (enabled) {
            _fetcher.add("payments")
        } else {
            _fetcher.remove("payments")
        }
    }

    public fun payments(childFetcher: Fetcher<PaymentAuto>) {
        _fetcher = _fetcher.add(
            "payments",
            childFetcher
        )
    }

    public fun payments(childFetcher: Fetcher<PaymentAuto>, cfgBlock: (KListFieldDsl<PaymentAuto>.() -> Unit)?) {
        _fetcher = _fetcher.add(
            "payments",
            childFetcher,
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun payments(childBlock: PaymentAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "payments",
            PaymentAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher()
        )
    }

    public fun payments(cfgBlock: (KListFieldDsl<PaymentAuto>.() -> Unit)?, childBlock: PaymentAutoFetcherDsl.() -> Unit) {
        _fetcher = _fetcher.add(
            "payments",
            PaymentAutoFetcherDsl().apply { childBlock() }.internallyGetFetcher(),
            JavaFieldConfigUtils.list(cfgBlock)
        )
    }

    public fun payments(enabled: Boolean, childFetcher: Fetcher<PaymentAuto>) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childFetcher)
        }
    }

    public fun payments(
        enabled: Boolean,
        childFetcher: Fetcher<PaymentAuto>,
        cfgBlock: (KListFieldDsl<PaymentAuto>.() -> Unit)?,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childFetcher, cfgBlock)
        }
    }

    public fun payments(enabled: Boolean, childBlock: PaymentAutoFetcherDsl.() -> Unit) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(childBlock)
        }
    }

    public fun payments(
        enabled: Boolean,
        cfgBlock: (KListFieldDsl<PaymentAuto>.() -> Unit)?,
        childBlock: PaymentAutoFetcherDsl.() -> Unit,
    ) {
        if (!enabled) {
            _fetcher = _fetcher.remove("payments")
        } else {
            payments(cfgBlock, childBlock)
        }
    }
}

private val emptySalesOrderAutoFetcher: Fetcher<SalesOrderAuto> =
        FetcherImpl(SalesOrderAuto::class.java)
